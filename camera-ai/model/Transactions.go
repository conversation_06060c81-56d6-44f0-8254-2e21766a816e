package model

import (
	"design-ai/enums"
	"design-ai/utils/logger"
	"design-ai/utils/tools"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"strings"
)

type transactions_ struct {
}

func (transactions *transactions_) ManageAddCoin(balance *CoinBalance) error {
	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if err := balance.New(tx); err != nil {
			return err
		}
		return nil
	})
	return nil
}

func (transactions *transactions_) InviteNewUserAddCoin(balance *CoinBalance, shareResult *ShareResult) error {
	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		var invitedUser User                       //新注册的用户
		invitedUserId := shareResult.VisitedUserId //新注册的用户ID
		invitedCode := shareResult.InvitationCode
		userID := shareResult.UserId //发起邀请的用户ID
		if err := db.First(&invitedUser, invitedUserId).Error; err != nil || invitedUser.ID == 0 {
			logger.Error(" orderNo:", balance.OrderNo, " balance.OccurredAmount:", balance.OccurredAmount, " invitedUserID:", invitedUser.ID)
			return err
		}

		if invitedUser.InvitedUserId > 0 || len(invitedUser.InvitedCode) == 6 {
			logger.Error("用户已经被邀请过，invitedUserId：", invitedUserId, "  InvitedCode:", invitedUser.InvitedCode)
			return errors.New("该用户已被邀请过")
		}

		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}

		shareResult.OrderNo = balance.OrderNo
		if err := shareResult.New(tx); err != nil {
			logger.Error(err)
			return err
		}

		if err := tx.Debug().Model(&invitedUser).Omit("version").Updates(User{InvitedCode: invitedCode, InvitedUserId: userID}).Error; err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (transactions *transactions_) UpscaleCost(item OutImg) error {
	needCoin := 2
	var user User
	if err := user.GetById(item.UserId); err != nil {
		logger.Error(err)
		return err
	}
	if user.Coin < needCoin {
		logger.Error("星星不足", item.UserId)
		return errors.New("星星不足")
	}

	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')

		var balance CoinBalance

		orderNo, err := OrderNo.NewByOrderType(enums.OrderTypeEnum.Cost, 0)
		if err != nil {
			logger.Error(err)
			return err
		}
		if err := balance.GetBalanceObject(orderNo, item.UserId, enums.OrderTypeEnum.Cost, 0-needCoin, "精修消费", fmt.Sprintf("OutImgId:%d", item.ID), item.UserId, "用户ID"); err != nil {
			logger.Error(err)
			return err
		}
		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (transactions *transactions_) DownloadCost(item OutImg) error {
	needCoin := 2
	var user User
	if err := user.GetById(item.UserId); err != nil {
		logger.Error(err)
		return err
	}
	if user.Coin < needCoin {
		logger.Error("星星不足", item.UserId)
		return errors.New("星星不足")
	}

	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')

		var balance CoinBalance
		if item.OrderNo != "" {
			logger.Error("扣费已设置 item.ID:", item.ID)
			return errors.New("已扣费")
		}

		orderNo, err := OrderNo.NewByOrderType(enums.OrderTypeEnum.Cost, 0)
		if err != nil {
			logger.Error(err)
			return err
		}
		item.OrderNo = orderNo
		if err := balance.GetBalanceObject(orderNo, item.UserId, enums.OrderTypeEnum.Cost, 0-needCoin, "下载消费", fmt.Sprintf("OutImgId:%d", item.ID), item.UserId, "用户ID"); err != nil {
			logger.Error(err)
			return err
		}
		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}
		if item.OrderNo == "" {
			err := errors.New("未设置OrderNo")
			logger.Error(err)
			return err
		}
		if err := item.SetOrderNo(tx, orderNo); err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (transactions *transactions_) SubscriptionAddCoin(receipt *Receipt) error {
	var product RechargeProduct
	if err := product.GetByID(receipt.ProductId); err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {

		balance := CoinBalance{
			UserId:         receipt.UserId,
			OperatorId:     receipt.UserId,
			OccurredAmount: product.Coin,
		}

		outTradeNo := ""
		if product.ProductCategory == enums.ProductCategoryEnum.Subscription {
			outTradeNo, _ = OrderNo.NewByOrderType(enums.OrderTypeEnum.Subscribe, 0)
		} else {
			//outTradeNo, _ = OrderNo.NewByOrderType(enums.OrderTypeEnum.RechargeBuy, 0)
		}

		if err := balance.GetBalanceObject(outTradeNo, receipt.UserId, enums.OrderTypeEnum.Subscribe, product.Coin, "订阅购买", fmt.Sprintf("订阅金额%s", product.Price), receipt.UserId, "User"); err != nil {
			logger.Error(err)
			return err
		}
		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}

		var receipt Receipt
		if err := receipt.GetByTransactionId(receipt.TransactionId); err != nil {
			logger.Error(err)
			return err
		}
		if receipt.OrderNo != "" {
			err := errors.New("该订单已经处理过")
			logger.Error(err, " recharge.PayTradeId:", receipt.TransactionId)
			return err
		}
		if err := receipt.SetOrderNo(tx, outTradeNo); err != nil {
			logger.Error(err)
			return err
		}
		//
		////根据订阅产品设置会员类型
		//memberType := 0
		//expiresDate := time.Time{}
		//if receipt.ProductCode == "roodesign_sub_01" {
		//	expiresDate = receipt.ExpiresDate
		//	memberType = enums.MemberTypeEnum.Primary
		//} else if receipt.ProductCode == "roodesign_sub_01" {
		//	expiresDate = receipt.ExpiresDate
		//	memberType = enums.MemberTypeEnum.Advanced
		//}
		//
		//if memberType == 0 {
		//	err := errors.New("为找到会员类型")
		//	logger.Error(err)
		//	return err
		//}
		//
		//var user User
		//if err := user.GetByID(receipt.UserId); err != nil {
		//	return err
		//}
		//if expiresDate.After(user.MemberExpires) {
		//	if err := user.SetMemberExpires(tx, memberType, expiresDate); err != nil {
		//		logger.Error(err)
		//		return err
		//	}
		//} else {
		//	err := errors.New("会员过期时间不正确")
		//	logger.Error(err, user.ID, " ", expiresDate)
		//	return err
		//}
		return nil
	})

}

func (transactions *transactions_) ReceiptSuccess(outTradeNo string, transactionIdentifier string, receipt *Receipt) error {
	if receipt.OrderNo != "" {
		err := errors.New("该订单已经处理过")
		logger.Error(err, " recharge.PayTradeId:", receipt.TransactionId)
		return err
	}
	var product RechargeProduct
	if err := product.GetByProductCode(receipt.ProductCode); err != nil {
		return err
	}
	return db.Transaction(func(tx *gorm.DB) error {

		balance := CoinBalance{
			UserId:         receipt.UserId,
			OperatorId:     receipt.UserId,
			OccurredAmount: product.Coin,
		}

		orderNo := ""
		if receipt.TransactionId == transactionIdentifier && transactionIdentifier != "" {
			orderNo = outTradeNo
			var recharge Recharge
			if err := recharge.GetByOutTradeNo(outTradeNo); err != nil {
				logger.Error(err)
				return err
			}
			if recharge.State != 0 {
				logger.Error(recharge.ID, "state:", recharge.State)
				return errors.New("状态不正确")
			}
			if product.ProductCategory == enums.ProductCategoryEnum.Subscription {
				recharge.ExpiresDate = receipt.ExpiresDate
			}
			if err := recharge.SetPaySuccess(tx, receipt.TransactionId, receipt.PurchaseDate, ""); err != nil {
				logger.Error(err)
				return err
			}
		} else {
			if product.ProductCategory == enums.ProductCategoryEnum.Subscription {
				orderNo, _ = OrderNo.NewByOrderType(enums.OrderTypeEnum.Subscribe, 0)
			} else if product.ProductCategory == enums.ProductCategoryEnum.Consumable {
				outTradeNo, _ = OrderNo.NewByOrderType(enums.OrderTypeEnum.RechargeBuy, 0)
			}
		}

		show := "充值购买"
		orderType := enums.OrderTypeEnum.RechargeBuy
		if product.ProductCategory == enums.ProductCategoryEnum.Subscription {
			show = "订阅购买"
			orderType = enums.OrderTypeEnum.Subscribe
		}
		if err := balance.GetBalanceObject(orderNo, receipt.UserId, orderType, product.Coin, show, fmt.Sprintf("金额%s", product.Price), receipt.UserId, "User"); err != nil {
			logger.Error(err)
			return err
		}
		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}

		//var receipt Receipt
		//if err := receipt.GetByTransactionId(receipt.TransactionId); err != nil {
		//	logger.Error(err)
		//	return err
		//}

		if err := receipt.SetOrderNo(tx, orderNo); err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})

}

func (transactions *transactions_) RechargeSuccess(recharge *Recharge) error {
	logger.Info("支付成功事务逻辑开始", recharge.OutTradeNo)
	if recharge.State != 0 {
		logger.Error(recharge.ID, "state:", recharge.State)
		return errors.New("状态不正确")
	}

	var product RechargeProduct
	if err := product.GetByID(recharge.ProductId); err != nil {
		logger.Error(err)
		return err
	}

	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')

		balance := CoinBalance{
			UserId:         recharge.UserId,
			OperatorId:     recharge.UserId,
			OccurredAmount: recharge.Coin,
		}
		if recharge.PayChannel == enums.PayChannelEnum.AppleIap {
			err := errors.New("apple 支付逻辑不在这里")
			logger.Error(err)
			return err
		} else if recharge.PayChannel == enums.PayChannelEnum.AliPay {
			if err := recharge.SetPaySuccess(tx, recharge.PayTradeId, recharge.PayTime, recharge.PayCallbackJson); err != nil {
				logger.Error(err)
				return err
			}
		} else if recharge.PayChannel == enums.PayChannelEnum.WechatPay {
			if err := recharge.SetPaySuccess(tx, recharge.PayTradeId, recharge.PayTime, recharge.PayCallbackJson); err != nil {
				logger.Error(err)
				return err
			}
		} else {
			err := errors.New("未写业务逻辑")
			logger.Error(err)
			return err
		}

		if product.ProductCategory == enums.ProductCategoryEnum.Subscription {
			if err := balance.GetBalanceObject(recharge.OutTradeNo, recharge.UserId, enums.OrderTypeEnum.Subscribe, recharge.Coin, "订阅购买", fmt.Sprintf("订阅金额%s", recharge.Amount), recharge.UserId, "User"); err != nil {
				logger.Error(err)
				return err
			}
			if err := balance.New(tx); err != nil {
				logger.Error(err)
				return err
			}

			//根据订阅产品设置会员类型
			memberType := 0

			if strings.Contains(product.ShowTitle, "普通") || strings.Contains(product.ShowTitle, "测试") {
				memberType = 1
			} else if strings.Contains(product.ShowTitle, "高级") {
				memberType = 2
			} else {
				logger.Error("未获取到订阅的会员类型", product)
				return errors.New("未获取到订阅的会员类型")
			}

			expiresDate := recharge.ExpiresDate
			if expiresDate.Equal(recharge.PayTime) || expiresDate.Before(recharge.PayTime) {
				logger.Error("过期时间不正确", product, expiresDate, product.SubDuration, recharge.PayTime)
				return errors.New("过期时间不正确")
			}
			var user User
			if err := user.GetById(recharge.UserId); err != nil {
				logger.Error(err)
				return err
			}

			if err := user.SetMemberExpires(tx, memberType, expiresDate); err != nil {
				logger.Error(err)
				return err
			}
		} else {
			logger.Info("开始创建流水 充值类型 ", product.ProductCategory, "   ", recharge.OutTradeNo)
			if err := balance.GetBalanceObject(recharge.OutTradeNo, recharge.UserId, enums.OrderTypeEnum.RechargeBuy, recharge.Coin, "购买数字人", fmt.Sprintf("充值金额%s", recharge.Amount), recharge.UserId, "User"); err != nil {
				logger.Error(err)
				return err
			}
			if err := balance.New(tx); err != nil {
				logger.Error(err)
				return err
			}
			var user User
			if err := user.GetById(recharge.UserId); err != nil {
				logger.Error(err)
				return err
			}

			if len(recharge.CustomParam) > 10 {
				logger.Info("开始处理CustomParam ", recharge.OutTradeNo)
				m := tools.GetMapFromJson(recharge.CustomParam)
				action := 0
				if v, ok := m["action"]; ok {
					action = int(v.(float64))
				}
				if v, ok := m["face_img_md5"]; ok && action == enums.RechargeActionEnum.BuyDigital {
					faceImgMd5 := v.(string)
					var uploadImg UploadImg
					if err := uploadImg.GetByMd5(faceImgMd5); err != nil {
						logger.Error(err)
						return err
					}

					uuid := uuid.New()
					uuidStr := strings.Replace(uuid.String(), "-", "", -1)
					digital := Digital{
						Uuid:        uuidStr,
						UserId:      recharge.UserId,
						Title:       "",
						TrainType:   1,
						TrainFree:   1,
						FaceImgMd5:  uploadImg.Md5,
						FaceImgPath: uploadImg.Path,
						OrderNo:     recharge.OutTradeNo,
						State:       1,
					}
					if err := digital.New(tx); err != nil {
						logger.Error(err)
						return err
					}
					if err := user.SetDigital(tx, digital.ID, digital.Uuid); err != nil {
						logger.Error(err)
						return err
					}
				} else {
					logger.Error("face_img_md5 字段不存在", recharge.CustomParam)
				}
			}
		}
		return nil
	})
	return nil
}

func (transactions *transactions_) FacefakeCostCoin(facefake *Facefake) error {

	var user User
	if err := db.First(&user, facefake.UserId).Error; err != nil {
		logger.Error(err)
		return err
	}

	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if facefake.PriceCoin > 0 {
			if user.Coin < facefake.PriceCoin {
				return errors.New("剩余星星不足")
			}

			var balance CoinBalance

			orderNo, err := OrderNo.NewByOrderType(enums.OrderTypeEnum.Cost, 0)
			if err != nil {
				logger.Error(err)
				return err
			}
			facefake.OrderNo = orderNo
			if err := facefake.New(tx); err != nil {
				logger.Error(err)
				return err
			}
			if err := balance.GetBalanceObject(orderNo, facefake.UserId, enums.OrderTypeEnum.Cost, 0-facefake.PriceCoin, "制作消费", fmt.Sprintf("facefakeId:%d", facefake.ID), facefake.UserId, "用户ID"); err != nil {
				logger.Error(err)
				return err
			}
			if err := balance.New(tx); err != nil {
				logger.Error(err)
				return err
			}
			if err := tx.Model(facefake).Updates(Facefake{OrderNo: orderNo}).Error; err != nil {
				return err
			}
		} else {
			if err := facefake.Save(); err != nil {
				logger.Error(err)
				return err
			}
		}
		return nil
	})
}

var Transactions transactions_

package model

import (
	"design-ai/enums"
	"gorm.io/gorm"
)

type ArtStyle struct {
	gorm.Model
	Title      string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	ArtType    int    `json:"art_type" gorm:"type:tinyint;not null;default:0;comment:1证件照 2艺术照 3社交头像"`
	Sex        int    `json:"sex" gorm:"type:tinyint;not null;default:0;comment:1男 2女"`
	AgeSect    string `json:"age_sect" gorm:"type:varchar(50);not null;default:'';comment:年龄段"`
	Dress      string `json:"dress" gorm:"type:varchar(50);not null;default:'';comment:服装"`
	PhotoBg    string `json:"photo_bg" gorm:"type:varchar(50);not null;default:'';comment:照片底色"`
	PhotoSize  string `json:"photo_size" gorm:"type:varchar(50);not null;default:'';comment:照片尺寸"`
	StyleImgs  string `json:"style_imgs" gorm:"type:json;comment:风格图片"`
	Remark     string `json:"remark" gorm:"type:varchar(500);not null;default:'';comment:后台备注"`
	OrderIndex int    `json:"order_index" gorm:"type:int;not null;default:0;comment:序号(越大越前面)"`
	PriceCoin  int    `json:"price_coin" gorm:"type:int;not null;default:0;comment:所需Coin"`
	IsHot      int    `json:"is_hot" gorm:"type:int;not null;default:0;comment:热门推荐"`
	IsDefault  int    `json:"is_default" gorm:"type:int;not null;default:0;comment:1默认"`
	State      int    `json:"state" gorm:"type:int;not null;default:0;comment:状态(1可用)"`
}

type ArtStyleTest struct {
	gorm.Model
	Title      string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	ArtType    int    `json:"art_type" gorm:"type:tinyint;not null;default:0;comment:1证件照 2艺术照 3社交头像"`
	Sex        int    `json:"sex" gorm:"type:tinyint;not null;default:0;comment:1男 2女"`
	AgeSect    string `json:"age_sect" gorm:"type:varchar(50);not null;default:'';comment:年龄段"`
	Dress      string `json:"dress" gorm:"type:varchar(50);not null;default:'';comment:服装"`
	PhotoBg    string `json:"photo_bg" gorm:"type:varchar(50);not null;default:'';comment:照片底色"`
	PhotoSize  string `json:"photo_size" gorm:"type:varchar(50);not null;default:'';comment:照片尺寸"`
	StyleImgs  string `json:"style_imgs" gorm:"type:json;comment:风格图片"`
	Remark     string `json:"remark" gorm:"type:varchar(500);not null;default:'';comment:后台备注"`
	OrderIndex int    `json:"order_index" gorm:"type:int;not null;default:0;comment:序号(越大越前面)"`
	PriceCoin  int    `json:"price_coin" gorm:"type:int;not null;default:0;comment:所需Coin"`
	IsHot      int    `json:"is_hot" gorm:"type:int;not null;default:0;comment:热门推荐"`
	IsDefault  int    `json:"is_default" gorm:"type:int;not null;default:0;comment:1默认"`
	State      int    `json:"state" gorm:"type:int;not null;default:0;comment:状态(1可用)"`
}

func (ArtStyle) TableName() string {
	return "T_ArtStyle"
}

func (o *ArtStyle) GetById(id uint) error {
	return db.First(o, id).Error
}

func (o *ArtStyle) GetByDress(artType int, sex int, ageSect string, dress string) error {
	return db.First(o, "art_type=? and sex=? and age_sect=? and dress=?", artType, sex, ageSect, dress).Error
}

func (o *ArtStyle) GetPassport(artType int, sex int, ageSect string, dress string, orderIndex int, photoBg string, photoSize string) error {
	if orderIndex >= 0 {
		return db.Debug().First(o, "art_type=? and sex=? and age_sect=? and dress=? and order_index=? and photo_bg=? and photo_size=?",
			artType, sex, ageSect, dress, orderIndex, photoBg, photoSize).Error
	} else {
		return db.Debug().First(o, "art_type=? and sex=? and age_sect=? and dress=? and photo_bg=? and photo_size=?",
			artType, sex, ageSect, dress, photoBg, photoSize).Error
	}

}

func (o *ArtStyle) GetList(dest interface{}, id uint, artType int, sex int, ageSect string, dress string, photoGb string, state int, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	} else {
		if artType > 0 {
			tx.Where("art_type=?", artType)
		}
		if sex > 0 {
			tx.Where("sex=?", sex)
		}

		if artType == enums.ArtTypeEnum.Passport {
			if ageSect != " " {
				tx.Where("age_sect=?", ageSect)
			}
			if dress != " " {
				tx.Where("dress=?", dress)
			}
			if photoGb != " " {
				tx.Where("photo_bg=?", photoGb)
			}
		}

		if state >= 0 {
			tx.Where("state=?", state)
		}
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("order_index desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *ArtStyle) Save() error {
	return db.Save(o).Error
}

func (o *ArtStyle) SetState(state int) error {
	return db.Model(o).Updates(map[string]interface{}{"state": state}).Error
}

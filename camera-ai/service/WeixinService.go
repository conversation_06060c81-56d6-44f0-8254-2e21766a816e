package service

import (
	"design-ai/utils/config"
	"design-ai/utils/logger"
	"design-ai/utils/myhttp"
	"design-ai/utils/tools"
	"encoding/json"
	"errors"
	"github.com/go-pay/gopay/wechat/v3"
	"net/url"
	"strings"
	"unsafe"
)

type weixinService_ struct {
	client                     *wechat.ClientV3
	appId                      string
	mchId                      string
	mchCertificateSerialNumber string
	mchAPIv3Key                string
	privateKeyContent          string
}
type Code2SessionResp struct {
	SessionKey string `json:"session_key"`
	Unionid    string `json:"unionid"`
	ErrMsg     string `json:"errmsg"`
	Openid     string `json:"openid"`
	ErrCode    int    `json:"errcode"`
}
type PhoneInfoResp struct {
	PhoneNumber     string `json:"phoneNumber"`
	PurePhoneNumber string `json:"purePhoneNumber"`
	CountryCode     string `json:"countryCode"`
}

type PhoneNumberResp struct {
	ErrCode   int           `json:"errcode"`
	ErrMsg    string        `json:"errmsg"`
	PhoneInfo PhoneInfoResp `json:"phone_info"`
}

type MsgSecCheckResp struct {
	ErrCode int                      `json:"errcode"`
	ErrMsg  string                   `json:"errmsg"`
	Result  map[string]interface{}   `json:"result"`
	Detail  []map[string]interface{} `json:"detail"`
	TraceId string                   `json:"trace_id"`
}
type ImgSecCheckResp struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

const CheckOpenId = "oHlKy5DAZjbEeBCP28XwwGcEprsk"
const OutOfLineWords = "赤裸/裸体/乳房/阴道/色情/阴茎"

func (o *weixinService_) MsgSecCheckResult(content string, openId string) (string, error) {
	msg := ""
	if content == "" {
		return "", nil
	}

	arySexKey := strings.Split(OutOfLineWords, "/")
	for i := 0; i < len(arySexKey); i++ {
		if strings.Contains(content, arySexKey[i]) {
			msg = "描述文字涉及[" + "违规" + "]内容，请修改后再提交"
			return msg, nil
		}
	}

	checkOpenId := openId
	if checkOpenId == "" {
		checkOpenId = CheckOpenId
	}
	resp, _, err := o.MsgSecCheck(content, checkOpenId)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	pass := false
	if resp.ErrCode == 0 {
		label := int64(resp.Result["label"].(float64))
		if label == 100 {
			pass = true
		} else {
			txt := o.GetMsgSecCheckLabelTxt(label)
			if len(txt) == 0 {
				msg = "画面描述有违规文字，请修改后再提交"
			} else {
				msg = "画面描述文字涉及[" + txt + "]问题，请修改后再提交"
			}
			return msg, nil
		}
	} else {
		logger.Error("内容安全识别失败,请重试", resp, content)
		return "", errors.New("内容安全识别失败")
	}
	if pass {
		logger.Info("文本内容安全识别通过：", content)
		return "", nil
	} else {
		logger.Error("文本内容安全识别不通过", content)
		return "", errors.New("文本内容安全识别不通过")
	}
}

func (o *weixinService_) MsgSecCheck(content string, openid string) (MsgSecCheckResp, string, error) { //微信文本内容安全识别
	//https://api.weixin.qq.com/wxa/msg_sec_check?access_token=ACCESS_TOKEN

	urlStr := "https://api.weixin.qq.com/wxa/msg_sec_check?access_token=" + AccessTokenUpdate.GetAccessToken()
	//if config.AppMode == "debug" {
	//	urlStr = "https://api.weixin.qq.com/wxa/msg_sec_check?access_token=" + "64_IZgywQFsRo12V5Ae3CPubtoryk4uTOU3e0k-iQTbYYsRu8fP0-coOdIziqf9-OniXKS74pU8GPPAhR4cdrTm21TNQo_wkygrQYPQtQGDNuwlOT2x9NZuj08zlNMRCFhAFAALO"
	//
	//}
	params := make(map[string]interface{})
	params["content"] = content
	params["version"] = 2
	params["scene"] = 1
	params["openid"] = openid

	bs, err := myhttp.PostByte(urlStr, params)
	if err != nil {
		logger.Error("GetPhoneNumber 请求失败", err)
		return MsgSecCheckResp{}, "", err
	}
	var resp MsgSecCheckResp
	err = json.Unmarshal(bs, &resp)
	if err != nil {
		logger.Error(err)
		return MsgSecCheckResp{}, "", err
	}
	str := (*string)(unsafe.Pointer(&bs))
	return resp, *str, err
	//{"errcode":0,"errmsg":"ok","detail":[{"strategy":"content_model","errcode":0,"suggest":"pass","label":100,"prob":90},{"strategy":"keyword","errcode":0}],"trace_id":"63abe1a6-08453a65-4eb28ef3","result":{"suggest":"pass","label":100}}
}

func (o *weixinService_) ImgSecCheck(file string) (ImgSecCheckResp, string, error) { //微信图片内容安全识别 https://developers.weixin.qq.com/miniprogram/dev/framework/security.imgSecCheck.html#method-cloud

	urlStr := "https://api.weixin.qq.com/wxa/img_sec_check?access_token=" + AccessTokenUpdate.GetAccessToken()
	//if config.AppMode == "debug" {
	//	urlStr = "https://api.weixin.qq.com/wxa/msg_sec_check?access_token=" + "64_IZgywQFsRo12V5Ae3CPubtoryk4uTOU3e0k-iQTbYYsRu8fP0-coOdIziqf9-OniXKS74pU8GPPAhR4cdrTm21TNQo_wkygrQYPQtQGDNuwlOT2x9NZuj08zlNMRCFhAFAALO"
	//
	//}
	//要检测的图片文件，格式支持PNG、JPEG、JPG、GIF，图片尺寸不超过 750px x 1334px
	//{"errcode":47001,"errmsg":"data format error rid: 63ac58e6-3b1bff9f-570739ca"}
	bs, err := myhttp.PostFile(urlStr, "media", file)
	if err != nil {
		logger.Error("ImgSecCheck 请求失败", err)
	}
	var resp ImgSecCheckResp
	resp.ErrCode = -1
	err = json.Unmarshal(bs, &resp)
	str := (*string)(unsafe.Pointer(&bs))
	return resp, *str, err
}

func (o *weixinService_) GetMsgSecCheckLabelTxt(label int64) string {
	switch label {
	case 100:
		return "正常"
	case 10001:
		return "广告"
	case 20001:
		return "时政"
	case 20002:
		return "色情"
	case 20003:
		return "辱骂"
	case 20006:
		return "违法犯罪"
	case 20008:
		return "欺诈"
	case 20012:
		return "低俗"
	case 20013:
		return "版权"
	case 21000:
		return "其他"
	}
	return ""
}

func (o *weixinService_) getPhoneNumber(code string) (PhoneNumberResp, string, error) {
	urlStr := "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + AccessTokenUpdate.GetAccessToken()
	params := make(map[string]interface{})
	params["code"] = code
	bs, err := myhttp.PostByte(urlStr, params)
	if err != nil {
		logger.Error("GetPhoneNumber 请求失败", err)
	}
	var resp PhoneNumberResp
	resp.ErrCode = -1
	err = json.Unmarshal(bs, &resp)
	str := (*string)(unsafe.Pointer(&bs))
	return resp, *str, err
	//{"errcode":41002,"errmsg":"appid missing, rid: 638ed9fc-435d55ea-333db48a"}
	//{"errcode":41001,"errmsg":"access_token missing rid: 639848fb-59e7061d-6375fe6e"}
	//{"errcode":0,"errmsg":"ok","phone_info":{"phoneNumber":"***********","purePhoneNumber":"***********","countryCode":"86","watermark":{"timestamp":1672394922,"appid":"wx211812e4a35c097f"}}}
}

func (o *weixinService_) GetPhoneNumber(code string) (PhoneNumberResp, string, error) {
	phoneNumberResp, res, err := o.getPhoneNumber(code)
	return phoneNumberResp, res, err
}

func (o *weixinService_) GetOpenId(jsCode string) (Code2SessionResp, error) {
	urlStr := "https://api.weixin.qq.com/sns/jscode2session"
	params := url.Values{}
	params.Add("grant_type", "authorization_code")
	params.Add("appid", config.WeixinAppId)
	params.Add("secret", config.WeixinSecret)
	params.Add("js_code", jsCode)
	bs, err := myhttp.GetByte(urlStr, params)
	if err != nil {
		logger.Error(err, "获取AccessToken失败:", jsCode)
	}
	logger.Info("openId获取成功：", string(bs))
	var session Code2SessionResp
	err = json.Unmarshal(bs, &session)
	return session, err
}

func (o *weixinService_) GenUrlLink() string {
	urlStr := "https://api.weixin.qq.com/wxa/generate_urllink?access_token=" + AccessTokenUpdate.GetAccessToken()
	params := make(map[string]interface{})
	params["path"] = ""
	params["query"] = ""
	params["expire_type"] = 1
	params["expire_interval"] = 30
	bs, err := myhttp.PostByte(urlStr, params)
	if err != nil {
		logger.Error("GenUrlLink 请求失败", err)
		return ""
	}
	str := (*string)(unsafe.Pointer(&bs))

	m := tools.GetMapFromJson(*str)
	if _, ok := m["url_link"]; ok {
		if strings.HasPrefix(m["url_link"].(string), "https://") {
			return m["url_link"].(string)
		}
	}
	logger.Error(str)
	return ""
	//{
	//	"errcode": 0,
	//	"errmsg": "ok",
	//	"url_link": "URL Link"
	//}
}

func (o *weixinService_) SendSubscribeMessage(openId string, templateId string, data map[string]map[string]string) (string, error) {

	urlStr := "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + AccessTokenUpdate.GetAccessToken()
	params := make(map[string]interface{})
	params["touser"] = openId
	params["template_id"] = templateId
	params["data"] = data
	bs, err := myhttp.PostByte(urlStr, params)
	if err != nil {
		logger.Error("SendSubscribeMessage 请求失败", err)
	}
	str := (*string)(unsafe.Pointer(&bs))
	return *str, err
}

func (o weixinService_) SendPhotoCompledMessage(openId string, templateId string) {
	data := make(map[string]map[string]string)
	data["thing3"]["DATA"] = "" //温馨提示
	data["thing1"]["DATA"] = "" //作品名称
	data["thing5"]["DATA"] = "" //制作信息
	data["date2"]["DATA"] = ""  //完成时间

	//o.SendSubscribeMessage(openId, templateId, )
}

var WeixinService weixinService_

package service

import (
	"bufio"
	"design-ai/enums"
	"design-ai/model"
	"design-ai/utils/config"
	"design-ai/utils/logger"
	"design-ai/utils/myredis"
	"design-ai/utils/tools"
	"errors"
	"io/ioutil"
	"math/rand"
	"os"
	"path/filepath"
	"strconv"
	"time"
)

type userService_ struct {
	NickLines []string `json:"nick_lines"`
}

func (service *userService_) RandomAvatar() (string, error) {
	folderPath := "shenbixiaoai/avatar/"
	files, err := ioutil.ReadDir(config.DiffusionFilePath + folderPath)
	if err != nil {
		return "", err
	}

	rand.Seed(time.Now().UnixNano())
	randomIndex := rand.Intn(len(files))
	randomFile := files[randomIndex]

	return filepath.Join(folderPath, randomFile.Name()), nil
}

func (service *userService_) RandomNickname() (string, error) {
	filename := config.DiffusionFilePath + "shenbixiaoai/nickname.txt"
	file, err := os.Open(filename)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 使用带缓冲的读取器读取文件内容
	scanner := bufio.NewScanner(file)
	lines := make([]string, 0)

	// 逐行读取文件并将每行保存到切片中
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	if err := scanner.Err(); err != nil {
		return "", err
	}

	//service.NickLines = lines

	// 随机选择一行
	rand.Seed(time.Now().UnixNano())
	randomIndex := rand.Intn(len(lines))
	randomLine := lines[randomIndex]

	return randomLine, nil
}

func (service *userService_) SetUserInvitationCode(userId uint) error {
	var user model.User
	if err := user.GetById(userId); err != nil {
		return errors.New("查找用户信息失败")
	}
	if user.InvitationCode != "" {
		return nil
	}

	for i := 0; i < 10; i++ {
		invitationCode := tools.GetInvitationCode()
		exists, err := user.ExistsInvitationCode(invitationCode)
		if err != nil {
			logger.Error(err)
			return err
		}
		if exists == false {
			if er := user.SetInvitationCode(invitationCode); er != nil {
				logger.Error(err)
				return err
			} else {
				return nil
			}
		}
	}
	return errors.New("邀请码多次尝试设置失败")
}

func (service *userService_) IsInsiderUser(userId uint) bool {

	//insiderUser := myredis.Get(enums.RedisKeyEnum.InsiderUser)
	//if strings.Contains(insiderUser, ","+strconv.Itoa(int(userId))+",") {
	//	return true
	//}
	//return false

	insiderUserState, _ := myredis.HGet(enums.RedisKeyEnum.InsiderUsers, strconv.Itoa(int(userId)))
	if insiderUserState != "" {
		return true
	}
	return false
}

func (service *userService_) GetInsiderUserState(userId uint) string {
	insiderUserState, _ := myredis.HGet(enums.RedisKeyEnum.InsiderUsers, strconv.Itoa(int(userId)))
	return insiderUserState
}

var UserService userService_

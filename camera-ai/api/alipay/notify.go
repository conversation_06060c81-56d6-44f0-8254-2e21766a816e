package alipay

import (
	"design-ai/enums"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/logger"
	"design-ai/utils/myredis"
	"design-ai/utils/tools"
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/alipay"
	"time"
)

func Notify(c *gin.Context) {

	//defer func() {
	//	if e := recover(); e != nil {
	//		logger.Error("Notify:", e)
	//	}
	//}()

	logger.Info("已经接收到支付宝回调v1.3")

	bm, err := alipay.ParseNotifyToBodyMap(c.Request)
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Info("notifyReq:", bm)

	ok, err := service.AlipayService.VerifySign(bm)
	if err != nil {
		logger.Error("err:", err)
		return
	}
	logger.Info("支付宝验签是否通过:", ok)

	tradeStatus := bm["trade_status"].(string)
	if tradeStatus == "TRADE_SUCCESS" {
		outTradeNo := bm["out_trade_no"].(string)
		tradeNo := bm["trade_no"].(string)

		payTime, err := time.ParseInLocation("2006-01-02 15:04:05", bm["gmt_payment"].(string), time.Local) //这里按照当前时区转
		logger.Info("payTime ", bm["gmt_payment"].(string), "  ", payTime)
		if err != nil {
			logger.Error(err)
			return
		}
		if err := service.RechargeService.HandlePaySuccessRecharge(outTradeNo, tradeNo, payTime, tools.GetJsonFromStruct(bm)); err == nil {
			logger.Info("HandleRecharge success")
			c.Writer.WriteString("success")
			return
		}
	} else {
		logger.Error("其它的支付状态:", tradeStatus, " bm: ", bm)
	}

}

func HandleRecharge11(outTradeNo string, tradeNo string, payTime time.Time) error {

	lockKey := "HandleRecharge_" + outTradeNo
	lock := myredis.Lock(lockKey, 5*1000)
	if lock {
		var recharge model.Recharge
		if err := recharge.GetByOutTradeNo(outTradeNo); err != nil {
			logger.Error(err)
			return err
		}
		if recharge.State == 1 {
			logger.Info("支付状态已成功，不处理")
			return nil
		}
		if recharge.State != 0 {
			logger.Info("支付状态已变动，不处理")
			return errors.New("支付状态已变动，不处理")
		}

		var product model.RechargeProduct
		if err := product.GetByID(recharge.ProductId); err != nil {
			logger.Error(err)
			return err
		}

		recharge.PayTradeId = tradeNo
		recharge.PayTime = payTime
		if product.ProductCategory == enums.ProductCategoryEnum.Subscription {
			expiresDate := time.Time{}
			if enums.SubDurationEnum.Get(product.SubDuration) == "" {
				logger.Error("销售产品信息错误", product)
				return errors.New("销售产品信息错误")
			}
			expiresDate = enums.SubDurationEnum.GetExpiresDate(product.SubDuration, recharge.PayTime)
			if expiresDate.Equal(recharge.PayTime) {
				logger.Error("过期时间获取失败", product, expiresDate, product.SubDuration, recharge.PayTime)
				return errors.New("过期时间获取失败")
			}
			recharge.ExpiresDate = expiresDate
		}
		if err := model.Transactions.RechargeSuccess(&recharge); err != nil {
			logger.Error(err)
			return err
		}
		myredis.UnLock(lockKey)
		return nil
	} else {
		logger.Error("lock 失败")
		return errors.New("lock 等待超时")
	}
}

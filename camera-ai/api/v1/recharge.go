package v1

import (
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/skip2/go-qrcode"
	"net/http"
	"strings"
)

type rechargeApi struct {
}

type productListReq struct {
	ProductId uint   `json:"product_id"`
	Store     string `json:"store"`
	GroupName string `json:"group_name"`
}

type rechargeReq struct {
	CustomParam string `json:"custom_param"`
	ProductId   uint   `json:"product_id"`
	PayChannel  string `json:"pay_channel"`
	Gateway     string `json:"gateway"`
	Coin        int    `json:"coin"`
	Price       string `json:"price"`
}

type rechargeProductResp struct {
	ProductId   uint            `json:"product_id"`
	ProductCode string          `json:"product_code"`
	GroupName   string          `json:"group_name"`
	ShowTitle   string          `json:"show_title"`
	Description string          `json:"description"`
	Tip         string          `json:"tip"`
	Coin        int             `json:"coin"`
	Price       decimal.Decimal `json:"price"`
	OrigPrice   decimal.Decimal `json:"orig_price"`
}

type rechargeListReq struct {
	KW       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type rechargeListItemResp struct {
	UserId     uint            `json:"user_id"`
	CreatedAt  model.JsonTime  `json:"created_at"'`
	PayTime    model.JsonTime  `json:"pay_time"'`
	OutTradeNo string          `json:"out_trade_no"`
	Amount     decimal.Decimal `json:"amount"`
}

type queryOrderReq struct {
	OutTradeNo string `json:"out_trade_no"`
}

type queryOrderResp struct {
	OutTradeNo string `json:"out_trade_no"`
	State      int    `json:"state"`
}

func (obj rechargeApi) GetProductList(c *gin.Context) {
	var code int

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims.UserId <= 0 {
	//	errmsg.Abort(c, errmsg.FAIL, "请先登录")
	//	return
	//}

	insider := false
	tokenHeader := c.Request.Header.Get("Authorization")
	if tokenHeader != "" {
		claims, err := middleware.GetClaimsByToken(tokenHeader)
		if claims == nil || err != nil {
			logger.Error(err)
		} else {
			insider = service.UserService.IsInsiderUser(claims.UserId)
		}
	}

	var oReq productListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var rechargeProduct model.RechargeProduct
	ary, err := rechargeProduct.GetList(rechargeProduct.ID, "camera.cyuai.aigc", oReq.Store, oReq.GroupName) //会员套餐
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据订购产品失败")
		return
	}

	outAry := make([]rechargeProductResp, 0)
	for _, item := range ary {
		if !insider && strings.Contains(item.ShowTitle+item.Description+item.GroupName, "测试") {
			continue
		}
		resp := rechargeProductResp{
			ProductId:   item.ID,
			ProductCode: item.ProductCode,
			ShowTitle:   item.ShowTitle,
			Description: item.Description,
			Tip:         item.Tip,
			GroupName:   item.GroupName,
			Coin:        item.Coin,
			Price:       item.Price,
			OrigPrice:   item.OrigPrice,
		}
		outAry = append(outAry, resp)
	}

	result := make(map[string]interface{})
	result["items"] = outAry

	c.JSON(http.StatusOK, gin.H{
		"code":   code,
		"msg":    "产品价格信息",
		"result": result,
	})
}

func (obj rechargeApi) Launch(c *gin.Context) {

	var code int
	var oReq rechargeReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var product model.RechargeProduct
	if err := product.GetByID(oReq.ProductId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "未找到购买商品数据")
		return
	}
	var user model.User
	if user.GetById(claims.UserId) != nil {
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	//if product.ProductCategory == enums.ProductCategoryEnum.Subscription {
	//	if user.MemberExpires.After(time.Now()) {
	//		logger.Error("您的会员还未过期，请到期后再续费  userID:", claims.UserId)
	//		errmsg.Abort(c, errmsg.FAIL, "您的会员还未过期，请到期后再续费")
	//		return
	//	}
	//}

	outTradeNo := ""
	if product.ProductCategory == enums.ProductCategoryEnum.Subscription {
		outTradeNo, _ = model.OrderNo.NewByOrderType(enums.OrderTypeEnum.Subscribe, 0)
	} else {
		outTradeNo, _ = model.OrderNo.NewByOrderType(enums.OrderTypeEnum.RechargeBuy, 0)
	}
	if outTradeNo == "" {
		logger.Error(errors.New("生成单号失败"), oReq)
		errmsg.Abort(c, errmsg.FAIL, "生成单号失败")
		return
	}

	amount, err := decimal.NewFromString(oReq.Price)
	amount = amount.Round(2)
	if err != nil || amount.LessThanOrEqual(decimal.Zero) {
		logger.Error(claims.UserId, " 充值金额不正确 ", oReq)
		errmsg.Abort(c, errmsg.FAIL, "充值金额不正确")
		return
	}

	product.Price = product.Price.Round(2)

	fmt.Println(amount, product.Price)
	if !product.Price.Equal(amount) {
		logger.Error(claims.UserId, "充值金额不一致 ", oReq)
		errmsg.Abort(c, errmsg.FAIL, "充值金额不一致")
		return
	}

	if product.Coin != oReq.Coin {
		logger.Error(claims.UserId, "星星数量不一致 ", oReq)
		errmsg.Abort(c, errmsg.FAIL, "星星数量不一致")
		return
	}

	if tmpKey := enums.PayGatewayEnum.GetKey(oReq.Gateway); tmpKey == "" {
		logger.Error("支付网关参数错误", oReq)
		errmsg.Abort(c, errmsg.FAIL, "支付网关参数错误")
		return
	}

	if tmpKey := enums.PayChannelEnum.GetKey(oReq.PayChannel); tmpKey == "" {
		logger.Error("支付渠道参数错误", oReq)
		errmsg.Abort(c, errmsg.FAIL, "支付渠道参数错误")
		return
	}

	recharge := model.Recharge{
		UserId:                claims.UserId,
		OutTradeNo:            outTradeNo,
		ProductId:             oReq.ProductId,
		ProductCode:           product.ProductCode,
		ProductCategory:       product.ProductCategory,
		Coin:                  product.Coin,
		Amount:                product.Price,
		PayChannel:            oReq.PayChannel,
		Gateway:               oReq.Gateway,
		PayCallbackJson:       "{}",
		PayRefundCallbackJson: "{}",
		CustomParam:           "{}",
	}

	if oReq.CustomParam != "" {
		recharge.CustomParam = oReq.CustomParam
	}

	if err := recharge.Save(); err != nil || recharge.ID == 0 {
		errmsg.Abort(c, errmsg.FAIL, "充值订单生成失败")
		return
	}

	result := make(map[string]interface{})
	if oReq.PayChannel == enums.PayChannelEnum.WechatPay {
		if oReq.Gateway == enums.PayGatewayEnum.Web {
			logger.Info("发起微信native支付")
			nativeRsp, err := service.WechatpayService.TradePayNative(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				logger.Error(err, nativeRsp)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_url"] = nativeRsp.Response.CodeUrl

			qrCode, err := qrcode.New(nativeRsp.Response.CodeUrl, qrcode.Medium)
			if err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "生成二维码失败")
				return
			}
			pngBytes, err := qrCode.PNG(256)
			if err != nil {
				fmt.Println(err)
				return
			}
			result["qr_code"] = "data:image/png;base64," + base64.StdEncoding.EncodeToString(pngBytes)
			result["trade_no"] = recharge.OutTradeNo
			result["out_trade_no"] = recharge.OutTradeNo

		} else if oReq.Gateway == enums.PayGatewayEnum.H5 {
			h5Rsp, err := service.WechatpayService.TradePayH5(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				logger.Error(err, h5Rsp)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_url"] = h5Rsp.Response.H5Url
		} else if oReq.Gateway == enums.PayGatewayEnum.App {
			prepayRsp, err := service.WechatpayService.TradePayApp(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				logger.Error(err, prepayRsp)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["prepayid"] = prepayRsp.Response.PrepayId
			payParams, err := service.WechatpayService.PaySignOfApp(prepayRsp.Response.PrepayId)
			if err != nil {
				logger.Error(err)
			}
			result["payParams"] = payParams

		} else if oReq.Gateway == enums.PayGatewayEnum.Jsapi {
			prepayRsp, err := service.WechatpayService.TradePayJsapi(user.Openid, recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				logger.Error(err, prepayRsp)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["prepayid"] = prepayRsp.Response.PrepayId
			payParams, err := service.WechatpayService.PaySignOfJSAPI(prepayRsp.Response.PrepayId)
			if err != nil {
				logger.Error(err)
			}
			result["payParams"] = payParams
		} else {
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		}
	} else if oReq.PayChannel == enums.PayChannelEnum.AliPay {
		if oReq.Gateway == enums.PayGatewayEnum.Wap {
			payUrl, err := service.AlipayService.TradeWapPay(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				logger.Error(err, payUrl)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_url"] = payUrl
		} else if oReq.Gateway == enums.PayGatewayEnum.Web {
			payUrl, err := service.AlipayService.TradePagePay(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				logger.Error(err, payUrl)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_url"] = payUrl
		} else if oReq.Gateway == enums.PayGatewayEnum.Native {
			payRsp, err := service.AlipayService.TradePrecreate(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				logger.Error(err, payRsp)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["pay_rsp"] = payRsp
		} else if oReq.Gateway == enums.PayGatewayEnum.App {
			payParam, err := service.AlipayService.TradeAppPay(recharge.OutTradeNo, recharge.Amount, product.ShowTitle+" "+product.Description)
			if err != nil {
				logger.Error(err, payParam)
				errmsg.Abort(c, errmsg.FAIL, "发送订单失败")
				return
			}
			result["out_trade_no"] = recharge.OutTradeNo
			result["pay_param"] = payParam
		} else {
			logger.Error("参数错误", oReq)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		}
	} else if oReq.PayChannel == enums.PayChannelEnum.AppleIap {
		result["out_trade_no"] = recharge.OutTradeNo
	} else {
		logger.Error("参数错误", oReq)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	if err := recharge.GetByID(recharge.ID); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "充值订单生成失败")
		return
	}
	logger.Info("len(result)", len(result))
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "请充值",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "")
	}

}

func (obj rechargeApi) QueryByOutTradeNo(c *gin.Context) {
	var code int

	var oReq queryOrderReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if er := c.ShouldBindJSON(&oReq); er != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var recharge model.Recharge
	if err := recharge.GetByOutTradeNo(oReq.OutTradeNo); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询订单出错")
		return
	}
	if recharge.UserId != claims.UserId {
		errmsg.Abort(c, errmsg.FAIL, "查询支付订单失败")
		return
	}

	result := make(map[string]interface{})
	result["out_trade_no"] = recharge.OutTradeNo
	result["state"] = recharge.State
	result["state_txt"] = enums.RechargeStateEnum.GetShowTitle(recharge.State)

	c.JSON(http.StatusOK, gin.H{
		"code":   code,
		"msg":    "订单信息",
		"result": result,
	})

}

func (obj rechargeApi) GetBalance(c *gin.Context) {
	var code int
	var msg string
	var listReq rechargeListReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if er := c.ShouldBindJSON(&listReq); er != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	if listReq.PageSize < 1 {
		listReq.PageSize = 1
	}
	if listReq.Page < 1 {
		listReq.Page = 1
	}

	var recharge model.Recharge
	ary := make([]rechargeListItemResp, 0)
	total, err := recharge.GetList(&ary, claims.UserId, listReq.KW, enums.RechargeStateEnum.TRADE_SUCCESS, listReq.Page, listReq.PageSize)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询出错")
		return
	}

	result := make(map[string]interface{})
	result["items"] = ary
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

//
//func (obj rechargeApi) GetList(c *gin.Context) {
//	var code int
//	var msg string
//	var listReq req.RechargeListReq
//
//	claims := c.Value("claims").(*middleware.MyClaims)
//	if claims.UserId <= 0 {
//		errmsg.Abort(c, errmsg.FAIL, "请先登录")
//		return
//	}
//
//	er := c.ShouldBindJSON(&listReq)
//	if er != nil {
//		code = errmsg.FAIL
//		msg = "数据获取失败"
//		errmsg.Abort(c, code, msg)
//		return
//	}
//
//	if listReq.PageSize < 1 {
//		listReq.PageSize = 1
//	}
//	if listReq.Page < 1 {
//		listReq.Page = 1
//	}
//
//	var o model.Recharge
//	data, total, err := o.GetList(claims.UserId, listReq.KW, 1, listReq.Page, listReq.PageSize)
//	if err != nil {
//		errmsg.Abort(c, errmsg.FAIL, "查询出错")
//		return
//	}
//
//	result := make(map[string]interface{})
//	result["items"] = data
//	result["total"] = total
//
//	if code == errmsg.SUCCESS {
//		c.JSON(http.StatusOK, gin.H{
//			"code":    code,
//			"message": "",
//			"msg":     "",
//			"result":  result,
//		})
//	} else {
//		errmsg.Abort(c, errmsg.FAIL, msg)
//	}
//}

var RechargeApi rechargeApi

package v1

import (
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/tools"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
)

type coinApi_ struct {
}

type addQuickReq struct {
	UserId   uint   `json:"user_id"`
	Amount   int    `json:"amount"`
	Operator string `json:"operator"`
}

type balanceListReq struct {
	KW       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type balanceItemResp struct {
	ID             uint           `json:"id"`
	UserId         uint           `json:"user_id"`
	CreatedAt      model.JsonTime `json:"created_at"'`
	OrderNo        string         `json:"order_no"`
	OccurredAmount int            `json:"occurred_amount"`
	Show           string         `json:"show"`
}

func (obj coinApi_) GetBalance(c *gin.Context) {
	var code int
	var msg string
	var listReq balanceListReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	er := c.ShouldBindJSON(&listReq)
	if er != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if listReq.PageSize < 1 {
		listReq.PageSize = 1
	}
	if listReq.Page < 1 {
		listReq.Page = 1
	}

	var coinBalance model.CoinBalance
	ary := make([]balanceItemResp, 0)
	total, err := coinBalance.GetList(&ary, claims.UserId, listReq.KW, listReq.Page, listReq.PageSize)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询出错")
		return
	}

	result := make(map[string]interface{})
	result["items"] = ary
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj coinApi_) AddQuick(c *gin.Context) {
	var code int
	var msg string
	var oReq addQuickReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if claims.UserId != 1 {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if oReq.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请输入用户")
		return
	}

	var user model.User
	str := strconv.FormatUint(uint64(oReq.UserId), 10)
	if tools.IsMobile(str) {
		if err := user.GetByMobile(str); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "手机号码不正确")
			return
		}
	} else {
		if err := user.GetById(oReq.UserId); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "用户ID不正确")
			return
		}
	}

	if oReq.Amount <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请输入金额")
		return
	}

	if len(oReq.Operator) == 0 {
		errmsg.Abort(c, errmsg.FAIL, "请输入操作员")
		return
	}

	balance := model.CoinBalance{
		UserId:         user.ID,
		OperatorId:     claims.UserId,
		OccurredAmount: oReq.Amount,
	}

	orderNo, err := model.OrderNo.NewByOrderType(enums.OrderTypeEnum.ManagerAdd, 0)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "生成单号错误")
		return
	}

	err = balance.GetBalanceObject(orderNo, user.ID, enums.OrderTypeEnum.ManagerAdd, oReq.Amount, "内测赠送", "管理员添加", claims.UserId, oReq.Operator)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "生成流水数据错误")
		return
	}
	err = model.Transactions.ManageAddCoin(&balance)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "钱币添加出错")
		return
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  "添加成功",
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

var CoinApi coinApi_

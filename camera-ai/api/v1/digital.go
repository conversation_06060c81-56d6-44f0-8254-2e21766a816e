package v1

import (
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/myhttp"
	"design-ai/utils/tools"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
)

type digitalApi_ struct {
}

var DigitalApi digitalApi_

type analyzeFaceReq struct {
	FaceImgMd5 string `json:"face_img_md5"`
}

type digitalResetReq struct {
	FaceImgMd5 string `json:"face_img_md5"`
}

func (obj digitalApi_) AnalyzeFace(c *gin.Context) {
	var code int
	var msg string
	var oReq analyzeFaceReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var uploadImg model.UploadImg
	if err := uploadImg.GetByMd5(oReq.FaceImgMd5); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户照片失败")
		return
	}
	if uploadImg.UserId != claims.UserId {
		logger.Error("用户ID不一致", claims.UserId, "   ", oReq)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	fields := map[string]string{
		//"relative_path_file": relativePathFile,
	}
	headers := map[string]string{
		//"Authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJtb2JpbGUiOiIxMzAqKioqMzI3OSIsInVzZXJuYW1lIjoidmJlbiIsImlkIjoxLCJleHAiOjMzMjE2MTQ1NzUwLCJpc3MiOiJkZXNpZ24iLCJuYmYiOjE2ODAxNDU2NTB9.GBfjCyCz71aJBKysCVODng5L3b2sF4JyqHgZODqw6ck",
	}
	url := "http://192.168.200.170:5000/has_face"
	filepath := config.DiffusionFilePath + uploadImg.Path
	strJson, err := myhttp.UploadFile(url, filepath, "image", fields, headers)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "上传图片到服务器失败")
		return
	}
	m := tools.GetMapFromJson(strJson)
	facePass := true
	if _, ok := m["has_face"]; ok {
		facePass = m["has_face"].(bool)
		if facePass == false {
			msg = "未检测到面部信息，请按要求上传正脸照片"
		} else {
			msg = "识别成功"
		}
	} else {
		msg = "识别失败"
		logger.Error("face 识别失败", oReq, "   ", strJson)
		logJson := fmt.Sprintf(`{"face_img_md5":%s,"face_img_path":%s}`, oReq.FaceImgMd5, uploadImg.Path)

		userEnvStr := c.Request.Header.Get("User-Env")
		if userEnvStr == "" {
			userEnvStr = "{}"
		}
		userLog := model.UserLog{
			TrackId: tools.Md5(userEnvStr),
			UserId:  claims.UserId,
			LogType: enums.UserLogTypeEnum.ImportantError,
			Ip:      tools.GetClientIp(c.Request.Header),
			LogJson: logJson,
			UserEnv: userEnvStr,
		}
		if err := userLog.Save(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存日志出错")
			return
		}
	}
	//var user model.User
	//if err := user.GetById(claims.UserId); err != nil {
	//	logger.Error(err)
	//	errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
	//	return
	//}

	result := make(map[string]interface{})
	result["face_pass"] = facePass

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj digitalApi_) Reset(c *gin.Context) {
	var code int
	var msg string
	var oReq digitalResetReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	if user.DigitalUuId == "" {
		logger.Error("还没有数字人,不能重置 userID:", claims.UserId)
		errmsg.Abort(c, errmsg.FAIL, "还没有数字人,不能重置")
		return
	}

	var uploadImg model.UploadImg
	if err := uploadImg.GetByMd5(oReq.FaceImgMd5); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户照片失败")
		return
	}

	var digital model.Digital
	if err := digital.GetByMd5(user.DigitalUuId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取数字人信息失败")
		return
	}

	if digital.TrainFree <= 0 {
		logger.Error("数字人免费制作次数已经用完", digital.ID)
		errmsg.Abort(c, errmsg.FAIL, "数字人免费制作次数已经用完")
		return
	}
	logger.Info("免费重置数字人", uploadImg.Md5, uploadImg.Path, digital.TrainFree-1)
	if err := digital.ReSetFaceImg(uploadImg.Md5, uploadImg.Path, digital.TrainFree-1); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数字人重置失败")
		return
	}

	result := make(map[string]interface{})

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "重置成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj digitalApi_) Del(c *gin.Context) {
	var code int
	var msg string

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	if user.DigitalId > 0 || user.DigitalUuId != "" {
		if err := user.DelDigital(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "删除失败")
			return
		}
	}

	result := make(map[string]interface{})

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "删除成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

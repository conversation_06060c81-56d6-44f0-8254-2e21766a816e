package manage

import (
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/tools"
	"net/http"

	"github.com/gin-gonic/gin"
)

type userApi_ struct {
}

var UserApi userApi_

type setInsiderReq struct {
	UserId  uint `json:"user_id"`
	Insider int  `json:"insider"`
}

type userQueryReq struct {
	Username string `json:"username"`
	Mobile   string `json:"mobile"`
	Invitor  string `json:"invitor"`
	OrderBy  string `json:"order_by"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type userListRes struct {
	ID             uint           `json:"id"`
	CreatedAt      model.JsonTime `json:"create_at"`
	Username       string         `json:"username"`
	Mobile         string         `json:"mobile"`
	Coin           string         `json:"coin"`
	InvitationCode string         `json:"invitation_code"`
	InvitedCode    string         `json:"invited_code"`
	InvitorMobile  string         `json:"invitor_mobile"`
	Remark         string         `json:"remark"`
	Plat           string         `json:"plat"`
	Os             string         `json:"os"`
	Channel        string         `json:"channel"`
	RegIp          string         `json:"reg_ip"`
	RegAddress     string         `json:"reg_address"`
	RegEnv         string         `json:"reg_env"`
}

// 用户列表
func (obj userApi_) UserList(c *gin.Context) {
	var code int
	var req userQueryReq
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var user model.User
	arr := make([]userListRes, 0)
	total, err := user.GetUserList(&arr, req.Username, req.Mobile, req.Invitor, req.OrderBy, req.Page, req.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "用户信息获取失败")
		return
	}

	for i := 0; i < len(arr); i++ {

		if user.RegEnv != "" {
			var userEnv model.UserEnv
			if err := tools.GetStructFromJson(&userEnv, user.RegEnv); err != nil {
				logger.Error(err, user.RegEnv)
			}
			if arr[i].Plat == "" {
				arr[i].Plat = service.LocationService.GetPlat(userEnv)
			}
			arr[i].Channel = userEnv.Channel
		}
	}

	result := make(map[string]interface{})
	result["items"] = arr
	result["total"] = total
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}

}

func (obj userApi_) SetInsider(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	var oReq setInsiderReq

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if oReq.Insider != 0 && oReq.Insider != 1 && oReq.Insider != 2 {
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	userId := oReq.UserId
	var user model.User
	if err := user.GetById(userId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	if er := user.SetInsider(oReq.Insider); er != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "设置失败")
		return
	}
	result := make(map[string]interface{})
	result["insider"] = oReq.Insider
	c.JSON(http.StatusOK, gin.H{
		"code":   code,
		"msg":    "设置成功",
		"result": result,
	})
}

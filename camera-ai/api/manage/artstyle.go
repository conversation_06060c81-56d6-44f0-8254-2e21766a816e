package manage

import (
	"crypto/md5"
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/myimg"
	"design-ai/utils/tools"
	"encoding/hex"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path"
	"strings"
	"time"
)

type artStyleApi struct {
}

type artStyleReq struct {
	ID         uint   `json:"id"`
	ArtType    int    `json:"art_type"`
	Sex        int    `json:"sex"`
	Title      string `json:"title"`
	IsHot      int    `json:"is_hot"`
	IsDefault  int    `json:"is_default"`
	PriceCoin  int    `json:"price_coin"`
	OrderIndex int    `json:"order_index"`
	Remark     string `json:"remark"`
	State      int    `json:"state"`
}

type artStyleRepairReq struct {
	ArtStyleId uint `json:"art_style_id"`
}

type artStyleListReq struct {
	ArtStyleId uint   `json:"art_style_id"`
	ArtType    int    `json:"art_type"`
	Sex        int    `json:"sex"`
	AgeSect    string `json:"age_sect"`
	State      int    `json:"state"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type artStyleListItem struct {
	ID           uint            `json:"id"`
	CreatedAt    model.JsonTime  `json:"created_at"`
	Title        string          `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	ArtType      int             `json:"art_type" gorm:"type:tinyint;not null;default:0;comment:1证件照 2艺术照 3社交头像" `
	Sex          int             `json:"sex" gorm:"type:tinyint;not null;default:0;comment:1男 2女"`
	StyleImgUrls []uploadImgItem `json:"style_img_urls" gorm:"type:json;comment:风格图片"`
	Remark       string          `json:"remark" gorm:"type:varchar(500);not null;default:'';comment:后台备注"`
	OrderIndex   int             `json:"order_index" gorm:"type:int;not null;default:0;comment:序号(越大越前面)"`
	PriceCoin    int             `json:"price_coin" gorm:"type:int;not null;default:0;comment:所需Coin"`
	IsHot        int             `json:"is_hot" gorm:"type:int;not null;default:0;comment:热门推荐"`
	IsDefault    int             `json:"is_default" gorm:"type:int;not null;default:0;comment:1默认"`
	MainBody     int             `json:"main_body" gorm:"type:int;not null;default:0;comment:主体 对应MainBodyEnum"`
	State        int             `json:"state" gorm:"type:int;not null;default:0;comment:状态(1可用)"`
}

type uploadImgItem struct {
	Md5        string  `json:"md5" gorm:"type:varchar(50);not null;default:'';comment:图片md5"`
	Path       string  `json:"-" gorm:"type:varchar(100);not null;default:'';comment:图片路径"`
	ImgUrl     string  `json:"img_url"`
	Width      int     `json:"width" gorm:"type:int;not null;default:0;comment:图片宽度"`
	Height     int     `json:"height" gorm:"type:int;not null;default:0;comment:图片高度"`
	OrderIndex float32 `json:"order_index" gorm:"type:float;not null;default:0;comment:排序 越大越前面"`
	State      int     `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0初始 1上传成功 2校验失败 3非法图片 4校验通过 5使用中"`
}

func (obj artStyleApi) Add(c *gin.Context) {
	var code int
	var msg string

	var oReq artStyleReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	o := model.ArtStyle{
		Title:      oReq.Title,
		ArtType:    oReq.ArtType,
		Sex:        oReq.Sex,
		PriceCoin:  oReq.PriceCoin,
		IsHot:      oReq.IsHot,
		IsDefault:  oReq.IsDefault,
		OrderIndex: oReq.OrderIndex,
		Remark:     oReq.Remark,
		State:      1,
	}
	msg = "添加成功"
	if oReq.ID > 0 {
		msg = "更新成功"
		if err := o.GetById(oReq.ID); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "风格获取失败")
			return
		}
		logger.Info("ArtStyle更新备份 ID：", o.ID, "  更新前StyleImgs：", o.StyleImgs)

		o.Title = oReq.Title
		o.PriceCoin = oReq.PriceCoin
		o.IsHot = oReq.IsHot
		o.IsDefault = oReq.IsDefault
		o.OrderIndex = oReq.OrderIndex
		o.Remark = oReq.Remark
		o.State = oReq.State
	} else {
		o.StyleImgs = "[]"
	}

	if err := o.Save(); err != nil || o.ID == 0 {
		logger.Error("数据保存失败", err, o.ID)
		code = errmsg.FAIL
		msg = "数据保存失败"
		errmsg.Abort(c, code, msg)
		return
	}

	result := make(map[string]interface{})
	result["art_style_id"] = o.ID

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj artStyleApi) UploadStyleImg(c *gin.Context) {
	var code int

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	f, err := c.FormFile("file")
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}

	artStyleId := uint(0)
	artStyleIdStr, _ := c.GetPostForm("art_style_id")
	if artStyleIdStr != "" {
		artStyleId = tools.ParseUint(artStyleIdStr)
	}
	if artStyleId <= 0 {
		logger.Error("artStyleId<=0:", artStyleIdStr)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
	}
	oMd5Str := fmt.Sprintf("%d,%s", artStyleId, time.Now().Format("2006-01-02 15:04:05.000"))
	has := md5.Sum([]byte(oMd5Str))
	md5Str := hex.EncodeToString(has[:])
	if len(md5Str) != 32 {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "头像图片名称生成失败")
		return
	}

	ext := path.Ext(f.Filename) // 输出 .html

	filename := md5Str + strings.ToLower(ext)

	pre2 := md5Str
	pre2 = pre2[0:1]

	//filepath := config.TempImgFilePath + filename
	//relativePath := fmt.Sprintf("%s/user-%s/%s", time.Now().Format("20060102"), pre2, filename)
	relativePath := fmt.Sprintf("camera/artstyle/")
	relativePathFile := relativePath + filename

	dirPath := config.DiffusionFilePath + relativePath
	filepath := config.DiffusionFilePath + relativePathFile

	tempImg := model.UploadImg{
		OrigWhere: enums.UploadImgOrigWhereEnum.ArtStyle,
		OrigId:    artStyleId,
		Md5:       md5Str,
		Path:      relativePathFile,
		State:     0,
	}
	if err := tempImg.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片数据生成失败")
		return
	}

	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		// mkdir 创建目录，mkdirAll 可创建多层级目录
		if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
			logger.Error(err, dirPath)
			errmsg.Abort(c, errmsg.FAIL, "创建路径失败")
			return
		}
	}

	if err := c.SaveUploadedFile(f, filepath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}
	if err := tempImg.SetState(enums.UploadImgStateEnum.Uploaded); err != nil {
		logger.Error(err)
	}
	img, size, _, err := myimg.FileToImgAndSize(filepath)
	if err := tempImg.SetWidthHeight(img.Bounds().Size().X, img.Bounds().Size().Y, size); err != nil {
		logger.Error(err)
	}
	small := myimg.ResizeImg(512, 512, img, true)
	smallFilePath := service.ImgService.GetSmallImagePath(filepath)
	if err := myimg.ImgToFile(small, smallFilePath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "缩略图保存失败")
	}

	result := make(map[string]interface{})

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "上传成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "头像上传失败")
	}
}

func (obj artStyleApi) Repair(c *gin.Context) {
	var code int
	var oReq artStyleRepairReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	if err := errmsg.ShouldBindJSON(c, &oReq); err != nil {
		logger.Error(err)
		return
	}

	if oReq.ArtStyleId == 0 {
		logger.Error("参数错误")
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	if err := service.ArtStyle.RepairArtisticItem(oReq.ArtStyleId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "处理失败")
		return
	}

	result := make(map[string]interface{})

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "处理成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "处理失败")
	}
}

func (obj artStyleApi) RepairPassport(c *gin.Context) {
	var code int
	var oReq artStyleRepairReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	if err := errmsg.ShouldBindJSON(c, &oReq); err != nil {
		logger.Error(err)
		return
	}

	if err := service.ArtStyle.RepairPassportArtStyle(oReq.ArtStyleId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "处理失败")
		return
	}

	result := make(map[string]interface{})

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "处理成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "处理失败")
	}
}

func (obj artStyleApi) GetList(c *gin.Context) {
	var code int
	var oReq artStyleListReq
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var artStyle model.ArtStyle
	var arr = make([]artStyleListItem, 0)
	total, err := artStyle.GetList(&arr, oReq.ArtStyleId, oReq.ArtType, oReq.Sex, oReq.AgeSect, " ", " ", oReq.State, oReq.Page, oReq.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
		return
	}

	var uploadImg model.UploadImg
	for i := 0; i < len(arr); i++ {
		var arrImg = make([]uploadImgItem, 0)
		_, err := uploadImg.GetList(&arrImg, uint(0), enums.UploadImgOrigWhereEnum.ArtStyle, arr[i].ID, -1, "asc", 1, 100)
		if err != nil {
			logger.Error(err)
		}
		for j := 0; j < len(arrImg); j++ {
			arrImg[j].ImgUrl = config.DiffusionDomain + arrImg[j].Path
		}
		arr[i].StyleImgUrls = arrImg
		//arr[i].StateTxt = enums.HandrawStateEnum.GetKey(arr[i].State)
	}
	result := make(map[string]interface{})
	result["items"] = arr
	result["total"] = total
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

var ArtStyleApi artStyleApi

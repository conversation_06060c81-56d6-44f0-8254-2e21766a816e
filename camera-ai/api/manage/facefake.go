package manage

import (
	"crypto/md5"
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/service"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/tools"
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"net/http"
	"strings"
	"time"
)

type facefakeApi_ struct {
}

var FacefakeApi facefakeApi_

type facefakeReq struct {
	StyleImgMd5 string `json:"style_img_md5"`
	PhotoBg     string `json:"photo_bg"`
	PhotoSize   string `json:"photo_size"`
}

type facefakeDelReq struct {
	FfUuid string `json:"ff_uuid"`
}

type facefakeResp struct {
	FfUuid     string   `json:"ff_uuid"`
	OutImgMd5s []string `json:"out_img_md5s"`
	ArtType    int      `json:"art_type"`
	PhotoBg    string   `json:"photo_bg"`
	PhotoSize  string   `json:"photo_size"`
}

type facefakeListReq struct {
	FfUuid   string `json:"ff_uuid"`
	UserId   uint   `json:"user_id"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type facefakeOutImgItem struct {
	Md5           string `json:"-"`
	Width         int    `json:"width"`
	Height        int    `json:"height"`
	Path          string `json:"-"`
	Path1         string `json:"-"`
	ArtStyleId    uint   `json:"art_style_id"`
	OutputImgMd5  string `json:"output_img_md5"`
	OutputImgUrl  string `json:"output_img_url"`
	SmallImgUrl   string `json:"small_img_url"`
	UpscaleImgUrl string `json:"upscale_img_url"`
	Collect       int    `json:"collect"`
}

type facefakeListItem struct {
	ID            uint                 `json:"id"`
	Uuid          string               `json:"uuid"`
	UserId        uint                 `json:"user_id"`
	ArtType       int                  `json:"art_type" gorm:"type:tinyint;not null;default:0;comment:1证件照 2艺术照 3社交头像" `
	ArtTypeTxt    string               `json:"art_type_txt"`
	ArtStyleId    uint                 `json:"art_style_id"`
	ArtStyleTitle string               `json:"art_style_title"`
	PhotoBg       string               `json:"photo_bg"`
	PhotoSize     string               `json:"photo_size"`
	StyleImgPath  string               `json:"-"`
	StyleImgUrl   string               `json:"style_img_url"`
	FaceImgPath   string               `json:"-"`
	FaceImgUrl    string               `json:"face_img_url"`
	Sex           int                  `json:"-" gorm:"type:tinyint;not null;default:0;comment:1男 2女"`
	SexTxt        string               `json:"sex_txt"`
	StyleImgMd5   string               `json:"style_img_md5" gorm:"type:varchar(50);comment:风格照片md5"`
	OutImgs       []facefakeOutImgItem `json:"out_imgs" gorm:"type:json;comment:生成图片"`
	CreatedAt     model.JsonTime       `json:"created_at"`
}

func (obj facefakeApi_) Gen(c *gin.Context) {
	var code int

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	userId := claims.UserId

	var oReq facefakeReq
	if err := errmsg.ShouldBindJSON(c, &oReq); err != nil {
		logger.Error(err)
		return
	}

	var user model.User
	if err := user.GetById(userId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	needCoin := 0

	var styleImg model.UploadImg
	if err := styleImg.GetByMd5(oReq.StyleImgMd5); err != nil {
		logger.Error(err, oReq.StyleImgMd5)
		errmsg.Abort(c, errmsg.FAIL, "请点击更换造型")
		return
	}
	if styleImg.OrigWhere != enums.UploadImgOrigWhereEnum.ArtStyle {
		logger.Error("风格图片信息不匹配", styleImg)
		errmsg.Abort(c, errmsg.FAIL, "风格图片信息不匹配")
		return
	}

	var artStyle model.ArtStyle
	if err := artStyle.GetById(styleImg.OrigId); err != nil {
		logger.Error(err)
	}

	if artStyle.ArtType == enums.ArtTypeEnum.Artistic {
		oReq.PhotoSize = ""
		oReq.PhotoBg = ""
	}

	var digital model.Digital
	if err := digital.GetById(user.DigitalId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数字人失效，请重新生成")
		return
	}

	uuid := uuid.New()
	uuidStr := strings.Replace(uuid.String(), "-", "", -1)
	facefake := model.Facefake{
		Uuid:          uuidStr,
		UserId:        claims.UserId,
		TrainType:     1,
		ArtType:       artStyle.ArtType,
		Sex:           artStyle.Sex,
		ArtStyleId:    artStyle.ID,
		ArtStyleTitle: artStyle.Title,
		AgeSect:       artStyle.AgeSect,
		Dress:         artStyle.Dress,
		PhotoBg:       oReq.PhotoBg,
		PhotoSize:     oReq.PhotoSize,
		StyleImgMd5:   styleImg.Md5,
		StyleImgPath:  styleImg.Path,
		StyleImgIndex: int(styleImg.OrderIndex),
		FaceImgMd5:    digital.FaceImgMd5,
		FaceImgPath:   digital.FaceImgPath,
		PriceCoin:     needCoin,
		PushJson:      "{}",
	}

	if user.Coin < needCoin {
		logger.Error("星星不足")
		errmsg.Abort(c, errmsg.FAIL, "星星不足，请充值")
		return
	}

	if err := model.Transactions.FacefakeCostCoin(&facefake); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "创建失败")
		return
	}

	json := service.FacefakeService.GetPushJsonTemplate(facefake.ID)
	if json == "" {
		errmsg.Abort(c, errmsg.FAIL, "生成绘图数据失败")
		return
	}
	if err := facefake.SetPushJson(json); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存绘图数据失败")
		return
	}

	orderIndex := int(styleImg.OrderIndex)
	aryArtStyleImg := make([]string, 0)
	for i := 1; i <= 4; i++ {
		tmpPath := ""
		if artStyle.ArtType == enums.ArtTypeEnum.Artistic {
			tmpPath = service.ArtStyle.GetStyleImagePath(artStyle, orderIndex, i)
		} else if artStyle.ArtType == enums.ArtTypeEnum.Passport {
			tmpPath = service.ArtStyle.GetPassportStyleImagePath(facefake, i)
		} else {
			logger.Error("未处理的照片类型", artStyle)
		}

		logger.Info(tmpPath)
		if exist, err := tools.PathFileExists(config.DiffusionFilePath + tmpPath); exist && err == nil {
			aryArtStyleImg = append(aryArtStyleImg, tmpPath)
		} else {
			logger.Error(err, "风格图路径不存在：", config.DiffusionFilePath+tmpPath)
		}
	}
	logger.Info(aryArtStyleImg)
	if len(aryArtStyleImg) == 0 {
		logger.Error("获取风格照片失败", oReq)
		errmsg.Abort(c, errmsg.FAIL, "获取风格照片失败")
		return
	}

	count := 4
	var outImages []model.OutImg
	for i := 0; i < count; i++ {
		oMd5Str := fmt.Sprintf("camera,%s,%d,%d", time.Now().Format("2006-01-02 15:04:05.000"), tools.GetRandom(1000, 9999), i)
		has := md5.Sum([]byte(oMd5Str))
		md5Str := hex.EncodeToString(has[:])
		if len(md5Str) != 32 {
			logger.Error(errors.New("md5位数不正确"))
			errmsg.Abort(c, errmsg.FAIL, "手绘图名称生成失败")
			return
		}

		styleImgPath := facefake.StyleImgPath
		if len(aryArtStyleImg) > i {
			styleImgPath = aryArtStyleImg[i]
		}

		outImg := model.OutImg{
			UserId:        userId,
			OrigWhere:     enums.OrigWhereEnum.Facefake,
			OrigId:        facefake.ID,
			BatchNum:      i,
			ArtStyleId:    artStyle.ID,
			ArtType:       artStyle.ArtType,
			StyleImgPath:  styleImgPath,
			StyleImgIndex: orderIndex,
			FaceImgPath:   digital.FaceImgPath,
			Sex:           facefake.Sex,
			AgeSect:       facefake.AgeSect,
			Dress:         facefake.Dress,
			PhotoBg:       facefake.PhotoBg,
			PhotoSize:     facefake.PhotoSize,
			Md5:           md5Str,
		}
		outImages = append(outImages, outImg)
	}

	if err := outImages[0].BatchCreate(outImages); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "批量添加数据出错")
		return
	}

	failCount := 0
	successCount := 0
	tmpAry := make([]string, 0)
	for _, item := range outImages {
		tmpAry = append(tmpAry, item.Md5)
		if _, err := service.FacefakeService.PushJson(json, item.Md5, item.StyleImgPath); err != nil {
			errmsg.Abort(c, errmsg.FAIL, "发送绘图数据失败")
			failCount++
			return
		}
		successCount++
	}

	result := make(map[string]interface{})

	result["item"] = facefakeResp{
		FfUuid:     uuidStr,
		ArtType:    facefake.ArtType,
		PhotoBg:    facefake.PhotoBg,
		PhotoSize:  facefake.PhotoSize,
		OutImgMd5s: tmpAry,
	}

	msg := fmt.Sprintf("数据发送完成(%d/%d)", successCount, count)
	msg = "制作中..."
	if failCount > 0 {
		msg += fmt.Sprintf("，失败%d条", failCount)
	}
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "绘图数据发送失败")
	}
}

func (obj facefakeApi_) GetList(c *gin.Context) {
	var code int
	var oReq facefakeListReq
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var faceFake model.Facefake

	if oReq.FfUuid != "" {
		if err := faceFake.GetByUuId(oReq.FfUuid); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "获取数据失败")
			return
		}
	}

	var arr = make([]facefakeListItem, 0)
	total, err := faceFake.GetList(&arr, faceFake.ID, oReq.UserId, 0, 0, -1, oReq.Page, oReq.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
		return
	}

	var outImg model.OutImg
	for i := 0; i < len(arr); i++ {
		var arrImg = make([]facefakeOutImgItem, 0)
		_, err := outImg.GetList(&arrImg, arr[i].UserId, enums.OrigWhereEnum.Facefake, arr[i].ID, 0, 1, 100)
		if err != nil {
			logger.Error(err)
		}
		for j := 0; j < len(arrImg); j++ {
			arrImg[j].OutputImgMd5 = arrImg[j].Md5
			if arrImg[j].Path != "" {
				arrImg[j].OutputImgUrl = config.DiffusionDomain + arrImg[j].Path
				arrImg[j].SmallImgUrl = config.DiffusionDomain + service.ImgService.GetSmallJpgImagePath(arrImg[j].Path)
			}
			if arrImg[j].Path1 != "" {
				arrImg[j].UpscaleImgUrl = config.DiffusionDomain + arrImg[j].Path1
			}
		}
		arr[i].OutImgs = arrImg
		arr[i].ArtTypeTxt = enums.ArtTypeTxtEnum.GetValue(enums.ArtTypeEnum.GetKey(arr[i].ArtType))
		if arr[i].Sex == 1 {
			arr[i].SexTxt = "男"
		} else if arr[i].Sex == 2 {
			arr[i].SexTxt = "女"
		}
		arr[i].StyleImgUrl = config.DiffusionDomain + arr[i].StyleImgPath
		arr[i].FaceImgUrl = config.DiffusionDomain + arr[i].FaceImgPath
	}
	result := make(map[string]interface{})
	result["items"] = arr
	result["total"] = total
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

func (obj facefakeApi_) Del(c *gin.Context) {
	var code int
	var msg string
	var oReq facefakeDelReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var faceFake model.Facefake
	if err := faceFake.GetByUuId(oReq.FfUuid); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取信息失败")
		return
	}

	if faceFake.UserId != claims.UserId {
		logger.Error("无权限")
		errmsg.Abort(c, errmsg.FAIL, "无权限")
		return
	}

	var arr = make([]facefakeListItem, 0)
	_, err := faceFake.GetList(&arr, faceFake.ID, claims.UserId, 0, 0, -1, 1, 10)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "查询失败")
		return
	}

	var outImg model.OutImg
	for i := 0; i < len(arr); i++ {
		var arrImg = make([]model.OutImg, 0)
		_, err := outImg.GetList(&arrImg, claims.UserId, enums.OrigWhereEnum.Facefake, arr[i].ID, 0, 1, 100)
		if err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "数据照片失败")
			return
		}
		for j := 0; j < len(arrImg); j++ {
			if err := service.ImgService.DelOutImg(arrImg[j]); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "删除照片失败")
				return
			}
			if err := arrImg[j].Delete(); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "删除照片失败")
				return
			}
		}
	}
	if err := faceFake.Delete(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "删除照片组失败")
		return
	}

	result := make(map[string]interface{})

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "删除成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

package push

import (
	"crypto/sha1"
	"design-ai/utils/logger"
	"fmt"
	"github.com/gin-gonic/gin"
	"io/ioutil"
	"net/http"
	"sort"
)

func NotifyWx(c *gin.Context) {
	logger.Info("已经接收到微信回调v1.3")

	// 从 HTTP 请求头中获取加密算法类型和加密使用的证书序列号
	//nonce := c.Request.Header.Get("Wechatpay-Nonce")
	//serial := c.Request.Header.Get("Wechatpay-Serial")

	//logger.Info("nonce:", nonce, "  ", "serial:", serial)

	signature := c.Query("signature") //signature	微信加密签名，signature结合了开发者填写的token参数和请求中的timestamp参数、nonce参数。
	timestamp := c.Query("timestamp") //	时间戳
	nonce := c.Query("nonce")         //	随机数
	echostr := c.Query("echostr")     //	随机字符串

	token := "YM5jo1xNPjXfP3NCIFwAj84bK4XipL1z"
	//EncodingAESKey := "JahwaRusZ9T94Mr8ldkjIPLRFEIscFfnEwQCJTzBx93"

	logger.Info(signature, "  ", timestamp, "  ", nonce, "  ", echostr)

	if signature != "" { //token验证绑定url
		signatureCheck := calculateSignature(token, timestamp, nonce)
		logger.Info("check:", signature, "      ", signatureCheck)
		if signature == signatureCheck {
			c.String(http.StatusOK, echostr)
			return
		} else {
			logger.Error("token 验证失败")
			c.String(http.StatusOK, "token 验证失败")
			return
		}
	} else {
		body, _ := ioutil.ReadAll(c.Request.Body)
		logger.Info(c.Request.URL.Path, " body====", string(body), "===body")
	}
	//c.JSON(http.StatusOK, &wechat.V3NotifyRsp{Code: gopay.SUCCESS, Message: "成功"})
	logger.Info("返回success")
	c.String(http.StatusOK, "success")

}

func calculateSignature(token, timestamp, nonce string) string {
	// 将三个参数放入一个切片中
	parameters := []string{token, timestamp, nonce}
	// 对参数进行字典序排序
	sort.Strings(parameters)
	// 将排序后的参数拼接成一个字符串
	joined := parameters[0] + parameters[1] + parameters[2]

	// 计算 SHA-1 加密
	hash := sha1.New()
	hash.Write([]byte(joined))
	encrypted := hash.Sum(nil)

	// 将加密后的结果转换为16进制字符串
	signature := fmt.Sprintf("%x", encrypted)
	return signature
}

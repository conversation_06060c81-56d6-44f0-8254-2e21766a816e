package appleiap

import (
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/myhttp"
	"design-ai/utils/tools"
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
	"time"
)

//type payment struct {
//	Productid string `json:"productid"`
//	Quantity  int    `json:"quantity"`
//	Username  string `json:"username"`
//}
//
//type transaction struct {
//	Payment               payment   `json:"payment"`
//	TransactionDate       time.Time `json:"transactionDate"`
//	TransactionIdentifier string    `json:"transactionIdentifier"`
//	TransactionReceipt    string    `json:"transactionReceipt"`
//	TransactionState      int       `json:"transactionState"`
//	ErrMsg                string    `json:"errMsg"`
//}

type transaction struct {
	Payment struct {
		Productid string `json:"productid"`
		Quantity  string `json:"quantity"`
		Username  string `json:"username"`
	} `json:"payment"`
	TransactionDate       string `json:"transactionDate"`
	TransactionIdentifier string `json:"transactionIdentifier"`
	TransactionReceipt    string `json:"transactionReceipt"`
	TransactionState      string `json:"transactionState"`
	ErrMsg                string `json:"errMsg"`
}

type verifyReceiptApi struct {
}

type verifyReceiptReq struct {
	UserId          uint   `json:"user_id"`
	TransactionJson string `json:"transaction_json"`
	BRestore        bool   `json:"b_restore"`
}

//发起邀请
func (obj verifyReceiptApi) VerifyReceipt(c *gin.Context) {
	var code int
	var msg string
	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims.UserId <= 0 {
	//	errmsg.Abort(c, errmsg.FAIL, "请先登录")
	//	return
	//}

	tokenHeader := c.Request.Header.Get("Authorization")
	logger.Info("tokenHeader:", tokenHeader)
	claims, err1 := middleware.GetClaimsByToken(tokenHeader)
	if claims == nil || err1 != nil {
		logger.Error(claims, err1)
	}

	var oReq verifyReceiptReq

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		logger.Error(err)
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	//oReq.TransactionJson = `{"payment":{"productid":"roodesign_con_01","quantity":"1","username":"r2023042921175200000052"},"transactionDate":"2023-04-29 21:19:26","transactionIdentifier":"2000000322619913","transactionReceipt":"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","transactionState":"1"}`
	//oReq.T=`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`
	logger.Info("transaction_json:  ", oReq.TransactionJson)

	var trans transaction
	if err := json.Unmarshal([]byte(oReq.TransactionJson), &trans); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "序列化数据失败")
	}

	//var user model.User
	//if err := user.GetByID(claims.UserId); err != nil {
	//	logger.Error(err)
	//	errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
	//	return
	//}

	var recharge model.Recharge
	if err := recharge.GetByOutTradeNo(trans.Payment.Username); err != nil {
		logger.Error("trans.Payment.Username", trans.Payment.Username, "   ", err)
		errmsg.Abort(c, errmsg.FAIL, "查询订单信息失败")
		return
	}
	if err := recharge.SetAppleVerifyInfo(oReq.TransactionJson); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "更新订单信息失败")
		return
	}

	song := make(map[string]interface{})
	song["receipt-data"] = trans.TransactionReceipt
	song["password"] = "ab2f43217ef1412eace3771e5526362a"

	url := "https://buy.itunes.apple.com/verifyReceipt"
	if config.AppMode == "debug" { //|| recharge.ProductCategory == enums.ProductCategoryEnum.Subscription
		url = "https://sandbox.itunes.apple.com/verifyReceipt"
	}
	resString, err := myhttp.Post(url, song)
	if len(resString) < 50 {
		logger.Info("切换验证URL:", resString)
		url = "https://sandbox.itunes.apple.com/verifyReceipt"
		resString, err = myhttp.Post(url, song)
	}
	logger.Info("resString:", resString) // resString:{"status":21007}
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "验证订单信息出错")
		return
	}
	mapReceipt := make(map[string]interface{})
	if err := json.Unmarshal([]byte(resString), &mapReceipt); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "序列化订单信息出错")
		return
	}
	success := false
	if val, ok := mapReceipt["status"].(float64); ok {
		if val == 0 {
			success = true
		}
	} else {
		success = false
	}
	logger.Info("success:", success)

	if success == false {
		errmsg.Abort(c, errmsg.FAIL, "充值失败，状态不正确")
		return
	}

	if recharge.ProductCategory == enums.ProductCategoryEnum.Subscription { //订阅信息处理逻辑
		logger.Info("开始处理订阅逻辑")
		latestReceiptInfo, ok := mapReceipt["latest_receipt_info"].([]interface{})
		if !ok {
			errmsg.Abort(c, errmsg.FAIL, "订阅失败，获取订阅信息出错")
			return
		} else {
			logger.Info("latestReceiptInfo len：", len(latestReceiptInfo))
		}

		if err := AnalyzeLatestReceiptInfo(recharge.UserId, recharge.PayChannel, recharge.ProductId, mapReceipt["receipt"].(map[string]interface{}), latestReceiptInfo); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "订阅失败，设置订阅信息出错")
			return
		} else {
			logger.Info("AnalyzeLatestReceiptInfo success")
		}
		//c.JSON(http.StatusOK, gin.H{
		//	"code": errmsg.SUCCESS,
		//	"msg":  "订阅成功",
		//})
		//return
	}

	tradeId := ""
	purchaseDate := time.Time{}
	expiresDate := time.Time{}
	inApp, ok := mapReceipt["receipt"].(map[string]interface{})["in_app"].([]interface{})
	if ok {
		for _, app := range inApp {
			appMap := app.(map[string]interface{})
			if appMap["transaction_id"].(string) == trans.TransactionIdentifier {
				tradeId = trans.TransactionIdentifier
				purchaseDate = tools.ParseTimeToLocation(appMap["purchase_date"].(string))
				if appMap["in_app_ownership_type"].(string) != "PURCHASED" {
					logger.Error("充值失败，票据订单状态不正确", resString)
					errmsg.Abort(c, errmsg.FAIL, "充值失败，票据状态不正确")
					return
				}
				if expiresDateStr, expiresDateOk := appMap["expires_date"].(string); expiresDateOk {
					expiresDate = tools.ParseTimeToLocation(expiresDateStr)
				}
				break
			}
		}
	}
	if tradeId == "" {
		logger.Error("充值失败，订单号不匹配")
		errmsg.Abort(c, errmsg.FAIL, "充值失败，订单号不匹配")
		return
	}

	if bExist, _ := recharge.ExistsPayTradeId(enums.PayChannelEnum.AppleIap, tradeId); bExist {
		var checkRecharge model.Recharge
		if err := checkRecharge.GetByPayTradeId(enums.PayChannelEnum.AppleIap, tradeId); err != nil {
			logger.Error("GetByPayTradeId payType:", enums.PayChannelEnum.AppleIap, "  tradeId:", tradeId)
			errmsg.Abort(c, errmsg.FAIL, "充值失败，该订单号已存在")
		} else {
			if checkRecharge.OutTradeNo == recharge.OutTradeNo && checkRecharge.State == 0 {
				//同一条记录，不处理
			} else {
				logger.Error("ExistsPayTradeId payType:", enums.PayChannelEnum.AppleIap, "  tradeId:", tradeId)
				errmsg.Abort(c, errmsg.FAIL, "充值失败，该订单号已存在")
				return
			}
		}
	}

	if tools.IsInitTime(purchaseDate) {
		logger.Error("充值失败，时间转换出错", purchaseDate)
		errmsg.Abort(c, errmsg.FAIL, "充值失败，时间转换出错")
		return
	}

	recharge.PayTradeId = tradeId
	recharge.PayTime = purchaseDate
	recharge.PayCallbackJson = resString
	if recharge.ProductCategory == enums.ProductCategoryEnum.Subscription {
		recharge.ExpiresDate = expiresDate
	}
	if err := model.Transactions.RechargeSuccess(&recharge); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "充值失败，订单处理失败")
		return
	} else {
		msg = "充值成功"
		if recharge.ProductCategory == enums.ProductCategoryEnum.Subscription {
			msg = "订阅成功"
		}
	}

	result := make(map[string]interface{})
	result["coin"] = recharge.Coin

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func AnalyzeLatestReceiptInfo(userId uint, channel string, productId uint, receiptMap map[string]interface{}, list []interface{}) error {

	m := make(map[string]interface{})
	for i := 0; i < len(list); i++ {
		item := list[i].(map[string]interface{})
		key := item["product_id"].(string)
		if _, ok := m[key]; !ok {
			m[key] = item
		}
	}

	var user model.User
	if err := user.GetById(userId); err != nil {
		logger.Error(err)
		return err
	}

	memberExpires := user.MemberExpires
	memberType := 0
	for k := range m {
		item := m[k].(map[string]interface{})

		quantity, _ := strconv.Atoi(item["quantity"].(string))
		is_trial_period, _ := strconv.ParseBool(item["is_trial_period"].(string))
		is_in_intro_offer_period, _ := strconv.ParseBool(item["is_in_intro_offer_period"].(string))

		expires_date := tools.ParseTimeToLocation(item["expires_date"].(string))

		receipt := model.Receipt{
			UserId:                      userId,
			PayChannel:                  channel,
			ProductId:                   productId,
			ProductCode:                 item["product_id"].(string),
			BundleID:                    receiptMap["bundle_id"].(string),
			ReceiptType:                 receiptMap["receipt_type"].(string),
			ReceiptCreationDate:         tools.ParseTimeToLocation(receiptMap["receipt_creation_date"].(string)),
			RequestDate:                 tools.ParseTimeToLocation(receiptMap["request_date"].(string)),
			Quantity:                    quantity,
			TransactionId:               item["transaction_id"].(string),
			OriginalTransactionId:       item["original_transaction_id"].(string),
			PurchaseDate:                tools.ParseTimeToLocation(item["purchase_date"].(string)),
			OriginalPurchaseDate:        tools.ParseTimeToLocation(item["original_purchase_date"].(string)),
			ExpiresDate:                 tools.ParseTimeToLocation(item["expires_date"].(string)),
			WebOrderLineItemId:          item["web_order_line_item_id"].(string),
			IsTrialPeriod:               is_trial_period,
			IsInIntroOfferPeriod:        is_in_intro_offer_period,
			InAppOwnershipType:          item["in_app_ownership_type"].(string),
			SubscriptionGroupIdentifier: item["subscription_group_identifier"].(string),
		}
		exist, err := receipt.Exists(receipt.BundleID, receipt.TransactionId)
		if err != nil {
			logger.Error(err)
			return err
		}
		if !exist {
			if err := receipt.Save(); err != nil {
				logger.Error(err)
				return err
			}
		}
		if receipt.InAppOwnershipType == "PURCHASED" {
			if expires_date.After(memberExpires) {
				if receipt.ProductCode == "roodesign_sub_01" {
					memberExpires = expires_date
					memberType = enums.MemberTypeEnum.Primary
				} else if receipt.ProductCode == "roodesign_sub_01" {
					memberExpires = expires_date
					memberType = enums.MemberTypeEnum.Advanced
				}
			}
		}
	}

	if memberExpires.After(user.MemberExpires) {
		if memberType <= 0 {
			err := errors.New("会员类型不正确")
			logger.Error(err)
			return err
		}
		//if err := user.SetMemberExpires( memberType, memberExpires); err != nil {
		//	logger.Error(err)
		//	return err
		//}
	}

	return nil
}

var VerifyReceiptApi verifyReceiptApi

type T struct {
	Receipt struct {
		ReceiptType                string `json:"receipt_type"`
		AdamId                     int    `json:"adam_id"`
		AppItemId                  int    `json:"app_item_id"`
		BundleId                   string `json:"bundle_id"`
		ApplicationVersion         string `json:"application_version"`
		DownloadId                 int    `json:"download_id"`
		VersionExternalIdentifier  int    `json:"version_external_identifier"`
		ReceiptCreationDate        string `json:"receipt_creation_date"`
		ReceiptCreationDateMs      string `json:"receipt_creation_date_ms"`
		ReceiptCreationDatePst     string `json:"receipt_creation_date_pst"`
		RequestDate                string `json:"request_date"`
		RequestDateMs              string `json:"request_date_ms"`
		RequestDatePst             string `json:"request_date_pst"`
		OriginalPurchaseDate       string `json:"original_purchase_date"`
		OriginalPurchaseDateMs     string `json:"original_purchase_date_ms"`
		OriginalPurchaseDatePst    string `json:"original_purchase_date_pst"`
		OriginalApplicationVersion string `json:"original_application_version"`
		InApp                      []struct {
			Quantity                string `json:"quantity"`
			ProductId               string `json:"product_id"`
			TransactionId           string `json:"transaction_id"`
			OriginalTransactionId   string `json:"original_transaction_id"`
			PurchaseDate            string `json:"purchase_date"`
			PurchaseDateMs          string `json:"purchase_date_ms"`
			PurchaseDatePst         string `json:"purchase_date_pst"`
			OriginalPurchaseDate    string `json:"original_purchase_date"`
			OriginalPurchaseDateMs  string `json:"original_purchase_date_ms"`
			OriginalPurchaseDatePst string `json:"original_purchase_date_pst"`
			IsTrialPeriod           string `json:"is_trial_period"`
			InAppOwnershipType      string `json:"in_app_ownership_type"`
		} `json:"in_app"`
	} `json:"receipt"`
	Environment string `json:"environment"`
	Status      int    `json:"status"`
}

//{
//"receipt": {
//"receipt_type": "ProductionSandbox",
//"adam_id": 0,
//"app_item_id": 0,
//"bundle_id": "com.cyuai.aigc",
//"application_version": "1",
//"download_id": 0,
//"version_external_identifier": 0,
//"receipt_creation_date": "2023-03-09 15:03:13 Etc/GMT",
//"receipt_creation_date_ms": "1678374193000",
//"receipt_creation_date_pst": "2023-03-09 07:03:13 America/Los_Angeles",
//"request_date": "2023-03-10 01:31:46 Etc/GMT",
//"request_date_ms": "1678411906483",
//"request_date_pst": "2023-03-09 17:31:46 America/Los_Angeles",
//"original_purchase_date": "2013-08-01 07:00:00 Etc/GMT",
//"original_purchase_date_ms": "1375340400000",
//"original_purchase_date_pst": "2013-08-01 00:00:00 America/Los_Angeles",
//"original_application_version": "1.0",
//"in_app": [{
//"quantity": "1",
//"product_id": "10006",
//"transaction_id": "2000000293537426",
//"original_transaction_id": "2000000293537426",
//"purchase_date": "2023-03-09 11:56:09 Etc/GMT",
//"purchase_date_ms": "1678362969000",
//"purchase_date_pst": "2023-03-09 03:56:09 America/Los_Angeles",
//"original_purchase_date": "2023-03-09 11:56:09 Etc/GMT",
//"original_purchase_date_ms": "1678362969000",
//"original_purchase_date_pst": "2023-03-09 03:56:09 America/Los_Angeles",
//"is_trial_period": "false",
//"in_app_ownership_type": "PURCHASED"
//},
//{
//"quantity": "1",
//"product_id": "10088",
//"transaction_id": "2000000293542165",
//"original_transaction_id": "2000000293542165",
//"purchase_date": "2023-03-09 12:02:06 Etc/GMT",
//"purchase_date_ms": "1678363326000",
//"purchase_date_pst": "2023-03-09 04:02:06 America/Los_Angeles",
//"original_purchase_date": "2023-03-09 12:02:06 Etc/GMT",
//"original_purchase_date_ms": "1678363326000",
//"original_purchase_date_pst": "2023-03-09 04:02:06 America/Los_Angeles",
//"is_trial_period": "false",
//"in_app_ownership_type": "PURCHASED"
//}
//]
//},
//"environment": "Sandbox",
//"status": 0
//}

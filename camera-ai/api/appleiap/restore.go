package appleiap

import (
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/myhttp"
	"design-ai/utils/tools"
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"strconv"
)

func Restore(c *gin.Context) {
	var code int
	var msg string

	tokenHeader := c.Request.Header.Get("Authorization")
	//logger.Info("tokenHeader:", tokenHeader)
	claims, err1 := middleware.GetClaimsByToken(tokenHeader)
	if claims == nil || err1 != nil {
		logger.Error(claims, err1)
		errmsg.Abort(c, errmsg.FAIL, "请登录账号再进行恢复购买")
		return
	}

	var oReq verifyReceiptReq

	err := c.Should<PERSON>ind<PERSON>(&oReq)
	if err != nil {
		logger.Error(err)
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	//oReq.TransactionJson = `{"payment":{"productid":"roodesign_con_01","quantity":"1","username":"r2023042921175200000052"},"transactionDate":"2023-04-29 21:19:26","transactionIdentifier":"2000000322619913","transactionReceipt":"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","transactionState":"1"}`

	logger.Info("transaction_json:  ", oReq.TransactionJson)

	/*
			test14 := `
		    {
		        "payment": {
		            "productid": "roodesign_sub_01",
		            "quantity": "1"
		        },
		        "transactionDate": "2023-04-30 08:37:03",
		        "transactionIdentifier": "2000000325898122",
		        "transactionReceipt": "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",
		        "transactionState": "3"
		    }
		`

			test1 := `    {
		        "payment": {
		            "productid": "roodesign_sub_01",
		            "quantity": "1"
		        },
		        "transactionDate": "2023-04-30 02:37:03",
		        "transactionIdentifier": "2000000325898110",
		        "transactionReceipt": "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",
		        "transactionState": "3"
		    }`
			if test1 != "" {

			}

			oReq.TransactionJson = test14
	*/

	var trans transaction
	if err := json.Unmarshal([]byte(oReq.TransactionJson), &trans); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "序列化数据失败")
	}

	//var user model.User
	//if err := user.GetByID(claims.UserId); err != nil {
	//	logger.Error(err)
	//	errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
	//	return
	//}

	song := make(map[string]interface{})
	song["receipt-data"] = trans.TransactionReceipt
	song["password"] = "ab2f43217ef1412eace3771e5526362a"

	url := "https://buy.itunes.apple.com/verifyReceipt"
	if config.AppMode == "debug" { //|| recharge.ProductCategory == enums.ProductCategoryEnum.Subscription
		url = "https://sandbox.itunes.apple.com/verifyReceipt"
	}
	resString, err := myhttp.Post(url, song)
	if len(resString) < 50 {
		logger.Info("切换验证URL:", resString)
		url = "https://sandbox.itunes.apple.com/verifyReceipt"
		resString, err = myhttp.Post(url, song)
	}
	logger.Info("resString:", resString) // resString:{"status":21007}
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "验证订单信息出错")
		return
	}
	mapReceipt := make(map[string]interface{})
	if err := json.Unmarshal([]byte(resString), &mapReceipt); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "序列化订单信息出错")
		return
	}
	success := false
	if val, ok := mapReceipt["status"].(float64); ok {
		if val == 0 {
			success = true
		}
	} else {
		success = false
	}

	if success == false {
		errmsg.Abort(c, errmsg.FAIL, "充值失败，状态不正确")
		return
	}

	latestReceiptInfo, _ := mapReceipt["latest_receipt_info"].([]interface{})

	if err := AnalyzeAndSaveReceipt(claims.UserId, mapReceipt["receipt"].(map[string]interface{}), latestReceiptInfo); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "恢复购买失败，分析票据出错")
		return
	}

	result := make(map[string]interface{})

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "恢复购买完成",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func AnalyzeAndSaveReceipt(userId uint, receiptMap map[string]interface{}, latestReceiptInfo []interface{}) error {

	if latestReceiptInfo == nil {
		//inApp, ok := receiptMap["in_app"].([]interface{})
		//if !ok {
		//	err := errors.New("in_app字段信息获取失败")
		//	logger.Error(err)
		//	return err
		//}
		//latestReceiptInfo = inApp
		err := errors.New("未获取到最新购买收据信息")
		return err
	}

	memberExpires := tools.DefaultTime()
	memberType := 0
	for _, val := range latestReceiptInfo {
		item := val.(map[string]interface{})

		quantity, _ := strconv.Atoi(item["quantity"].(string))
		is_trial_period, _ := strconv.ParseBool(item["is_trial_period"].(string))
		is_in_intro_offer_period, _ := strconv.ParseBool(item["is_in_intro_offer_period"].(string))

		expires_date := tools.ParseTimeToLocation(item["expires_date"].(string))

		var product model.RechargeProduct
		if err := product.GetByProductCode(item["product_id"].(string)); err != nil {
			logger.Error(err)
			return err
		}
		receipt := model.Receipt{
			UserId:                      userId,
			PayChannel:                  enums.PayChannelEnum.AppleIap,
			ProductId:                   product.ID,
			ProductCode:                 item["product_id"].(string),
			BundleID:                    receiptMap["bundle_id"].(string),
			ReceiptType:                 receiptMap["receipt_type"].(string),
			ReceiptCreationDate:         tools.ParseTimeToLocation(receiptMap["receipt_creation_date"].(string)),
			RequestDate:                 tools.ParseTimeToLocation(receiptMap["request_date"].(string)),
			Quantity:                    quantity,
			TransactionId:               item["transaction_id"].(string),
			OriginalTransactionId:       item["original_transaction_id"].(string),
			PurchaseDate:                tools.ParseTimeToLocation(item["purchase_date"].(string)),
			OriginalPurchaseDate:        tools.ParseTimeToLocation(item["original_purchase_date"].(string)),
			ExpiresDate:                 tools.ParseTimeToLocation(item["expires_date"].(string)),
			WebOrderLineItemId:          item["web_order_line_item_id"].(string),
			IsTrialPeriod:               is_trial_period,
			IsInIntroOfferPeriod:        is_in_intro_offer_period,
			InAppOwnershipType:          item["in_app_ownership_type"].(string),
			SubscriptionGroupIdentifier: item["subscription_group_identifier"].(string),
		}
		if receipt.InAppOwnershipType != "PURCHASED" {
			logger.Error("receipt.InAppOwnershipType != \"PURCHASED\" 不保存Receipt")
			continue
		}
		var checkReceipt model.Receipt
		if err := checkReceipt.GetByTransactionId(receipt.TransactionId); err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := receipt.Save(); err != nil {
					logger.Error(err)
					return err
				}
			} else {
				logger.Error(err)
				return err
			}
		}

		if err := checkReceipt.GetByTransactionId(receipt.TransactionId); err != nil {
			logger.Error(err)
			return err
		}

		if tools.IsInitTime(checkReceipt.HandleTime) {
			if err := model.Transactions.SubscriptionAddCoin(&checkReceipt); err != nil {
				logger.Error(err)
			}
		}
		if receipt.InAppOwnershipType == "PURCHASED" {
			if expires_date.After(memberExpires) {
				if receipt.ProductCode == "roodesign_sub_01" {
					memberExpires = expires_date
					memberType = enums.MemberTypeEnum.Primary
				} else if receipt.ProductCode == "roodesign_sub_01" {
					memberExpires = expires_date
					memberType = enums.MemberTypeEnum.Advanced
				}
			}
		}
		if memberType > 0 {
			var user model.User
			if err := user.GetById(userId); err != nil {
				logger.Error(err)
			}
			if memberExpires.After(user.MemberExpires) {
				if err := user.SetMemberExpires(nil, memberType, memberExpires); err != nil {
					logger.Error(err)
				}
			}
		}
	}

	return nil
}

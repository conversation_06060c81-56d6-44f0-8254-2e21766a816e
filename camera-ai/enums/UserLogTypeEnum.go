package enums

import "reflect"

type userLogTypeEnum_ struct {
	Other, Reg, Login, Open, WechatpayNotify, ImportantError int
}

var UserLogTypeEnum = userLogTypeEnum_{
	Other:           0, //其他
	Reg:             1, //注册
	Login:           2, //登录
	Open:            3, //打开应用
	WechatpayNotify: 4, //微信支付回调信息
	ImportantError:  5, //重要错误
}

func (c userLogTypeEnum_) GetKey(value int) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if vo.Field(i).Interface().(int) == value {
			return typeVo.Field(i).Name
		}
	}
	return ""
}

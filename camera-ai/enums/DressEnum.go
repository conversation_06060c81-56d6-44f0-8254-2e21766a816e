package enums

import "reflect"

type dressEnum_ struct {
	Normal, Suit, WhiteShirt, Career string
}

var DressEnum = dressEnum_{
	Normal:     "",
	Suit:       "西装",
	WhiteShirt: "白衬衫",
	Career:     "职场",
}

func (c dressEnum_) GetKey(value string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if vo.Field(i).Interface().(string) == value {
			return typeVo.Field(i).Name
		}
	}
	return ""
}

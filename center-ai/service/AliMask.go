package service

import (
	config2 "center-ai/utils/config"
	"center-ai/utils/logger"
	"center-ai/utils/myimg"
	"center-ai/utils/tools"
	"errors"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	imageseg20191230 "github.com/alibabacloud-go/imageseg-20191230/v2/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"image"
	"os"
)

type alimask_ struct {
}

var AliMask alimask_

func (obj alimask_) Analyze(originImgPath string, maskImgPath string) (string, error) {
	// 场景一，使用本地文件
	file1, err := os.Open(originImgPath)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	defer file1.Close()

	file2, err := os.Open(maskImgPath)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	defer file2.Close()

	refineMaskAdvanceRequest := &imageseg20191230.RefineMaskAdvanceRequest{
		ImageURLObject:     file1,
		MaskImageURLObject: file2,
	}
	//logger.Info(refineMaskAdvanceRequest)

	// 创建AccessKey ID和AccessKey Secret，请参考https://help.aliyun.com/document_detail/175144.html。
	// 如果您用的是RAM用户的AccessKey，还需要为RAM用户授予权限AliyunVIAPIFullAccess，请参考https://help.aliyun.com/document_detail/145025.html。
	// 从环境变量读取配置的AccessKey ID和AccessKey Secret。运行示例前必须先配置环境变量。
	//accessKeyId := os.Getenv("ALIBABA_CLOUD_ACCESS_KEY_ID")
	//accessKeySecret := os.Getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET")
	// 初始化配置对象 &openapi.Config。Config对象存放AccessKeyId、AccessKeySecret、Endpoint等配置。
	config := &openapi.Config{
		AccessKeyId:     tea.String(config2.AccessKeyId),
		AccessKeySecret: tea.String(config2.AccessKeySecret),
	}
	// 访问的域名
	config.Endpoint = tea.String("imageseg.cn-shanghai.aliyuncs.com")
	client, err := imageseg20191230.NewClient(config)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	runtime := &util.RuntimeOptions{}
	refineMaskAdvanceResponse, err := client.RefineMaskAdvance(refineMaskAdvanceRequest, runtime)
	if err != nil {
		// 获取整体报错信息
		logger.Error(err)
		return "", err
	}
	if len(refineMaskAdvanceResponse.Body.Data.Elements) <= 0 {
		logger.Error(errors.New("数组为0"))
		return "", errors.New("数组为0")
	}
	imgUrl := refineMaskAdvanceResponse.Body.Data.Elements[0].ImageURL

	return *imgUrl, nil
}

func (obj alimask_) MaskUp(absoluteImgPath string, absoluteMaskImgPath string) (image.Image, error) {
	maskUpUrl, err := AliMask.Analyze(absoluteImgPath, absoluteMaskImgPath)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	maskUpBase64, err := tools.GetImgBase64FromUrl(maskUpUrl)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	maskUpImg, err := myimg.Base64ToImg(maskUpBase64)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	return maskUpImg, nil
}

func (obj alimask_) SegmentHDBody(imgPath string) (string, error) {
	// 场景一，使用本地文件
	file1, err := os.Open(imgPath)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	defer file1.Close()

	segmentHDBodyAdvanceRequest := &imageseg20191230.SegmentHDBodyAdvanceRequest{
		ImageURLObject: file1,
	}

	//logger.Info(refineMaskAdvanceRequest)

	// 创建AccessKey ID和AccessKey Secret，请参考https://help.aliyun.com/document_detail/175144.html。
	// 如果您用的是RAM用户的AccessKey，还需要为RAM用户授予权限AliyunVIAPIFullAccess，请参考https://help.aliyun.com/document_detail/145025.html。
	// 从环境变量读取配置的AccessKey ID和AccessKey Secret。运行示例前必须先配置环境变量。
	//accessKeyId := os.Getenv("ALIBABA_CLOUD_ACCESS_KEY_ID")
	//accessKeySecret := os.Getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET")
	// 初始化配置对象 &openapi.Config。Config对象存放AccessKeyId、AccessKeySecret、Endpoint等配置。
	config := &openapi.Config{
		AccessKeyId:     tea.String(config2.AccessKeyId),
		AccessKeySecret: tea.String(config2.AccessKeySecret),
	}
	// 访问的域名
	config.Endpoint = tea.String("imageseg.cn-shanghai.aliyuncs.com")
	client, err := imageseg20191230.NewClient(config)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	runtime := &util.RuntimeOptions{}
	segmentHDBodyResponse, err := client.SegmentHDBodyAdvance(segmentHDBodyAdvanceRequest, runtime)
	if err != nil {
		// 获取整体报错信息
		logger.Error(err)
		return "", err
	}
	if *segmentHDBodyResponse.Body.Data.ImageURL == "" {
		logger.Error(errors.New("提取失败"))
		return "", errors.New("提取失败")
	}
	imgUrl := *segmentHDBodyResponse.Body.Data.ImageURL

	return imgUrl, nil
}

package service

import (
	"center-ai/enums"
	"center-ai/model"
	"center-ai/utils/config"
	"center-ai/utils/logger"
	"center-ai/utils/myimg"
	"center-ai/utils/myredis"
	"center-ai/utils/tools"
	"errors"
	"fmt"
	"strings"
	"time"
)

type shopshow_ struct {
}

var ShopShowService shopshow_

func (d *shopshow_) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("ShopShowService奔溃:", e)
		}
	}()
	logger.Info("ShopShowService.Run 开始循环获取图片")
	for {
		if RunPause {
			RunTask[0] = true
			logger.Info("RunPause 延迟2秒")
			time.Sleep(time.Second * 2)
			continue
		}
		value, err := d.<PERSON>son()

		if err != nil {
			logger.Error(value, err)
			continue
		}
		if value == "" {
			continue
		}
		logger.Info("接收到绘图json:", value)
		if err := d.UpdateToImg(value); err != nil {
			logger.Error(err)
		}
	}
}

func (d *shopshow_) UpdateToImg(value string) error {

	if value == "" {
		logger.Error("数据为空")
		return errors.New("数据为空")
	}
	outImgPath := ""
	outImgMd5 := ""
	mSdOutput := tools.GetMapFromJson(value)

	if val, ok := mSdOutput["error_info"]; ok && val.(string) != "" {
		mResult := tools.GetMapFromJson(val.(string))
		if path, exists := mResult["out_image_path"]; exists {
			outImgPath = path.(string)
		}
	}

	if val, ok := mSdOutput["result"]; ok && val.(string) != "" {
		mResult := tools.GetMapFromJson(val.(string))
		if path, exists := mResult["out_image_path"]; exists {
			outImgPath = path.(string)
		}
	} else {
		logger.Error(" key result  is not exist!", value)
	}

	if md5, ok := mSdOutput["custom_md5"]; ok {
		outImgMd5 = md5.(string)
	}

	if outImgMd5 == "" {
		err := errors.New("path字段不为空")
		logger.Error(err, outImgMd5)
		return err
	}
	var outImg model.ShopOut
	if err := outImg.GetByMd5(outImgMd5); err != nil {
		logger.Error(err)
		return err
	}

	if outImgPath == "" { //绘图出错处理
		if err := outImg.SetInfo(value); err != nil {
			logger.Error(err)
		}
		d.RemoveProgress(outImgMd5)
		return nil
	}

	img, _, err := myimg.FileToImg(config.DiffusionFilePath + outImgPath)
	if err != nil {
		logger.Error(err)
		return err
	}
	if img == nil {
		logger.Error("图片不存在", value)
		return fmt.Errorf("图片不存在")
	}

	if outImg.Path == "" {
		outImg.Path = outImgPath
		if warn, err := ShopOrigin.CheckSdGen(outImg); err != nil {
			logger.Error(err)
		} else {
			if warn != nil && len(warn) > 0 {
				mSdOutput["warning"] = warn
				value = tools.GetJsonFromStruct(mSdOutput)
			}
		}

		//logger.Info("outputData.SisPath:", outputData.SisPath)
		//if err := outImg.SetPathAndWidth(outImgPath, img.Bounds().Size().X, img.Bounds().Size().Y); err != nil {
		if err := outImg.SetPathAndInfo(outImgPath, value, img.Bounds().Size().X, img.Bounds().Size().Y); err != nil {
			logger.Error(err)
			return err
		}
	}
	d.RemoveProgress(outImgMd5)

	var shopOrigin model.ShopOrigin
	if outImg.OrigWhere == enums.OrigWhereEnum.Shopshow {
		logger.Info("开始合并图片：", shopOrigin.OriginMd5, "    ", outImg.Md5)
		if err := shopOrigin.GetById(outImg.OrigId); err != nil {
			logger.Error(err)
			return err
		}
		if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBigHead {
			originAbsolutePath := config.DiffusionFilePath + shopOrigin.OriginImg

			originImg, _, err := myimg.FileToImg(originAbsolutePath)
			if err != nil {
				logger.Error(err, originAbsolutePath)
			}

			resizeImg := myimg.ResizeImg(shopOrigin.CropWidth, shopOrigin.CropHeight, img, false)
			mergeImg := myimg.MergeImg(originImg, resizeImg, shopOrigin.CropX, shopOrigin.CropY)

			mergePath := ImgPath.GetSdMergePath(outImgPath)
			mergeAbsolutePath := config.DiffusionFilePath + mergePath
			//if err := myimg.ImgToPngFile(mergeImg, mergeAbsolutePath); err != nil {
			//	logger.Error(err)
			//} else {
			//	if err1 := outImg.SetMergePath(mergePath); err1 != nil {
			//		logger.Error(err1, mergePath)
			//	}
			//}

			chunk := myimg.TEXtChunk{
				Key:   "parameters",
				Value: fmt.Sprintf("MadeIn: cyuai, SN: %s", outImg.Md5),
			}
			if er := myimg.WriteTextChunkToPng1(mergeImg, mergeAbsolutePath, chunk); er != nil {
				logger.Error(er)
				return er
			} else {
				if err1 := outImg.SetMergePath(mergePath); err1 != nil {
					logger.Error(err1, mergePath)
					return err1
				}
			}

			//if chunks, err := myimg.ReadTextChunksFromPng(config.DiffusionFilePath + outImgPath); err == nil {
			//	if len(chunks) > 0 && chunks[0].Key == "parameters" {
			//		chunks[0].Value = chunks[0].Value + fmt.Sprintf("\nMadeType: %d, ShopFaceId: %d, UserId: %d, OutMd5: %s", outImg.MadeType, outImg.ShopFaceId, shopOrigin.UserId, outImg.Md5)
			//		if er := myimg.WriteTextChunkToPng1(mergeImg, mergeAbsolutePath, chunks[0]); er != nil {
			//			logger.Error(err)
			//		}
			//	}
			//	if err1 := outImg.SetMergePath(mergePath); err1 != nil {
			//		logger.Error(err1, mergePath)
			//	}
			//} else {
			//	logger.Error(err)
			//}

		} else if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBigHeadAndBackColor {
			if outImgPath == ImgPath.GetSdMergeSdBgPath(outImg.Path) { //换背景色处理
				if err1 := outImg.SetMergePath(outImgPath); err1 != nil {
					logger.Error(err1, outImgPath)
					return err1
				}
				logger.Info("重置merge图片路径为更换了背景色的路径")
			} else {
				if err := ShopOrigin.MergeOutputImg(outImg.OrigId, outImg.Md5); err != nil { //合并换头图片处理
					logger.Error(err)
				}
			}
		} else if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeSence {
			if b, _ := tools.PathFileExists(config.DiffusionFilePath + ImgPath.GetOriginBodyPath1(shopOrigin.OriginImg)); b {
				destBodyImg, _, err := myimg.FileToImg(config.DiffusionFilePath + ImgPath.GetDestBodyPath(shopOrigin.OriginImg))
				if err != nil {
					logger.Error(err)
				} else {
					mergeImg := myimg.MergeImg(img, destBodyImg, 0, 0)
					mergePath := ImgPath.GetSdMergePath(outImgPath)
					mergeAbsolutePath := config.DiffusionFilePath + mergePath

					chunk := myimg.TEXtChunk{
						Key:   "parameters",
						Value: fmt.Sprintf("MadeIn: cyuai, SN: %s", outImg.Md5),
					}
					if er := myimg.WriteTextChunkToPng1(mergeImg, mergeAbsolutePath, chunk); er != nil {
						logger.Error(er)
						return er
					} else {
						if err1 := outImg.SetMergePath(mergePath); err1 != nil {
							logger.Error(err1, mergePath)
							return err1
						}
					}
				}

			} else {
				logger.Error("主体图不存在", ImgPath.GetOriginBodyPath1(shopOrigin.OriginImg))
			}
		}
		logger.Info("合并图片完成：", shopOrigin.OriginMd5, "    ", outImg.Md5)
	}
	return nil
}

func (d *shopshow_) PopJson() (string, error) {
	value, err := myredis.BRPop(enums.AigcRedisKeyEnum.SdApiPop)
	return value, err
}

func (d *shopshow_) RemoveProgress(imgMd5 string) error {
	if _, err := myredis.HDel(enums.AigcRedisKeyEnum.SdApiProgress, imgMd5); err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func (d *shopshow_) GetProgress(imgMd5 string) (float64, string) {
	logger.Info("开始获取绘图进度", imgMd5)
	if oldValue, err := myredis.HGet(enums.AigcRedisKeyEnum.SdApiProgress, imgMd5); err != nil {
		logger.Error(err, imgMd5)
	} else if oldValue != "" {
		logger.Info("开始获取绘图进度 oldValue:", oldValue, "   ", imgMd5)
		m := tools.GetMapFromJson(oldValue)
		if val, ok := m["sd_server"]; ok {
			sdServer := val.(string)
			return m["progress"].(float64), sdServer
		}
		if val, ok := m["progress"]; ok {
			oldProgress := val.(float64)
			return oldProgress, ""
		}
	} else {
		logger.Info("开始获取绘图进度 oldValue为空:", oldValue, "   ", imgMd5)
	}
	return -1, ""
}
func (d *shopshow_) GetProgressInTarget(sdServer string, imgMd5 string) (int64, error) { //在目标队列的位置
	ary := make([]string, 0)
	jsonStr, err := myredis.HGet(enums.AigcRedisKeyEnum.SdApiPushTarget, sdServer)
	if err != nil {
		logger.Error(err)
		return -1, err
	}
	if jsonStr == "" {
		return -1, nil
	}
	if err := tools.GetStructFromJson(&ary, jsonStr); err != nil {
		logger.Error(err)
		return -1, err
	}
	for i := 0; i < len(ary); i++ {
		if strings.Contains(ary[i], imgMd5) {
			return int64(i + 1), nil
		}
	}
	return -1, nil
}

func (d *shopshow_) GetProgressRank(imgMd5 string, pushJson string) int64 {
	logger.Info("开始获取绘图队列位置", imgMd5)
	zIndex, _ := myredis.LLen(enums.AigcRedisKeyEnum.SdApiPush)
	return zIndex
	//if zIndex, err := myredis.ZRevRank(enums.AigcRedisKeyEnum.SdApiPush, pushJson); err != nil {
	//	logger.Error(err, imgMd5)
	//	zIndex, _ = myredis.LLen(enums.AigcRedisKeyEnum.SdApiPush)
	//	return zIndex
	//} else {
	//	return zIndex + 1
	//}
	//return -1
}

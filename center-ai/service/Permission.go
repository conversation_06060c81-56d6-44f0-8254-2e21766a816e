package service

import (
	"center-ai/enums"
	"center-ai/model"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"encoding/json"
	"fmt"
	"strings"
)

type permission_ struct {
	RouterData         map[string]interface{}
	RolePermissionData map[string][]string
}

func (obj *permission_) LoadData() error {
	obj.RouterData = make(map[string]interface{}, 0)
	obj.RolePermissionData = make(map[string][]string, 0)

	var permssion model.Permission
	ary := permssion.GetList()
	for _, item := range ary {
		tmpAry := tools.GetStringAryFromJson(item.RouterPaths)
		var project model.Project
		bundle := project.GetBundleById(item.ProjectId)
		for _, tmpStr := range tmpAry {
			obj.RouterData[bundle+"_"+tmpStr] = item
		}
	}

	var role model.Role
	aryRole := role.GetListForLoad()
	for _, item := range aryRole {
		aryPermission := make([]map[string]interface{}, 0)
		if err := json.Unmarshal([]byte(item.Permissions), &aryPermission); err != nil {
			logger.Error(err)
			break
		}
		for _, permission := range aryPermission {
			permId := uint64(permission["perm_id"].(float64))
			permOperates := permission["perm_operates"].([]interface{})

			out := make([]string, 0)
			for _, v := range permOperates {
				// using a type assertion, convert v to a string
				out = append(out, v.(string))
			}
			obj.RolePermissionData[fmt.Sprintf("%d_%d", item.ID, permId)] = out
		}
	}
	return nil
}
func (obj *permission_) getRolePermOperates(roleId uint, permId uint) []string {
	operates := make([]string, 0)
	if _, ok := obj.RolePermissionData[fmt.Sprintf("%d_%d", roleId, permId)]; ok {
		operates = obj.RolePermissionData[fmt.Sprintf("%d_%d", roleId, permId)]
	}
	return operates
}

func (obj *permission_) IsAdmin(roleStr string) bool {
	roles := make([]string, 0)
	if err := tools.GetStructFromJson(&roles, roleStr); err != nil {
		logger.Error(err)
		return false
	}
	for _, str := range roles {
		if str == enums.PermissionEnum.Admin {
			return true
		}
	}
	return false
}

func (obj *permission_) GetRoleIds(roleStr string) []uint {
	rolesOut := make([]uint, 0)
	roles := make([]string, 0)
	if err := tools.GetStructFromJson(&roles, roleStr); err != nil {
		logger.Error(err)
		return rolesOut
	}
	for _, str := range roles {
		if num := tools.ParseUint(str); num > 0 {
			rolesOut = append(rolesOut, num)
		} else {
			logger.Error("不是数字：", str)
		}
	}
	return rolesOut
}

func (obj *permission_) GetPermissionId(bundle string, routePath string) uint {

	if _, ok := obj.RouterData[bundle+"_"+routePath]; ok {
		permission := obj.RouterData[bundle+"_"+routePath].(model.Permission)
		return permission.ID
	} else {
		logger.Error("routerUrl key不存在", bundle+"_"+routePath)
	}
	return 0
}

func (obj *permission_) GetUserRolePermOperates(userRoles string, bundle string, routerUrl string) []string {

	operates := make([]string, 0)
	roles := make([]string, 0)
	if err := tools.GetStructFromJson(&roles, userRoles); err != nil {
		logger.Error(err)
		return operates
	}

	if len(roles) == 1 {
		if roles[0] == enums.PermissionEnum.Admin {
			return []string{"all"}
		}
	}

	permissionId := obj.GetPermissionId(bundle, routerUrl)
	checkStr := ""
	for _, roleIdStr := range roles {
		if roleIdStr == enums.PermissionEnum.Admin {
			return []string{"all"}
		}
		roleId := tools.ParseUint(roleIdStr)
		tmpAry := obj.getRolePermOperates(roleId, permissionId)
		for _, tmp := range tmpAry {
			if tmp == "all" {
				return []string{"all"}
			}
			if !strings.Contains(checkStr, tmp) {
				operates = append(operates, tmp)
				checkStr += "," + tmp
			}
		}
	}
	return operates
}

/*
func (obj permission_) GetUserOperates(userId uint, bundle string, routerUrl string) []string {

	var user model.User
	operates := make([]string, 0)
	if err := user.GetById(userId); err != nil {
		logger.Error(err)
		return operates
	}

	roles := make([]string, 0)
	if err := tools.GetStructFromJson(&roles, user.Roles); err != nil {
		logger.Error(err)
		return operates
	}
	for _, str := range roles {
		if str == enums.PermissionEnum.Admin {
			operates = append(operates, "all")
			return operates
		}
	}

	if obj.IsAdmin(user.Roles) {
		operates = append(operates, "all")
		return operates
	}

	permissionId := obj.GetPermissionId(bundle, routerUrl)

	var rolePermission model.RolePermission
	for _, roleId := range obj.GetRoleIds(user.Roles) {
		operatesStr := rolePermission.GetPermissionOperates(roleId, permissionId)
		if operatesStr != "" {
			if err := tools.GetStructFromJson(&operates, operatesStr); err != nil {
				logger.Error(err)
			}
			return operates
		}
	}

	return operates
}*/

var Permission permission_

package service

import (
	"bytes"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"net/http"
	url2 "net/url"
	"strconv"
	"time"
)

type baota_ struct {
}

func (o *baota_) Test1() {
	// API接口地址
	//url := "http://117.187.188.4:2588/system?action=GetSystemTotal"
	url := "http://192.168.200.4:42130/system?action=GetSystemTotal"
	// 获取当前时间戳，单位为毫秒
	timestamp := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	// API接口加密字符串，需要在宝塔面板中生成
	//btSign := "kq5ZJjwaRSO6EYotv0821eckkCoA63sp"
	btSign := "RBbTmM5LJP17B6fvdnUqKnLMUi0VcMfD"
	// 计算MD5加密签名
	md5Sign := o.getMd5(btSign)
	// 构造请求参数
	requestParams := "request_time=" + timestamp + "&request_token=" + o.getMd5(timestamp+md5Sign)
	// 发送POST请求
	resp, err := http.Post(url, "application/x-www-form-urlencoded", bytes.NewReader([]byte(requestParams)))
	if err != nil {
		fmt.Println(err)
		return
	}
	defer resp.Body.Close()
	// 解析响应数据
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(string(respBody))
}

func (o *baota_) RestartProject(projectName string) map[string]interface{} {
	// API接口地址
	//url := "http://192.168.200.25:8888/project/go/restart_project"
	////url := "http://192.168.200.20:2588/project/go/restart_project"
	////url := "http://127.0.0.1:8888/project/go/restart_project"
	//if config.Env == enums.EnvEnum.DEV {
	//	url = "http://117.187.188.4:2588/project/go/restart_project"
	//}
	//url := "http://192.168.200.4:42130/project/go/restart_project"
	url := "http://10.20.100.201:18319/project/go/restart_project"
	logger.Info("开始重启项目：", projectName, " url  ", url)
	// 获取当前时间戳，单位为毫秒
	timestamp := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	// API接口加密字符串，需要在宝塔面板中生成
	//btSign := "kq5ZJjwaRSO6EYotv0821eckkCoA63sp"
	//btSign := "RBbTmM5LJP17B6fvdnUqKnLMUi0VcMfD"
	btSign := "ktgBWGo96RgkmmyxsY5Q0xw87DIdAYwA"
	// 计算MD5加密签名
	md5Sign := o.getMd5(btSign)
	// 构造请求参数
	requestParams := "request_time=" + timestamp + "&request_token=" + o.getMd5(timestamp+md5Sign)

	param := fmt.Sprintf(`{"project_name":"%s"}`, projectName)
	requestParams += "&data=" + url2.QueryEscape(param)
	//requestParams += "&data=%7B%22project_name%22%3A%22aigc_api_4005%22%7D"
	// 发送POST请求
	logger.Info("发送重启请求：", projectName, " url  ", url)
	resp, err := http.Post(url, "application/x-www-form-urlencoded", bytes.NewReader([]byte(requestParams)))
	if err != nil {
		logger.Error(err)
		return nil
	}
	defer resp.Body.Close()
	// 解析响应数据
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error(err)
		return nil
	}
	logger.Info("重启相应数据：", string(respBody))
	return tools.GetMapFromJson(string(respBody))
	//{"status": true, "status_code": 1, "error_msg": "", "data": "重启成功"}
}

func (o *baota_) RestartProject13(projectName string) map[string]interface{} {
	// API接口地址
	//url := "http://192.168.200.25:8888/project/go/restart_project"
	////url := "http://192.168.200.20:2588/project/go/restart_project"
	////url := "http://127.0.0.1:8888/project/go/restart_project"
	//if config.Env == enums.EnvEnum.DEV {
	//	url = "http://117.187.188.4:2588/project/go/restart_project"
	//}
	url := "http://192.168.200.13:17491/project/go/restart_project"
	logger.Info("开始重启项目：", projectName, " url  ", url)
	// 获取当前时间戳，单位为毫秒
	timestamp := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	// API接口加密字符串，需要在宝塔面板中生成
	//btSign := "kq5ZJjwaRSO6EYotv0821eckkCoA63sp"
	btSign := "5oZsbWhpkmG1mY4nETzqLfBaBPFmvh68"
	// 计算MD5加密签名
	md5Sign := o.getMd5(btSign)
	// 构造请求参数
	requestParams := "request_time=" + timestamp + "&request_token=" + o.getMd5(timestamp+md5Sign)

	param := fmt.Sprintf(`{"project_name":"%s"}`, projectName)
	requestParams += "&data=" + url2.QueryEscape(param)
	//requestParams += "&data=%7B%22project_name%22%3A%22aigc_api_4005%22%7D"
	// 发送POST请求
	logger.Info("发送重启请求：", projectName, " url  ", url)
	resp, err := http.Post(url, "application/x-www-form-urlencoded", bytes.NewReader([]byte(requestParams)))
	if err != nil {
		logger.Error(err)
		return nil
	}
	defer resp.Body.Close()
	// 解析响应数据
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error(err)
		return nil
	}
	logger.Info("重启相应数据：", string(respBody))
	return tools.GetMapFromJson(string(respBody))
	//{"status": true, "status_code": 1, "error_msg": "", "data": "重启成功"}
}

func (o *baota_) StartProject(projectName string) map[string]interface{} {
	// API接口地址
	//url := "http://192.168.200.25:8888/project/go/start_project"
	////url := "http://192.168.200.20:2588/project/go/restart_project"
	////url := "http://127.0.0.1:8888/project/go/restart_project"
	//if config.Env == enums.EnvEnum.DEV {
	//	url = "http://117.187.188.4:2588/project/go/start_project"
	//}
	//url := "http://192.168.200.4:42130/project/go/start_project"
	url := "http://10.20.100.201:18319/project/go/restart_project"
	logger.Info("开始重启项目：", projectName, " url  ", url)
	// 获取当前时间戳，单位为毫秒
	timestamp := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	// API接口加密字符串，需要在宝塔面板中生成
	//btSign := "kq5ZJjwaRSO6EYotv0821eckkCoA63sp"
	//btSign := "RBbTmM5LJP17B6fvdnUqKnLMUi0VcMfD"
	btSign := "ktgBWGo96RgkmmyxsY5Q0xw87DIdAYwA"

	// 计算MD5加密签名
	md5Sign := o.getMd5(btSign)
	// 构造请求参数
	requestParams := "request_time=" + timestamp + "&request_token=" + o.getMd5(timestamp+md5Sign)

	param := fmt.Sprintf(`{"project_name":"%s"}`, projectName)
	requestParams += "&data=" + url2.QueryEscape(param)
	//requestParams += "&data=%7B%22project_name%22%3A%22aigc_api_4005%22%7D"
	// 发送POST请求
	logger.Info("发送重启请求：", projectName, " url  ", url)
	resp, err := http.Post(url, "application/x-www-form-urlencoded", bytes.NewReader([]byte(requestParams)))
	if err != nil {
		logger.Error(err)
		return nil
	}
	defer resp.Body.Close()
	// 解析响应数据
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error(err)
		return nil
	}
	logger.Info("重启相应数据：", string(respBody))
	return tools.GetMapFromJson(string(respBody))
	//{"status": true, "status_code": 1, "error_msg": "", "data": "重启成功"}
}

// 计算MD5加密值
func (o *baota_) getMd5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// 计算MD5加密值
func (o *baota_) postUrl(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

var BaoTaService baota_

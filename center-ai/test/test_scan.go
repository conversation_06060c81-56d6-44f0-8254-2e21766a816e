package main

import (
	"fmt"
	"reflect"
	"testing"
)

type Source struct {
	Name     string
	Age      int
	Location string
}

type Target struct {
	Name     string
	Age      int
	Location string
}

func Scan(source interface{}, target interface{}) error {
	sourceValue := reflect.ValueOf(source)
	targetValue := reflect.ValueOf(target)

	for i := 0; i < sourceValue.NumField(); i++ {
		sourceField := sourceValue.Field(i)
		targetField := targetValue.Elem().Field(i)

		if !targetField.CanSet() {
			continue
		}

		targetField.Set(sourceField)
	}
	return nil
}

func TestSum(t *testing.T) {
	source := Source{"Alice", 30, "New York"}
	var target Target

	err := Scan(source, &target)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	fmt.Printf("Name: %s, Age: %d, Location: %s\n", target.Name, target.Age, target.Location)

}

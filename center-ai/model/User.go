package model

import (
	"center-ai/utils/logger"
	"errors"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
	"log"
	"time"
)

type User struct {
	gorm.Model
	Username      string    `json:"username" gorm:"type:varchar(20);not null"`
	Nickname      string    `json:"nickname" gorm:"type:varchar(20);not null"`
	Mobile        string    `json:"mobile" gorm:"type:varchar(20);not null"`
	Password      string    `json:"password" gorm:"type:varchar(100);not null;"`
	Roles         string    `json:"roles" gorm:"type:json;';comment:角色权限"`
	Avatar        string    `json:"avatar" gorm:"type:varchar(100);not null;default:'';comment:头像链接"`
	RegEnv        string    `json:"reg_env" gorm:"type:json;';comment:注册信息 对应UerEnv结构体"`
	LastLoginIp   string    `json:"last_login_ip" gorm:"type:varchar(50);not null;default:'';comment:最后登录Ip""`
	LastLoginTime time.Time `json:"last_login_time" gorm:"type:datetime;default:'1900-01-01';comment:最后登录时间"`
	Remark        string    `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	Token         string    `json:"token" gorm:"type:varchar(50);not null;comment:登录token"`
	Version       optimisticlock.Version
}

func (User) TableName() string {
	return "T_User"
}

func (o *User) GetById(id uint) error {
	return db.First(o, id).Error
}

func (o *User) GetList(dest interface{}, userId uint, mobile string, page int, pageSize int) (int64, error) {

	var total int64
	tx := db.Debug().Model(o)
	if userId > 0 {
		tx.Where("user_id=?", userId)
	}
	if mobile != "" {
		tx.Where("mobile=?", mobile)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *User) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o)
	if _, okk := queryParm["user_id"]; okk {
		tx.Where("id=?", queryParm["user_id"])
	} else {

		if _, ok := queryParm["mobile"]; ok {
			tx.Where("mobile like ?", "%"+queryParm["mobile"].(string)+"%")
		}

		//tx.Where("status<?", 9)
		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
		tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *User) GetByMobile(mobile string) error {
	var total int64
	err := db.Where("mobile = ?", mobile).Find(o).Count(&total).Error
	if total > 1 {
		return errors.New("存在多条数据")
	}
	return err
}

func (o *User) GetByInvitationCode(code string) error {
	return db.Where("invitation_code = ?", code).First(o).Error
}

func (o *User) GetByUsername(username string) error {
	var total int64
	err := db.Select("*").Where("username = ?", username).Find(o).Count(&total).Error
	if total > 1 {
		return errors.New("存在多条数据")
	}
	return err
}

func (o *User) ExistsUsername(username string) (bool, error) {
	var user User
	if err := db.First(&user, "username = ?", username).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) ExistsMobile(mobile string) (bool, error) {
	var user User
	if err := db.First(&user, "mobile = ?", mobile).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) Save() error {
	return db.Debug().Save(o).Error
}
func (o *User) SetMobile(mobile string) error {
	if o.Mobile == "" {
		return db.Debug().Model(o).Omit("version").Updates(User{Mobile: mobile}).Error
	} else {
		return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')

			if err := tx.Debug().Unscoped().Model(o).Updates(User{Mobile: mobile}).Error; err != nil {
				logger.Error(err)
				return err
			}
			return nil
		})
	}
}

func (o *User) SetLogin(loginIp string) error {
	return db.Debug().Model(o).Omit("version").Updates(User{LastLoginIp: loginIp, LastLoginTime: time.Now()}).Error
}

func (o *User) SetAvatar(avatar string) error {
	return db.Debug().Model(o).Omit("version").Updates(User{Avatar: avatar}).Error
}

func (o *User) SetUsername(username string) error {
	return db.Model(o).Omit("version").Updates(User{Username: username}).Error
}

func (o *User) SetRemark(remark string) error {
	return db.Model(o).Omit("version").Updates(User{Remark: remark}).Error
}

func (o *User) SetRoles(roles string) error {
	return db.Model(o).Omit("version").Updates(User{Roles: roles}).Error
}

func (o *User) SetPassword() error {
	if o.Password == "" {
		return errors.New("密码不能为空")
	}
	if len(o.Password) > 30 {
		return errors.New("该密码已加密过")
	}
	o.Password = ScryptPw(o.Password)
	return db.Debug().Omit("version").Model(o).Update("password", o.Password).Error
}

func (o *User) CheckPassword(pw string) error {
	return bcrypt.CompareHashAndPassword([]byte(o.Password), []byte(pw))
}

// BeforeCreate 密码加密&权限控制
func (u *User) BeforeCreate(_ *gorm.DB) (err error) {
	u.Password = ScryptPw(u.Password)
	return nil
}

// ScryptPw 生成密码
func ScryptPw(password string) string {
	const cost = 10

	HashPw, err := bcrypt.GenerateFromPassword([]byte(password), cost)
	if err != nil {
		log.Println(err)
	}

	return string(HashPw)
}

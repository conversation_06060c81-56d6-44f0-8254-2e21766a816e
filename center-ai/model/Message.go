package model

import (
	"center-ai/utils/tools"
	"gorm.io/gorm"
	"time"
)

type MessageStatusEnum int

const (
	MessageStatusReady      MessageStatusEnum = 0
	MessageStatusSending                      = 1
	MessageStatusSended                       = 2
	MessageStatusSendedFail                   = 3
	MessageStatusSendSuc                      = 4
	MessageStatusSendFail                     = 5
)

func MessageStatusEnumName(i MessageStatusEnum) string {
	switch i {
	case MessageStatusReady:
		return "待发送"
	case MessageStatusSending:
		return "发送中"
	case MessageStatusSended:
		return "请求发送成功"
	case MessageStatusSendedFail:
		return "请求发送失败"
	case MessageStatusSendSuc:
		return "发送成功"
	case MessageStatusSendFail:
		return "发送失败"
	}
	return ""
}

type MessageMethodEnum int

const (
	MessageMethodUnknow    MessageMethodEnum = 0
	MessageMethodWechat                      = 1
	MessageMethodSms                         = 2
	MessageMethodWechatSub                   = 3
)

func MessageMethodEnumName(i MessageMethodEnum) string {
	switch i {
	case MessageMethodUnknow:
		return "未设置"
	case MessageMethodWechat:
		return "微信模板消息"
	case MessageMethodSms:
		return "短信"
	case MessageMethodWechatSub:
		return "微信订阅消息"
	}
	return ""
}

type Message struct {
	gorm.Model
	ProjectId     uint              `json:"project_id" gorm:"type:bigint;not null;default:0;comment:项目ID"`
	ProjectUserId uint              `json:"project_user_id" gorm:"type:bigint;not null;default:0;comment:项目用户ID;index"`
	Method        MessageMethodEnum `json:"method" gorm:"type:tinyint;not null;default:0;comment:提醒方式 1微信 2短信"`
	AccountId     uint              `json:"account_id" gorm:"type:bigint;not null;default:0;comment:账号ID;index"`
	Title         string            `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	Content       string            `json:"content" gorm:"type:varchar(500);not null;default:'';comment:内容"`
	Remark        string            `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	RegularTime   time.Time         `json:"regular_time" gorm:"type:datetime;default:'1900-01-01';comment:定时发送"`
	MessageCode   string            `json:"message_code" gorm:"type:varchar(50);not null;default:'';comment:消息码"`
	TemplateCode  string            `json:"template_code" gorm:"type:varchar(50);not null;default:'';comment:短信模板CODE"`
	SendParm      string            `json:"send_parm" gorm:"type:json;comment:发送参数"`
	SendTime      time.Time         `json:"send_time" gorm:"type:datetime;default:'1900-01-01';comment:发送时间"`
	SendTarget    string            `json:"send_target" gorm:"type:varchar(50);not null;default:'';comment:发送目标，手机号、微信OpenId"`
	SendResult    string            `json:"send_result" gorm:"type:json;comment:调用发送接口返回的数据"`
	MsgId         string            `json:"msg_id" gorm:"type:varchar(50);not null;default:'';comment:第三方消息ID"`
	Status        MessageStatusEnum `json:"status" gorm:"type:tinyint;not null;default:0;comment:状态"`
}

func (Message) TableName() string {
	return "T_Message"
}

func (o *Message) Save() error {
	if o.ID == 0 {
		if o.SendParm == "" {
			o.SendParm = "{}"
		}
		if o.SendResult == "" {
			o.SendResult = "{}"
		}
	}
	return db.Debug().Save(o).Error
}

func (o *Message) GetById(id uint) error {
	return db.First(o, id).Error
}

func (o *Message) GetByTitle(accountId uint, pId uint, title string) error {
	return db.First(o, "account_id=? and p_id=? and title = ?", accountId, pId, title).Error
}

func (o *Message) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {

		if _, ok := queryParm["account_id"]; ok {
			tx.Where("account_id=?", queryParm["account_id"])
		}

		if _, ok := queryParm["p_id"]; ok {
			tx.Where("p_id=?", queryParm["p_id"])
		}

		if _, ok := queryParm["kw"]; ok {
			kw := "%" + queryParm["kw"].(string) + "%"
			tx.Where("(title like ? or title_en like ?)", kw, kw)
		}

		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
		order := "id desc"
		if _, ok := queryParm["order"]; ok {
			if queryParm["order"].(string) == "select" {
				order = "p_id,order_index desc,id desc"
			}
		}
		tx.Order(order).Limit(pageSize).Offset((page - 1) * pageSize)
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *Message) ListForSend(dest interface{}, lastAutoId uint, pageSize int) error {
	//checkTime := time.Now().Add(time.Hour * 48 * -1)
	tx := db.Debug().Model(o)
	tx.Where("id>? and status=?", lastAutoId, MessageStatusReady)
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *Message) SetStatusSending() error {
	return db.Model(o).Updates(map[string]interface{}{"status": MessageStatusSending}).Error
}

func (o *Message) SetSendResult(content string, err error, msgId string) error {
	if err == nil {
		return db.Model(o).Updates(map[string]interface{}{"send_time": time.Now(), "status": MessageStatusSended, "content": content, "msg_id": msgId}).Error
	} else {
		var m = map[string]string{
			"err": err.Error(),
		}
		return db.Model(o).Updates(map[string]interface{}{"send_time": time.Now(), "status": MessageStatusSendedFail, "content": content, "send_result": tools.GetJsonFromStruct(m)}).Error
	}
}

package model

import "gorm.io/gorm"

type ShopFace struct {
	gorm.Model
	Title           string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	Sex             int    `json:"sex" gorm:"type:tinyint;not null;default:0;comment:1男 2女"`
	AgeSect         int    `json:"age_sect" gorm:"type:tinyint;not null;default:0;comment:年龄段"`
	SdModelId       uint   `json:"sd_model_id" gorm:"type:bigint;not null;default:0;comment:模型ID"`
	Prompt          string `json:"prompt" gorm:"type:varchar(1500);not null;default:'';comment:风格描述"`
	NegativePrompt  string `json:"negative_prompt" gorm:"type:varchar(1500);not null;default:'';comment:我不想要什么样的风格画面描述"`
	ControlnetUnits string `json:"controlnet_units" gorm:"type:json;comment:controlnet单元数组"`
	Cover           string `json:"cover" gorm:"type:varchar(100);not null;default:'';comment:封面图像"`
	Remark          string `json:"remark" gorm:"type:varchar(500);not null;default:'';comment:后台备注"`
	OrderIndex      int    `json:"order_index" gorm:"type:int;not null;default:0;comment:序号(越大越前面)"`
	PriceCoin       int    `json:"price_coin" gorm:"type:int;not null;default:0;comment:所需Coin"`
	State           int    `json:"state" gorm:"type:int;not null;default:0;comment:状态(1可用)"`
}

func (ShopFace) TableName() string {
	return "T_ShopFace"
}

func (o *ShopFace) GetById(id uint) error {
	return db.First(o, id).Error
}

func (o *ShopFace) GetList(dest interface{}, id uint, sex int, roleName string, page int, pageSize int) (int64, error) {

	var total int64
	tx := db.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	}
	if sex > 0 {
		tx.Where("sex=?", sex)
	}
	if roleName != "" {
		tx.Where("role_name like ?", roleName)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *ShopFace) Save() error {
	return db.Save(o).Error
}

func (o *ShopFace) SetCover(path string) error {
	return db.Model(o).Updates(ShopFace{Cover: path}).Error
}

//type ControlnetUnit struct {
//	InputImage    string `json:"input_image"`
//	Mask          string `json:"mask"`
//	Module        string `json:"module"`
//	Model         string `json:"model"`
//	Weight        int    `json:"weight"`
//	ResizeMode    int    `json:"resize_mode"`
//	Lowvram       string `json:"lowvram"`
//	ProcessorRes  int    `json:"processor_res"`
//	ThresholdA    int    `json:"threshold_a"`
//	ThresholdB    int    `json:"threshold_b"`
//	Guidance      int    `json:"guidance"`
//	GuidanceStart int    `json:"guidance_start"`
//	GuidanceEnd   int    `json:"guidance_end"`
//	Guessmode     string `json:"guessmode"`
//	PixelPerfect  string `json:"pixel_perfect"`
//	ControlMode   int    `json:"control_mode"`
//}

package model

import (
	"center-ai/utils/logger"
	"gorm.io/gorm"
)

type ShopOrigin struct {
	gorm.Model
	UserId       uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	Title        string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	OriginMd5    string `json:"origin_md5" gorm:"type:varchar(50);not null;default:'';comment:原始图路径"`
	OriginImg    string `json:"origin_img" gorm:"type:varchar(100);not null;default:'';comment:原始图路径"`
	OriginWidth  int    `json:"origin_width" gorm:"type:int;not null;default:0;comment:原始图宽度"`
	OriginHeight int    `json:"origin_height" gorm:"type:int;not null;default:0;comment:原始图高度"`
	MadeType     int    `json:"made_type" gorm:"type:int;not null;default:0;comment:制作类型"`
	ShopFaceId   uint   `json:"shop_face_id" gorm:"type:bigint;not null;default:0;comment:脸膜ID"`
	ShopSceneId  uint   `json:"shop_scene_id" gorm:"type:bigint;not null;default:0;comment:场景ID"`
	CropX        int    `json:"crop_x" gorm:"type:int;not null;default:0;comment:裁剪图相对于原始图左上角X坐标"`
	CropY        int    `json:"crop_y" gorm:"type:int;not null;default:0;comment:裁剪图相对于原始图左上角Y坐标"`
	CropWidth    int    `json:"crop_width" gorm:"type:int;not null;default:0;comment:裁剪图宽度"`
	CropHeight   int    `json:"crop_height" gorm:"type:int;not null;default:0;comment:裁剪图高度"`
	CropImg      string `json:"crop_img" gorm:"type:varchar(100);not null;default:'';comment:裁剪图路径"`
	BodyBgColor  string `json:"body_bg_color" gorm:"type:varchar(50);not null;default:'';comment:主体图背景色"`
	BodyImg      string `json:"body_img" gorm:"type:varchar(100);not null;default:'';comment:主体图路径"`
	BodyBgImg    string `json:"body_bg_img" gorm:"type:varchar(100);not null;default:'';comment:带背景色主体图路径"`
	//MaskPixels   int    `json:"mask_pixels" gorm:"type:int;not null;default:0;comment:蒙版缩进像素"`
	MaskImg    string `json:"mask_img" gorm:"type:varchar(100);not null;default:'';comment:蒙版图路径"`
	MaskMargin string `json:"mask_margin" gorm:"type:varchar(50);not null;default:'';comment:蒙版边缘参数2,3,2,2分别代表上右下左"`
	MaskUpImg  string `json:"mask_up_img" gorm:"type:varchar(100);not null;default:'';comment:精细蒙版图路径(当前用阿里云接口)"`
	MapImg     string `json:"map_img" gorm:"type:varchar(100);not null;default:'';comment:蒙版映射图路径"`
	PushJson   string `json:"push_json" gorm:"type:json;';comment:推送给后端的绘图JSON模板"`
	FinalImg   string `json:"final_img" gorm:"type:varchar(100);not null;default:'';comment:终稿图路径"`
	BatchCount int    `json:"batch_count" gorm:"type:int;not null;default:0;comment:批量次数，每生成一次加1"`
	TotalCount int    `json:"total_count" gorm:"type:int;not null;default:0;comment:生成的图片总数"`
	Remark     string `json:"remark" gorm:"type:varchar(150);not null;default:'';comment:备注"`
}

func (ShopOrigin) TableName() string {
	return "T_ShopOrigin"
}

func (o *ShopOrigin) GetById(id uint) error {
	return db.First(o, id).Error
}

func (o *ShopOrigin) GetList(dest interface{}, id uint, userId uint, madeType int, title string, page int, pageSize int) (int64, error) {

	var total int64
	tx := db.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	}
	if userId > 0 {
		tx.Where("user_id=?", userId)
	}
	if madeType > 0 {
		tx.Where("made_type=?", madeType)
	}
	if title != "" {
		tx.Where("title like ?", title)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *ShopOrigin) GetListForLoad(state ...int) []Role {
	ary := make([]Role, 0)
	tx := db.Debug().Model(o)
	if len(state) == 1 {
		tx.Where("state=?", state[0])
	}
	tx.Order("id desc")
	if err := tx.Scan(&ary).Error; err != nil {
		logger.Error(err)
	}
	return ary
}

func (o *ShopOrigin) Save() error {
	return db.Save(o).Error
}

func (o *ShopOrigin) SetMadeType(madeType int) error {
	return db.Model(o).Updates(ShopOrigin{MadeType: madeType}).Error
}

func (o *ShopOrigin) SetShopFace(shopFaceId uint) error {
	return db.Model(o).Updates(map[string]interface{}{"shop_face_id": shopFaceId}).Error
}

func (o *ShopOrigin) SetShopScene(shopSceneId uint) error {
	return db.Model(o).Updates(map[string]interface{}{"shop_scene_id": shopSceneId}).Error
}

func (o *ShopOrigin) SetBodyBgColor(bodyBgColor string) error {
	//return db.Debug().Model(o).Updates(ShopOrigin{BodyBgColor: bodyBgColor}).Error
	return db.Model(o).Updates(map[string]interface{}{"body_bg_color": bodyBgColor}).Error
}

func (o *ShopOrigin) SetBatchCount(batchCount, totalCount int) error {
	return db.Model(o).Updates(ShopOrigin{BatchCount: batchCount, TotalCount: totalCount}).Error
}

func (o *ShopOrigin) SetPushJson(pushJson string) error {
	return db.Model(o).Updates(ShopOrigin{PushJson: pushJson}).Error
}
func (o *ShopOrigin) SetOriginMd5(md5Str string) error {
	return db.Model(o).Updates(ShopOrigin{OriginMd5: md5Str}).Error
}
func (o *ShopOrigin) SetOriginImg(path string, width int, height int) error {
	return db.Model(o).Updates(ShopOrigin{OriginImg: path, OriginWidth: width, OriginHeight: height}).Error
}
func (o *ShopOrigin) SetCropImg(path string, width int, height int) error {
	return db.Model(o).Updates(ShopOrigin{CropImg: path, CropWidth: width, CropHeight: height}).Error
}
func (o *ShopOrigin) SetUploadCropImg(path string, cropX int, cropY int, width int, height int) error {
	//return db.Model(o).Updates(ShopOrigin{CropImg: path, CropX: cropX, CropY: cropY, CropWidth: width, CropHeight: height}).Error
	return db.Model(o).Updates(map[string]interface{}{"crop_img": path, "crop_x": cropX, "crop_y": cropY, "crop_width": width, "crop_height": height}).Error

}
func (o *ShopOrigin) SetBodyImg(path string) error {
	return db.Model(o).Updates(ShopOrigin{BodyImg: path}).Error
}
func (o *ShopOrigin) SetBodyBgImg(path string) error {
	return db.Model(o).Updates(ShopOrigin{BodyBgImg: path}).Error
}
func (o *ShopOrigin) SetMaskImg(path string) error {
	return db.Model(o).Updates(ShopOrigin{MaskImg: path}).Error
}
func (o *ShopOrigin) SetMaskUpImg(path string) error {
	return db.Model(o).Updates(ShopOrigin{MaskUpImg: path}).Error
}
func (o *ShopOrigin) SetMapImg(path string) error {
	return db.Model(o).Updates(ShopOrigin{MapImg: path}).Error
}
func (o *ShopOrigin) SetFinalImg(path string) error {
	return db.Model(o).Updates(ShopOrigin{FinalImg: path}).Error
}

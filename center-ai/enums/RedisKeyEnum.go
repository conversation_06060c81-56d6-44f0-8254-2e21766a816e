package enums

import "reflect"

type redisKeyEnum_ struct {
	Kv, AutoSerialNumber, ScanLogin, SmsReg, SmsLogin, SmsModifyPassword, NeedSendMessage, InsiderUser, InsiderUsers string
}

func (c redisKeyEnum_) Get(id string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if typeVo.Field(i).Name == id {
			return vo.Field(i).Interface().(string)
		}
	}
	return ""
}

var RedisKeyEnum = redisKeyEnum_{
	Kv:                "center-ai:kv:",
	AutoSerialNumber:  "center-ai:auto_serial_number",
	ScanLogin:         "center-ai:scan_login:",
	SmsReg:            "center-ai:sms:reg:",
	SmsLogin:          "center-ai:sms:login:",
	SmsModifyPassword: "center-ai:sms:modify_password:",
	NeedSendMessage:   "center-ai:list:message",
}

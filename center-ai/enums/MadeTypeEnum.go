package enums

type madeTypeEnum_ struct {
	ChangeHead, ChangeBackground, ChangeHeadAndBackground, ChangeSence, ChangeBigHead, ChangeBigHeadAndBackColor, ChangeClothes int
}

var MadeTypeEnum = madeTypeEnum_{
	ChangeHead:                1, //只换头
	ChangeBackground:          2, //只换背景色
	ChangeHeadAndBackground:   3, //换头换背景色
	ChangeSence:               4, //只换场景
	ChangeBigHead:             5, //只换头(大)
	ChangeBigHeadAndBackColor: 6, //换头换背景色
	ChangeClothes:             7, //只换服装
}

package shopshow

import (
	"center-ai/enums"
	"center-ai/middleware"
	"center-ai/model"
	"center-ai/service"
	"center-ai/utils/config"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/myimg"
	"center-ai/utils/tools"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

type _shopOriginApi struct {
}

var ShopOriginApi _shopOriginApi

type shopOriginAddReq struct {
	Id     uint   `json:"id"`
	Title  string `json:"title"`
	Remark string `json:"remark"`
}

type shopOriginSetParamReq struct {
	Id          uint   `json:"id"`
	MadeType    int    `json:"made_type"`
	Color       string `json:"color"`
	BodyBgColor string `json:"body_bg_color"`
	ShopFaceId  uint   `json:"shop_face_id"`
	ShopSceneId uint   `json:"shop_scene_id"`
	UnCrop      int    `json:"uncrop"`
	CropWidth   int    `json:"crop_width"`
	CropHeight  int    `json:"crop_height"`
	CropX       int    `json:"crop_x"`
	CropY       int    `json:"crop_y"`
	CropBase64  string `json:"crop_base64"`
}

type shopOriginCropReq struct {
	Id         uint   `json:"id"`
	CropX      int    `json:"crop_x"`
	CropY      int    `json:"crop_y"`
	CropBase64 string `json:"crop_base64"`
}

type shopOriginMaskReq struct {
	Id         uint   `json:"id"`
	MaskBase64 string `json:"mask_base64"`
}

type shopOriginGenMaskReq struct {
	Id                   uint    `json:"id"`
	ImgType              string  `json:"img_type"`
	SegmentParm          string  `json:"segment_parm"`
	StabilityScoreThresh float64 `json:"stability_score_thresh"`
	PredIouThresh        float64 `json:"pred_iou_thresh"`
	StabilityScoreOffset int     `json:"stability_score_offset"`
	PointsPerBatch       int     `json:"points_per_batch"`
}

type shopOriginMaskStatusReq struct {
	ImgMd5 string `json:"img_md5"`
}

type shopOriginGenMaskUpReq struct {
	Id uint `json:"id"`
}

type shopOriginInvertMaskReq struct {
	Id         uint `json:"id"`
	MaskPixels int  `json:"mask_pixels"`
}

type shopOriginImg2ImgReq struct {
	Id                uint   `json:"id"`
	ShopFaceId        uint   `json:"shop_face_id"`
	SdModelCheckpoint string `json:"sd_model_checkpoint"`
	Prompt            string `json:"prompt"`
	NegativePrompt    string `json:"negative_prompt"`
	SdServer          string `json:"sd_server"`
	BatchSize         int    `json:"batch_size"`
}

type shopOriginSdProgressReq struct {
	IdTask string `json:"id_task"`
}

type shopOriginListReq struct {
	Id       uint   `json:"id"`
	UserId   uint   `json:"user_id"`
	MadeType int    `json:"made_type"`
	Sex      int    `json:"sex"`
	Title    string `json:"title"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type shopOriginSetFinalImgReq struct {
	ShopOriginId uint   `json:"shop_origin_id"`
	OutImgMd5    string `json:"out_img_md5"`
}

type shopOriginItem struct {
	ID                uint           `json:"id"`
	UserId            uint           `json:"user_id"`
	Title             string         `json:"title"`
	OriginMd5         string         `json:"origin_md5"`
	OriginImg         string         `json:"-"`
	OriginImgUrl      string         `json:"origin_img_url"`
	OriginWhiteImgUrl string         `json:"origin_white_img_url"`
	OriginJpgImgUrl   string         `json:"origin_jpg_img_url"`
	OriginMaskImgUrl  string         `json:"origin_mask_img_url"`
	OriginWidth       int            `json:"origin_width"`
	OriginHeight      int            `json:"origin_height"`
	MadeType          int            `json:"made_type"`
	ShopFaceId        uint           `json:"shop_face_id"`
	ShopSceneId       uint           `json:"shop_scene_id"`
	BodyBgColor       string         `json:"body_bg_color"`
	CropImg           string         `json:"-"`
	CropImgUrl        string         `json:"crop_img_url"`
	BodyImg           string         `json:"-"`
	BodyImgUrl        string         `json:"body_img_url"`
	BodyBgImg         string         `json:"-"`
	BodyBgImgUrl      string         `json:"bodybg_img_url"`
	MaskImg           string         `json:"mask_img"`
	MaskImgUrl        string         `json:"mask_img_url"`
	MaskUpImg         string         `json:"-"`
	MaskUpImgUrl      string         `json:"maskup_img_url"`
	MapImg            string         `json:"-"`
	MapImgUrl         string         `json:"map_img_url"`
	PushJson          string         `json:"push_json"`
	FinalImg          string         `json:"final_img"`
	FinalImgUrl       string         `json:"final_img_url"`
	BatchCount        int            `json:"batch_count"`
	Remark            string         `json:"remark"`
	CreatedAt         model.JsonTime `json:"created_at"`
}

func (obj _shopOriginApi) Add(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	var oReq shopOriginAddReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var shopOrigin model.ShopOrigin
	if oReq.Id > 0 {
		if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
			errmsg.Abort(c, errmsg.FAIL, "权限不足")
			return
		}
		if err := shopOrigin.GetById(oReq.Id); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
			return
		}
		shopOrigin.Title = oReq.Title
		shopOrigin.Remark = oReq.Remark

		if err := shopOrigin.Save(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存信息失败")
			return
		}
	} else {
		if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
			errmsg.Abort(c, errmsg.FAIL, "权限不足")
			return
		}

		shopOrigin = model.ShopOrigin{
			UserId:   claims.UserId,
			Title:    oReq.Title,
			PushJson: "{}",
			Remark:   oReq.Remark,
		}
		if err := shopOrigin.Save(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存信息失败")
			return
		}
		oMd5Str := fmt.Sprintf("%d,%s", shopOrigin.ID, shopOrigin.CreatedAt.Format("2006-01-02 15:04:05.000"))
		md5Str := tools.GetMd5(oMd5Str)
		if md5Str == "" {
			logger.Error("MD5字符串生成失败 shopOriginId", shopOrigin.ID)
			errmsg.Abort(c, errmsg.FAIL, "MD5字符串生成失败")
			return
		}
		if err := shopOrigin.SetOriginMd5(md5Str); err != nil {
			logger.Error(err, "设置MD5字符串生成失败 shopOriginId", shopOrigin.ID)
			errmsg.Abort(c, errmsg.FAIL, "设置MD5字符串失败")
			return
		}
	}
	msg = "添加成功"
	if oReq.Id > 0 {
		msg = "修改成功"
	}
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) UploadOriginImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	id := uint(0)
	idStr, _ := c.GetPostForm("id")
	if idStr != "" {
		if num, err := strconv.Atoi(idStr); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		} else {
			id = uint(num)
		}
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	f, err := c.FormFile("origin_img")
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}

	md5Str := shopOrigin.OriginMd5
	if len(md5Str) != 32 {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参考图名称生成失败")
		return
	}

	ext := path.Ext(f.Filename) // 输出 .html
	path := service.ImgPath.GetOriginPath(md5Str, ext)
	absolutePath := config.DiffusionFilePath + path
	directory := filepath.Dir(absolutePath) // 获取目录路径
	// 创建目录，存在则不创建，不存在则创建
	if err := os.MkdirAll(directory, 0755); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "目录创建失败")
		return
	}

	if err := c.SaveUploadedFile(f, absolutePath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}
	img, _, err := myimg.FileToImg(absolutePath)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}
	rotate := false
	orientation := myimg.ReadOrientation(absolutePath)
	if orientation == myimg.OrientationRotate90 { //理解为图片的顶部在90度这个方向,旋转270度可以把图片顶部朝上
		img = myimg.Rotate270(img)
		rotate = true
	} else if orientation == myimg.OrientationRotate270 { //理解为图片的顶部在270度这个方向,旋转90度可以把图片顶部朝上
		img = myimg.Rotate90(img)
		rotate = true
	} else if orientation == myimg.OrientationRotate180 { //理解为图片的顶部在180度这个方向,旋转180度可以把图片顶部朝上
		img = myimg.Rotate180(img)
		rotate = true
	}

	if rotate {
		if err := myimg.ImgToJpegFile(img, absolutePath, 92); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存旋转图片失败")
			return
		}
	}

	width := img.Bounds().Size().X
	height := img.Bounds().Size().Y

	if err := shopOrigin.SetOriginImg(path, width, height); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片路径设置失败")
		return
	}

	msg = "添加成功"
	if id > 0 {
		msg = "修改成功"
	}
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["origin_img_url"] = config.DiffusionDomain + shopOrigin.OriginImg
	result["origin_img_width"] = width
	result["origin_img_height"] = height
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) UploadCropImg(c *gin.Context) {
	defer func() {
		if err := recover(); err != nil {
			logger.Error("UploadMaskImg:", err)
			errmsg.Abort(c, errmsg.FAIL, "执行失败，请重试")
			return
		}
	}()

	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginCropReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	if oReq.CropBase64 == "" {
		logger.Error("CropBase64=为空")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 CropBase64")
		return
	}
	if strings.HasPrefix(oReq.CropBase64, "data:") {
		tmpAry := strings.Split(oReq.CropBase64, ",")
		if len(tmpAry) != 2 {
			logger.Error("分隔Base64前缀出错")
			errmsg.Abort(c, errmsg.FAIL, "分隔Base64前缀出错")
			return
		}
		oReq.CropBase64 = tmpAry[1]
	}

	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	cropImg, err := myimg.Base64ToImg(oReq.CropBase64)
	if err != nil {
		logger.Error(err, "====", oReq.CropBase64)
		errmsg.Abort(c, errmsg.FAIL, "base64转图片失败")
		return
	}

	if shopOrigin.MadeType != enums.MadeTypeEnum.ChangeBigHead {
		if err := shopOrigin.SetMadeType(enums.MadeTypeEnum.ChangeBigHead); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "制作类型设置失败")
			return
		}
	}

	cropPath := service.ImgPath.GetCropPath(shopOrigin.OriginImg)
	absoluteCropPath := config.DiffusionFilePath + cropPath
	if err := myimg.ImgToPngFile(cropImg, absoluteCropPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存裁剪图失败")
		return
	}
	width := cropImg.Bounds().Size().X
	height := cropImg.Bounds().Size().Y
	if err := shopOrigin.SetUploadCropImg(cropPath, oReq.CropX, oReq.CropY, width, height); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存裁剪图路径失败")
		return
	}

	msg = "裁剪图上传成功"

	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["crop_img_url"] = config.DiffusionDomain + shopOrigin.CropImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) SetParam(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	var oReq shopOriginSetParamReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("数据获取失败", err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	if oReq.CropWidth == 0 {
		oReq.CropWidth = 1496
	}
	if oReq.CropHeight == 0 {
		oReq.CropHeight = 2000
	}
	if oReq.ShopSceneId > 0 {
		oReq.BodyBgColor = ""
	}

	var shopOrigin model.ShopOrigin

	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
		return
	}
	shopOrigin.BodyBgColor = oReq.BodyBgColor
	shopOrigin.MadeType = oReq.MadeType
	shopOrigin.ShopFaceId = oReq.ShopFaceId
	shopOrigin.ShopSceneId = oReq.ShopSceneId

	if err := shopOrigin.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存信息失败")
		return
	}

	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBigHeadAndBackColor {
		if oReq.CropBase64 == "" && shopOrigin.CropImg == "" {
			logger.Error("请上传裁剪图 shopOrigin.ID:", shopOrigin.ID)
			errmsg.Abort(c, errmsg.FAIL, "请上传裁剪图")
			return
		}
		msg = "参数设置成功"
		if oReq.CropBase64 != "" {
			cropImg, err := myimg.Base64ToImg(oReq.CropBase64)
			if err != nil {
				logger.Error(err, "====", oReq.CropBase64)
				errmsg.Abort(c, errmsg.FAIL, "base64转图片失败")
				return
			}
			width := cropImg.Bounds().Size().X
			height := cropImg.Bounds().Size().Y

			sdX, sdY := service.ShopOrigin.GetSdSize(width, height)
			cropImg = myimg.ResizeImg(sdX, sdY, cropImg, false)

			if shopOrigin.MadeType != enums.MadeTypeEnum.ChangeBigHeadAndBackColor {
				if err := shopOrigin.SetMadeType(enums.MadeTypeEnum.ChangeBigHeadAndBackColor); err != nil {
					logger.Error(err)
					errmsg.Abort(c, errmsg.FAIL, "制作类型设置失败")
					return
				}
			}

			cropPath := service.ImgPath.GetCropPath(shopOrigin.OriginImg)
			absoluteCropPath := config.DiffusionFilePath + cropPath
			if err := myimg.ImgToPngFile(cropImg, absoluteCropPath); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "保存裁剪图失败")
				return
			}

			if err := shopOrigin.SetUploadCropImg(cropPath, oReq.CropX, oReq.CropY, width, height); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "保存裁剪图路径失败")
				return
			}
			msg = "裁剪图上传成功"
		}

		result := make(map[string]interface{})
		result["id"] = shopOrigin.ID
		result["origin_md5"] = shopOrigin.OriginMd5
		result["crop_img_url"] = config.DiffusionDomain + shopOrigin.CropImg
		if code == 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		} else {
			errmsg.Abort(c, errmsg.FAIL, msg)
		}
		return
	}

	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBigHead {

		if oReq.CropBase64 == "" && shopOrigin.CropImg == "" {
			logger.Error("请上传裁剪图 shopOrigin.ID:", shopOrigin.ID)
			errmsg.Abort(c, errmsg.FAIL, "请上传裁剪图")
			return
		}
		msg = "参数设置成功"
		if oReq.CropBase64 != "" {
			cropImg, err := myimg.Base64ToImg(oReq.CropBase64)
			if err != nil {
				logger.Error(err, "====", oReq.CropBase64)
				errmsg.Abort(c, errmsg.FAIL, "base64转图片失败")
				return
			}

			width := cropImg.Bounds().Size().X
			height := cropImg.Bounds().Size().Y
			sdX, sdY := service.ShopOrigin.GetSdSize(width, height)
			cropImg = myimg.ResizeImg(sdX, sdY, cropImg, false)

			if shopOrigin.MadeType != enums.MadeTypeEnum.ChangeBigHead {
				if err := shopOrigin.SetMadeType(enums.MadeTypeEnum.ChangeBigHead); err != nil {
					logger.Error(err)
					errmsg.Abort(c, errmsg.FAIL, "制作类型设置失败")
					return
				}
			}

			cropPath := service.ImgPath.GetCropPath(shopOrigin.OriginImg)
			absoluteCropPath := config.DiffusionFilePath + cropPath
			if err := myimg.ImgToPngFile(cropImg, absoluteCropPath); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "保存裁剪图失败")
				return
			}

			if err := shopOrigin.SetUploadCropImg(cropPath, oReq.CropX, oReq.CropY, width, height); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "保存裁剪图路径失败")
				return
			}

			msg = "裁剪图上传成功"
		}

		result := make(map[string]interface{})
		result["id"] = shopOrigin.ID
		result["origin_md5"] = shopOrigin.OriginMd5
		result["crop_img_url"] = config.DiffusionDomain + shopOrigin.CropImg
		if code == 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		} else {
			errmsg.Abort(c, errmsg.FAIL, msg)
		}
		return
	}

	img, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.OriginImg)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取原始图失败")
		return
	}
	cropPath := "" + strings.Replace(shopOrigin.OriginImg, "origin_", "crop_", -1)
	absoluteCropPath := config.DiffusionFilePath + cropPath

	width := shopOrigin.OriginWidth
	height := shopOrigin.OriginHeight

	cropImg := img
	if oReq.UnCrop == 1 {

	} else {
		targetWidth := 1496
		targetHeight := 2000
		if width < 1496 && height < 2000 {
			// 计算宽度和高度的缩放比例，选择较小的那个比例以保持等比例放大
			widthRatio := float64(targetWidth) / float64(width)
			heightRatio := float64(targetHeight) / float64(height)
			scale := widthRatio
			if heightRatio < widthRatio {
				scale = heightRatio
			}

			// 计算新的宽度和高度
			newWidth := int(float64(width) * scale)
			newHeight := int(float64(height) * scale)
			img = myimg.ResizeImg(newWidth, newHeight, img, true)
		}
		cropImg = myimg.WindowImg(targetWidth, targetHeight, img)
	}

	if err := myimg.ImgToPngFile(cropImg, absoluteCropPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存裁剪图失败")
		return
	}
	width = cropImg.Bounds().Size().X
	height = cropImg.Bounds().Size().Y

	if err := shopOrigin.SetCropImg(cropPath, width, height); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存裁剪图信息失败")
		return
	}

	msg = "参数设置成功"
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["crop_img_url"] = config.DiffusionDomain + shopOrigin.CropImg
	//result["body_bg_color"] = oReq.BodyBgColor

	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) SetSimpleParam(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	var oReq shopOriginSetParamReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("数据获取失败", err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}

	var shopOrigin model.ShopOrigin

	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
		return
	}
	if oReq.ShopFaceId > 0 {
		if err := shopOrigin.SetShopFace(oReq.ShopFaceId); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存脸膜参数失败")
			return
		} else {
			msg = "脸膜参数保存成功"
		}
	}
	if oReq.ShopSceneId > 0 {
		if err := shopOrigin.SetShopScene(oReq.ShopSceneId); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存场景参数失败")
			return
		} else {
			msg += "场景参数保存成功"
		}
	}
	if oReq.BodyBgColor != "" {
		if err := shopOrigin.SetBodyBgColor(oReq.BodyBgColor); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存背景色参数失败")
			return
		} else {
			msg += "背景色参数保存成功"
		}
	}

	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
	return
}

func (obj _shopOriginApi) GenBodyImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginGenMaskReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}
	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBigHeadAndBackColor {
		originId := oReq.Id
		if err := service.ShopOrigin.GenBodyImg(originId); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "生成主体图失败")
			return
		}
		if err := service.ShopOrigin.GenBodyBgImg(originId); err != nil {
			errmsg.Abort(c, errmsg.FAIL, "生成主体背景图失败")
			logger.Error(err)
		}

		if err := shopOrigin.GetById(oReq.Id); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		}

		msg = "主体图生成完成"
		result := make(map[string]interface{})
		result["id"] = shopOrigin.ID
		result["origin_md5"] = shopOrigin.OriginMd5
		result["body_img_url"] = config.DiffusionDomain + shopOrigin.BodyImg
		result["bodybg_img_url"] = config.DiffusionDomain + shopOrigin.BodyBgImg
		if code == 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		} else {
			errmsg.Abort(c, errmsg.FAIL, msg)
		}
		return
	}

	logger.Info("裁剪图路径：", shopOrigin.CropImg)
	if shopOrigin.CropImg == "" {
		logger.Error("裁剪图不存在", oReq)
		errmsg.Abort(c, errmsg.FAIL, "裁剪图不存在")
		return
	}

	if bodyImgUrl, err1 := service.AliMask.SegmentHDBody(config.DiffusionFilePath + shopOrigin.CropImg); err1 != nil {
		logger.Error(err1)
		errmsg.Abort(c, errmsg.FAIL, "生成主体图失败")
		return
	} else {

		//img, _, err := tools.GetImgFromUrl(bodyImgUrl)
		//if err != nil {
		//	logger.Error(err)
		//	errmsg.Abort(c, errmsg.FAIL, "下载主体图失败")
		//	return
		//}
		bodyPath := service.ImgPath.GetBodyPath(shopOrigin.OriginImg)
		absoluteBodyPath := config.DiffusionFilePath + bodyPath

		if err := tools.SaveImgFromUrl(bodyImgUrl, absoluteBodyPath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "下载主体图失败")
			return
		}

		//bodyImg := myimg.TurnToBackgroundColor(img, "0,0,0,0")
		//
		//if err := myimg.ImgToFile(bodyImg, absoluteBodyPath); err != nil {
		//	logger.Error(err)
		//	errmsg.Abort(c, errmsg.FAIL, "保存主体图失败")
		//	return
		//}
		if err := shopOrigin.SetBodyImg(bodyPath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存主体图路径失败")
			return
		}

		img, _, err := myimg.FileToImg(absoluteBodyPath)
		if err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "获取主体图失败")
			return
		}

		logger.Info("开始换背景", shopOrigin.ID, "   ", shopOrigin.BodyBgColor)
		bodyBgImg := myimg.TurnToBackgroundColor(img, shopOrigin.BodyBgColor)

		bodyBgPath := service.ImgPath.GetBodyBgPath(shopOrigin.OriginImg)
		absoluteBodyBgPath := config.DiffusionFilePath + bodyBgPath

		if err := myimg.ImgToPngFile(bodyBgImg, absoluteBodyBgPath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存带背景主体图失败")
			return
		}
		if err := shopOrigin.SetBodyBgImg(bodyBgPath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存带背景主体图路径失败")
			return
		}
	}

	msg = "主体图生成完成"
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["body_img_url"] = config.DiffusionDomain + shopOrigin.BodyImg
	result["bodybg_img_url"] = config.DiffusionDomain + shopOrigin.BodyBgImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GenBodyImgFromUpload(c *gin.Context) { //上传图片通过阿里云获取主体图
	var code int
	var msg string
	//claims := c.Value("center_claims").(*middleware.CenterClaims)
	//
	//if claims.UserId <= 0 {
	//
	//}

	f, err := c.FormFile("file")
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}

	tempPath := service.ImgPath.GetTempPath(f.Filename)

	directory := filepath.Dir(tempPath) // 获取目录路径
	// 创建目录，存在则不创建，不存在则创建
	if err := os.MkdirAll(directory, 0755); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "目录创建失败")
		return
	}
	logger.Info("tempPath:", tempPath)
	if err := c.SaveUploadedFile(f, tempPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}

	result := make(map[string]interface{})
	if bodyImgUrl, err1 := service.AliMask.SegmentHDBody(tempPath); err1 != nil {
		logger.Error(err1)
		errmsg.Abort(c, errmsg.FAIL, "生成主体图失败")
		return
	} else {
		result["body_img_url"] = bodyImgUrl
	}
	if err := os.Remove(tempPath); err != nil && !os.IsNotExist(err) {
		logger.Error("临时图片删除失败", err, tempPath)
	}

	msg = "主体图生成完成"
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GenMaskImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginGenMaskReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}
	//logger.Info("裁剪图路径：", shopOrigin.CropImg)
	//if shopOrigin.CropImg == "" {
	//	logger.Error("裁剪图不存在", oReq)
	//	errmsg.Abort(c, errmsg.FAIL, "裁剪图不存在")
	//	return
	//}

	//if bol, err := service.SegmentService.PushJson(shopOrigin.OriginMd5, shopOrigin.CropImg, oReq.StabilityScoreThresh, oReq.PredIouThresh, oReq.StabilityScoreOffset, oReq.PointsPerBatch); err != nil {
	//	logger.Error("bol", bol, err)
	//	errmsg.Abort(c, errmsg.FAIL, "数据推送失败")
	//	return
	//}

	imgPath := shopOrigin.CropImg
	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeHead {
		imgPath = shopOrigin.BodyBgImg
	}
	if oReq.ImgType == "origin" {
		imgPath = shopOrigin.OriginImg
	} else if oReq.ImgType == "body" {
		imgPath = shopOrigin.BodyImg
	} else if oReq.ImgType == "white" {
		imgPath = service.ImgPath.GetOriginWhitePath(shopOrigin.OriginImg)
	} else if oReq.ImgType == "crop" {
		imgPath = shopOrigin.CropImg
	} else if oReq.ImgType == "bodybg" {
		imgPath = shopOrigin.BodyBgImg
	}

	if bol, err := service.SegmentService.PushJsonSegment(shopOrigin.OriginMd5, imgPath, oReq.SegmentParm); err != nil {
		logger.Error("bol", bol, err)
		errmsg.Abort(c, errmsg.FAIL, "数据推送失败")
		return
	}

	msg = "正在解析蒙版"
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GetMaskStatus(c *gin.Context) {
	var code int
	var msg string
	var oReq shopOriginMaskStatusReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	result := make(map[string]interface{})
	logger.Info("mask getstatus ", oReq.ImgMd5)

	if outDataStr, err := service.SegmentService.HGetJsonStr(oReq.ImgMd5); err != nil {
		logger.Error("val:", outDataStr, err)
		code = 1
		msg = "状态获取失败"
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
		return
	} else {
		c.Data(200, "application/json", []byte(outDataStr))
		return
	}
}

func (obj _shopOriginApi) ClearMask(c *gin.Context) {
	var code int
	var msg string
	var oReq shopOriginMaskStatusReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.ImgMd5 == "" {
		logger.Error("参数为空")
		errmsg.Abort(c, errmsg.FAIL, "参数为空")
		return
	}

	result := make(map[string]interface{})
	result["img_md5"] = oReq.ImgMd5

	if outDataStr, err := service.SegmentService.HRemoveJson(oReq.ImgMd5); err != nil {
		logger.Error("val:", outDataStr, err)
		code = 1
		msg = "蒙版缓存清除失败"
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
		return
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "蒙版缓存清除成功",
			"result": result,
		})
	}
}

func (obj _shopOriginApi) DelMaskKey(c *gin.Context) {
	var code int
	var msg string
	var oReq shopOriginMaskStatusReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	result := make(map[string]interface{})
	if err := service.SegmentService.HClear(); err != nil {
		logger.Error("val:", err)
		code = 1
		msg = "删除蒙版缓存失败"
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
		return
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "删除蒙版缓存成功",
			"result": result,
		})
	}
}

func (obj _shopOriginApi) UploadMaskImg(c *gin.Context) {
	defer func() {
		if err := recover(); err != nil {
			logger.Error("UploadMaskImg:", err)
			errmsg.Abort(c, errmsg.FAIL, "执行失败，请重试")
			return
		}
	}()

	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginMaskReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	if oReq.MaskBase64 == "" {
		logger.Error("MaskBase64=为空")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 MaskBase64为空")
		return
	}
	if strings.HasPrefix(oReq.MaskBase64, "data:") {
		tmpAry := strings.Split(oReq.MaskBase64, ",")
		if len(tmpAry) != 2 {
			logger.Error("分隔Base64前缀出错")
			errmsg.Abort(c, errmsg.FAIL, "分隔Base64前缀出错")
			return
		}
		oReq.MaskBase64 = tmpAry[1]
	}

	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	maskImg, err := myimg.Base64ToImg(oReq.MaskBase64)
	if err != nil {
		logger.Error(err, "====", oReq.MaskBase64)
		errmsg.Abort(c, errmsg.FAIL, "base64转图片失败")
		return
	}

	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeHead || shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBigHead {
		logger.Info("开始将蒙版转换为黑底", shopOrigin.ID)
		maskImg = myimg.TurnMaskTransparentToWhite(maskImg)

		bodyImg, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.BodyImg)
		if err != nil {
			logger.Error(err, "====", oReq.MaskBase64)
			errmsg.Abort(c, errmsg.FAIL, "获取透明主体图失败")
			return
		}
		maskImg = myimg.MaskCorrectFromBody(bodyImg, maskImg, 0)
		maskImg = myimg.MaskInvert(maskImg)
		//if claims.UserId != 7 { //7为刘隆ID
		//	maskImg = myimg.MaskInvert(maskImg)
		//}

		//bodyImg, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.BodyImg)
		//if err != nil {
		//	logger.Error(err, "====")
		//	errmsg.Abort(c, errmsg.FAIL, "获取主体透明图失败")
		//	return
		//}
		//maskImg1 := myimg.MaskFromBody(bodyImg, maskImg, false, 0) //蒙版和主体透明图图叠加，获取蒙版中主体不透明的像素

		//maskImg = myimg.TurnTransparentToBlackForHead(maskImg)

	} else if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeHeadAndBackground { //换头换背景，需要将头映射到透明主体图
		maskImg = myimg.TurnMaskTransparentToWhite(maskImg)
		//bodyImg, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.BodyImg)
		//if err != nil {
		//	logger.Error(err)
		//	errmsg.Abort(c, errmsg.FAIL, "获取透明主体图失败")
		//	return
		//}
		//maskImg = myimg.MaskFromBody(bodyImg, maskImg, true)
	} else {
		maskImg = myimg.TurnMaskTransparentToWhite(maskImg)
	}

	maskPath := service.ImgPath.GetMaskPath(shopOrigin.OriginImg)
	absoluteMaskPath := config.DiffusionFilePath + maskPath
	if err := myimg.ImgToPngFile(maskImg, absoluteMaskPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存蒙版图失败")
		return
	}
	if err := shopOrigin.SetMaskImg(maskPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存蒙版图路径失败")
		return
	}

	msg = "蒙版图上传成功"

	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["mask_img_url"] = config.DiffusionDomain + shopOrigin.MaskImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GenMaskFromBodyImg(c *gin.Context) {
	var code int
	var msg string
	result := make(map[string]interface{})
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginGenMaskUpReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	absoluteBodyPath := config.DiffusionFilePath + shopOrigin.BodyImg
	maskImgPath := service.ImgPath.GetMaskPath(shopOrigin.OriginImg)
	absoluteMaskImgPath := config.DiffusionFilePath + maskImgPath

	bodyImg, _, err := myimg.FileToImg(absoluteBodyPath)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取主体图失败")
		return
	}

	maskImg := myimg.TurnMaskTransparentToWhite(bodyImg)

	if err := myimg.ImgToPngFile(maskImg, absoluteMaskImgPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存蒙版图失败")
		return
	}
	if err := shopOrigin.SetMaskImg(maskImgPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存蒙版图路径失败")
		return
	}

	msg = "蒙版图生成成功"

	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["mask_img_url"] = config.DiffusionDomain + shopOrigin.MaskImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GenMaskUpImg(c *gin.Context) {
	var code int
	var msg string
	result := make(map[string]interface{})
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginGenMaskUpReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	absoluteCropPath := config.DiffusionFilePath + shopOrigin.CropImg
	absoluteMaskImgPath := config.DiffusionFilePath + shopOrigin.MaskImg

	maskUpUrl, err := service.AliMask.Analyze(absoluteCropPath, absoluteMaskImgPath)
	if err != nil {
		logger.Error(err, absoluteCropPath, " ", absoluteMaskImgPath)
		result["origin_img_url"] = config.DiffusionDomain + shopOrigin.CropImg
		result["mask_img_url"] = config.DiffusionDomain + shopOrigin.MaskImg
		c.JSON(http.StatusOK, gin.H{
			"code":   errmsg.FAIL,
			"msg":    "生成精细化蒙版失败",
			"result": result,
		})
		return
	}
	maskUpBase64, err := tools.GetImgBase64FromUrl(maskUpUrl)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "下载精细化蒙版失败")
		return
	}
	maskUpImg, err := myimg.Base64ToImg(maskUpBase64)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "base64转图片失败")
		return
	}

	maskUpPath := service.ImgPath.GetMaskUpPath(shopOrigin.OriginImg)
	absoluteMaskUpPath := config.DiffusionFilePath + maskUpPath

	if err := myimg.ImgToPngFile(maskUpImg, absoluteMaskUpPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存精细化蒙版失败")
		return
	}
	if err := shopOrigin.SetMaskUpImg(maskUpPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存精细化蒙版路径失败")
		return
	}

	msg = "精细蒙版图生成成功"

	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["maskup_img_url"] = config.DiffusionDomain + shopOrigin.MaskUpImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) InvertMaskImg(c *gin.Context) {
	var code int
	var msg string
	result := make(map[string]interface{})
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginInvertMaskReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	absoluteBodyPath := config.DiffusionFilePath + shopOrigin.BodyImg
	absoluteMaskImgPath := config.DiffusionFilePath + shopOrigin.MaskImg

	bodyImg, _, err := myimg.FileToImg(absoluteBodyPath)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取透明主体图失败")
		return
	}
	maskImg, _, err := myimg.FileToImg(absoluteMaskImgPath)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取透明主体图失败")
		return
	}
	maskUpImg := myimg.MaskFromBody(bodyImg, maskImg, true, oReq.MaskPixels)

	maskUpPath := service.ImgPath.GetMaskUpPath(shopOrigin.OriginImg)
	absoluteMaskUpPath := config.DiffusionFilePath + maskUpPath

	if err := myimg.ImgToPngFile(maskUpImg, absoluteMaskUpPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存精细化蒙版失败")
		return
	}
	if err := shopOrigin.SetMaskUpImg(maskUpPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存精细化蒙版路径失败")
		return
	}

	msg = "精细蒙版图生成成功"

	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["maskup_img_url"] = config.DiffusionDomain + shopOrigin.MaskUpImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GenMapImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginGenMaskReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	if shopOrigin.BodyImg == "" {
		logger.Error("主体图不存在", oReq)
		errmsg.Abort(c, errmsg.FAIL, "主体图不存在")
		return
	}

	if shopOrigin.MaskUpImg == "" {
		logger.Error("精细蒙版图不存在", oReq)
		errmsg.Abort(c, errmsg.FAIL, "精细蒙版图不存在")
		return
	}

	bodyImg, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.BodyImg)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取主体图失败")
		return
	}
	maskUpImg, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.MaskUpImg)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取蒙版图失败")
		return
	}
	mapImg := myimg.MapBodyFromMask(bodyImg, maskUpImg, false, "")
	mapPath := service.ImgPath.GetMapPath(shopOrigin.OriginImg)
	if err := myimg.ImgToPngFile(mapImg, config.DiffusionFilePath+mapPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "存储映射图失败")
		return
	}
	if err := shopOrigin.SetMapImg(mapPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "存储映射图路径失败")
		return
	}

	msg = "映射图生成完成"
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["map_img_url"] = config.DiffusionDomain + shopOrigin.MapImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GenMapImgInvert(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginGenMaskReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	if shopOrigin.BodyImg == "" {
		logger.Error("主体图不存在", oReq)
		errmsg.Abort(c, errmsg.FAIL, "主体图不存在")
		return
	}

	if shopOrigin.MaskUpImg == "" {
		logger.Error("精细蒙版图不存在", oReq)
		errmsg.Abort(c, errmsg.FAIL, "精细蒙版图不存在")
		return
	}

	bodyBgImg, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.BodyBgImg)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取主体图失败")
		return
	}
	bgColor := shopOrigin.BodyBgColor
	if bgColor == "" {
		bgColor = "255,255,255,255"
	}

	//maskImg, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.MaskImg)
	//if err != nil {
	//	logger.Error(err)
	//	errmsg.Abort(c, errmsg.FAIL, "获取蒙版图失败")
	//	return
	//}
	//mapImg := myimg.BodyFromMask(bodyBgImg, maskImg, true, bgColor)

	maskUpImg, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.MaskUpImg)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取精细蒙版图失败")
		return
	}
	mapImg := myimg.MapBodyFromMask(bodyBgImg, maskUpImg, false, bgColor)

	mapPath := service.ImgPath.GetMapPath(shopOrigin.OriginImg)
	if err := myimg.ImgToPngFile(mapImg, config.DiffusionFilePath+mapPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "存储映射图失败")
		return
	}
	if err := shopOrigin.SetMapImg(mapPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "存储映射图路径失败")
		return
	}

	msg = "映射图生成完成"
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["map_img_url"] = config.DiffusionDomain + shopOrigin.MapImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) SdImg2img(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginImg2ImgReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	if oReq.BatchSize == 0 && oReq.SdServer == "" {
		oReq.BatchSize = 3
	}

	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	if oReq.ShopFaceId > 0 {
		if err := shopOrigin.SetShopFace(oReq.ShopFaceId); err != nil {
			logger.Error(err, oReq)
			errmsg.Abort(c, errmsg.FAIL, "设置脸膜参数错误")
			return
		}
	}

	//beautifulRealistic_v60.safetensors [bc2f30f4ad]    //.safetensors这个后缀是要加的 可以拷贝sd webui 左上角的模型选择值
	//chilloutmix_NiPrunedFp16Fix.safetensors [59ffe2243a]
	//特别提示 .safetensors这个后缀是要加的(可以拷贝sd webui左上角的模型选择值)，如果webui左上角的模型没有出现hash值，需要选中加载出现hash值了才会生效

	parameters := make(map[string]interface{})
	imagesPath := make(map[string]string)

	fields := make(map[string]interface{})
	fields["sd_server"] = oReq.SdServer
	fields["sdapi"] = "Img2img"
	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBackground && shopOrigin.ShopSceneId > 0 {
		fields["sdapi"] = "Txt2img"
	}

	var sdParam model.SdParm

	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBackground {
		if err := sdParam.GetById(uint(5)); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "ShopFaceId 获取信息失败")
			return
		}
	} else {
		if err := sdParam.GetById(shopOrigin.ShopFaceId); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "ShopFaceId 获取信息失败")
			return
		}
	}

	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeHead {

		if tmpParameters, err := service.SdClient.GenParameters(sdParam.Parameters, sdParam.ControlnetUnits, "openpose,depth_midas"); err != nil {
			logger.Error("sdParamId:", sdParam.ID, err)
			errmsg.Abort(c, errmsg.FAIL, "生成Parameters参数失败")
			return
		} else {
			parameters = tmpParameters
		}
		//parameters["init_images"] = initImages
		//parameters["sampler_name"] = "DDIM" //暂时全部用这个

		parametersStr := tools.GetJsonFromMap(parameters)

		if strings.Contains(parametersStr, "{==={InitImg}===}") {
			imagesPath["InitImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={BodyBgImg}===}") {
			imagesPath["BodyBgImg"] = shopOrigin.BodyBgImg
		}
		if strings.Contains(parametersStr, "{==={CropImg}===}") {
			imagesPath["CropImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={MaskImg}===}") {
			imagesPath["MaskImg"] = shopOrigin.MaskImg
		}
		if strings.Contains(parametersStr, "{==={MaskUpImg}===}") {
			imagesPath["MaskUpImg"] = shopOrigin.MaskImg
		}
		//if strings.Contains(parametersStr, "{==={MapImg}===}") {
		//	imagesPath["MapImg"] = shopOrigin.MapImg
		//}

	} else if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBigHead {

		if shopOrigin.BodyBgColor == "255,255,255,255" {
			if tmpParameters, err := service.SdClient.GenParameters(sdParam.Parameters, sdParam.ControlnetUnits, "openpose,lineart_realistic"); err != nil {
				logger.Error("sdParamId:", sdParam.ID, err)
				errmsg.Abort(c, errmsg.FAIL, "生成Parameters参数失败")
				return
			} else {
				parameters = tmpParameters
			}
		} else {
			if tmpParameters, err := service.SdClient.GenParameters(sdParam.Parameters, sdParam.ControlnetUnits, "openpose,depth_midas"); err != nil {
				logger.Error("sdParamId:", sdParam.ID, err)
				errmsg.Abort(c, errmsg.FAIL, "生成Parameters参数失败")
				return
			} else {
				parameters = tmpParameters
			}
		}

		sdW, sdH := service.ShopOrigin.GetSdSize(shopOrigin.CropWidth, shopOrigin.CropHeight)

		parameters["width"] = sdW
		parameters["height"] = sdH

		parametersStr := tools.GetJsonFromMap(parameters)

		if strings.Contains(parametersStr, "{==={InitImg}===}") {
			imagesPath["InitImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={BodyBgImg}===}") {
			imagesPath["BodyBgImg"] = shopOrigin.BodyBgImg
		}
		if strings.Contains(parametersStr, "{==={CropImg}===}") {
			imagesPath["CropImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={MaskImg}===}") {
			imagesPath["MaskImg"] = shopOrigin.MaskImg
		}
		if strings.Contains(parametersStr, "{==={MaskUpImg}===}") {
			imagesPath["MaskUpImg"] = shopOrigin.MaskImg
		}
	} else if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBackground {
		//图生图接口 换纯色背景图 需要带颜色的主题图 主体黑白蒙版图
		logger.Info("更换纯色背景逻辑开始")

		if tmpParameters, err := service.SdClient.GenParameters(sdParam.Parameters, sdParam.ControlnetUnits, "openpose,lineart_realistic"); err != nil {
			logger.Error("sdParamId:", sdParam.ID, err)
			errmsg.Abort(c, errmsg.FAIL, "生成Parameters参数失败")
			return
		} else {
			parameters = tmpParameters
		}
		parameters["sampler_name"] = "DDIM" //纯色背景用这个
		parametersStr := tools.GetJsonFromMap(parameters)

		if strings.Contains(parametersStr, "{==={InitImg}===}") {
			imagesPath["InitImg"] = shopOrigin.BodyBgImg
		}
		if strings.Contains(parametersStr, "{==={BodyBgImg}===}") {
			imagesPath["BodyBgImg"] = shopOrigin.BodyBgImg
		}
		if strings.Contains(parametersStr, "{==={CropImg}===}") {
			imagesPath["CropImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={MaskImg}===}") {
			imagesPath["MaskImg"] = shopOrigin.MaskImg
		}
		if strings.Contains(parametersStr, "{==={MaskUpImg}===}") {
			imagesPath["MaskUpImg"] = shopOrigin.MaskImg
		}
		if strings.Contains(parametersStr, "{==={MapImg}===}") {
			imagesPath["MapImg"] = shopOrigin.MapImg
		}

	} else if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeHeadAndBackground {

		if tmpParameters, err := service.SdClient.GenParameters(sdParam.Parameters, sdParam.ControlnetUnits, "openpose,lineart_realistic"); err != nil {
			logger.Error("sdParamId:", sdParam.ID, err)
			errmsg.Abort(c, errmsg.FAIL, "生成Parameters参数失败")
			return
		} else {
			parameters = tmpParameters
		}
		prompt := parameters["prompt"].(string)
		if !strings.Contains(prompt, "(simple background:1.5)") {
			prompt += ",(simple background:1.5)"
			parameters["prompt"] = prompt
		}

		//parameters["sampler_name"] = "DDIM" //暂时全部用这个
		parametersStr := tools.GetJsonFromMap(parameters)

		if strings.Contains(parametersStr, "{==={InitImg}===}") {
			imagesPath["InitImg"] = shopOrigin.BodyBgImg
		}
		if strings.Contains(parametersStr, "{==={BodyBgImg}===}") {
			imagesPath["BodyBgImg"] = shopOrigin.BodyBgImg
		}
		if strings.Contains(parametersStr, "{==={CropImg}===}") {
			imagesPath["CropImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={MaskImg}===}") {
			imagesPath["MaskImg"] = shopOrigin.MaskUpImg
		}
		if strings.Contains(parametersStr, "{==={MaskUpImg}===}") {
			imagesPath["MaskUpImg"] = shopOrigin.MaskUpImg
		}
		if strings.Contains(parametersStr, "{==={MapImg}===}") {
			imagesPath["MapImg"] = shopOrigin.MapImg
		}
	} else if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeSence {
		logger.Info("更换场景逻辑开始")
		fields["sdapi"] = "Txt2img" //调用文生图接口

		if tmpParameters, err := service.SdClient.GenParameters(sdParam.Parameters, sdParam.ControlnetUnits, "lineart_realistic"); err != nil {
			logger.Error("sdParamId:", sdParam.ID, err)
			errmsg.Abort(c, errmsg.FAIL, "生成Parameters参数失败")
			return
		} else {
			parameters = tmpParameters
		}
		delete(parameters, "init_images") //不需要参考图
		delete(parameters, "mask")        //不需要蒙版
		//parameters["sampler_name"] = "DDIM"        //暂时全部用这个
		parameters["denoising_strength"] = 0.5     //启用了高清修复时的去噪强度
		parameters["hr_scale"] = 2                 //放大倍数
		parameters["hr_upscaler"] = "R-ESRGAN 4x+" //采样器模型
		parameters["hr_second_pass_steps"] = 20

		parametersStr := tools.GetJsonFromMap(parameters)

		if strings.Contains(parametersStr, "{==={InitImg}===}") {
			imagesPath["InitImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={BodyBgImg}===}") {
			imagesPath["BodyBgImg"] = shopOrigin.BodyBgImg
		}
		if strings.Contains(parametersStr, "{==={CropImg}===}") {
			imagesPath["CropImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={MaskImg}===}") {
			imagesPath["MaskImg"] = shopOrigin.MaskImg
		}
		if strings.Contains(parametersStr, "{==={MaskUpImg}===}") {
			imagesPath["MaskUpImg"] = shopOrigin.MaskImg
		}
		if strings.Contains(parametersStr, "{==={MapImg}===}") {
			imagesPath["MapImg"] = shopOrigin.MapImg
		}
	} else if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBigHeadAndBackColor {

		if tmpParameters, err := service.SdClient.GenParameters(sdParam.Parameters, sdParam.ControlnetUnits, "openpose,lineart_realistic"); err != nil {
			logger.Error("sdParamId:", sdParam.ID, err)
			errmsg.Abort(c, errmsg.FAIL, "生成Parameters参数失败")
			return
		} else {
			parameters = tmpParameters
		}
		prompt := parameters["prompt"].(string)
		if !strings.Contains(prompt, "(simple background:1.5)") {
			prompt += ",(simple background:1.5)"
			parameters["prompt"] = prompt
		}

		sdW, sdH := service.ShopOrigin.GetSdSize(shopOrigin.CropWidth, shopOrigin.CropHeight)

		parameters["width"] = sdW
		parameters["height"] = sdH

		parametersStr := tools.GetJsonFromMap(parameters)

		if strings.Contains(parametersStr, "{==={InitImg}===}") {
			imagesPath["InitImg"] = shopOrigin.BodyBgImg
		}
		if strings.Contains(parametersStr, "{==={BodyBgImg}===}") {
			imagesPath["BodyBgImg"] = shopOrigin.BodyBgImg
		}
		if strings.Contains(parametersStr, "{==={CropImg}===}") {
			imagesPath["CropImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={MaskImg}===}") {
			imagesPath["MaskImg"] = shopOrigin.MaskUpImg
		}
		if strings.Contains(parametersStr, "{==={MaskUpImg}===}") {
			imagesPath["MaskUpImg"] = shopOrigin.MaskUpImg
		}
		if strings.Contains(parametersStr, "{==={MapImg}===}") {
			imagesPath["MapImg"] = shopOrigin.MapImg
		}
	}

	if oReq.Prompt != "" {
		if v, ok := parameters["prompt"]; ok {
			parameters["prompt"] = oReq.Prompt + "," + v.(string)
		}
	}
	if oReq.NegativePrompt != "" {
		if v, ok := parameters["negative_prompt"]; ok {
			parameters["negative_prompt"] = oReq.NegativePrompt + "," + v.(string)
		}
	}
	modelName := ""
	{

		prompt := parameters["prompt"].(string)
		if _, ok := parameters["override_settings"]; ok {
			overrideSettings := parameters["override_settings"].(map[string]interface{})
			if _, ok1 := overrideSettings["sd_model_checkpoint"]; ok1 {
				modelName = overrideSettings["sd_model_checkpoint"].(string)
			}
		}
		if oReq.SdModelCheckpoint != "" {
			modelName = oReq.SdModelCheckpoint
			if _, ok := parameters["override_settings"]; ok {
				v := parameters["override_settings"].(map[string]interface{})
				v["sd_model_checkpoint"] = oReq.SdModelCheckpoint
				parameters["override_settings"] = v
			}
		}

		ary := service.SdClient.GetLoras(prompt)
		//logger.Info(prompt)
		//logger.Info(ary)
		for _, lora := range ary {
			if service.SdClient.LoraExists(lora) == false {
				logger.Error("lora不存在:", lora)
				errmsg.Abort(c, errmsg.FAIL, "lora不存在:"+lora)
				return
			}
		}
	}

	fields["custom_app"] = "shopshow"
	fields["custom_data"] = ""
	fields["custom_md5"] = "{==={CustomMd5}===}"
	fields["custom_path"] = service.ImgPath.GetOutputDirectory(shopOrigin.OriginImg) + "{==={CustomMd5}===}.png"
	fields["parameters"] = tools.GetJsonFromMap(parameters)
	fields["images_path"] = imagesPath

	options := make(map[string]interface{})
	options["need_info"] = true
	fields["options"] = options

	pushJson := tools.GetJsonFromMap(fields)
	if err := shopOrigin.SetPushJson(pushJson); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "设置推送模板出错")
		return
	}
	result := make(map[string]interface{})
	msg = "图片生成中..."

	if outAry, message, err := obj.batchPush(claims.UserId, shopOrigin.ID, oReq.BatchSize, modelName); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "推送失败")
		return
	} else {
		msg = message
		result["outputs_md5"] = outAry
	}

	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}
func (obj _shopOriginApi) SdProgress(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginSdProgressReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.IdTask == "" {
		logger.Error("oReq.IdTask 为空")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 oReq.IdTask 为空")
		return
	}
	str, err := service.SdClient.SdProgress(oReq.IdTask)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "查询进度失败")
		return
	}
	result := make(map[string]interface{})
	result["progress"] = str
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
	logger.Info("Post Res:", str)
}
func (obj _shopOriginApi) batchPush(userId uint, id uint, batchSize int, modelName string) ([]string, string, error) {
	if batchSize > 10 {
		batchSize = 10
	}
	if batchSize <= 0 {
		batchSize = 1
	}
	tmpAry := make([]string, 0)
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(id); err != nil {
		logger.Error(err)
		return tmpAry, "", err
	}
	batchIndex := shopOrigin.BatchCount + 1
	totalIndex := shopOrigin.TotalCount
	if err := shopOrigin.SetBatchCount(batchIndex, shopOrigin.TotalCount+batchSize); err != nil {
		logger.Error(err)
		return tmpAry, "", err
	}

	var outImages []model.ShopOut
	for i := 0; i < batchSize; i++ {
		oMd5Str := fmt.Sprintf("shopshow,%s,%d,%d", time.Now().Format("2006-01-02 15:04:05.000"), tools.GetRandom(1000, 9999), i)
		has := md5.Sum([]byte(oMd5Str))
		md5Str := hex.EncodeToString(has[:])
		if len(md5Str) != 32 {
			err := errors.New("md5位数不正确")
			logger.Error(err)
			return tmpAry, "", err
		}

		path := service.ImgPath.GetOutputDirectory(shopOrigin.OriginImg) + md5Str + ".png"
		logger.Info(path)
		totalIndex += 1

		endJson := strings.Replace(shopOrigin.PushJson, "{==={CustomMd5}===}", md5Str, -1)
		outImg := model.ShopOut{
			UserId:      userId,
			OrigWhere:   enums.OrigWhereEnum.Shopshow,
			OrigId:      shopOrigin.ID,
			MadeType:    shopOrigin.MadeType,
			ShopFaceId:  shopOrigin.ShopFaceId,
			ShopSceneId: shopOrigin.ShopSceneId,
			ModelName:   modelName,
			BatchIndex:  batchIndex,
			TotalIndex:  totalIndex,
			Md5:         md5Str,
			PushJson:    endJson,
			Parameters:  "{}",
			Info:        "{}",
			PushAt:      time.Now(),
			//Path:       path,
		}
		outImages = append(outImages, outImg)
	}

	if err := outImages[0].BatchCreate(outImages); err != nil {
		logger.Error(err)
		return tmpAry, "", err
	}

	//failCount := 0
	successCount := 0

	lastStr := ""
	for _, item := range outImages {
		tmpAry = append(tmpAry, item.Md5)
		if str, err := service.SdClient.Sd2img(item.PushJson); err != nil {
			logger.Error(err, str)
			return tmpAry, "", err
		} else {
			logger.Info("Post Res:", str)
			lastStr = str
		}
		successCount++
	}
	if strings.HasPrefix(lastStr, "{") {
		m := tools.GetMapFromJson(lastStr)
		if v, ok := m["msg"]; ok {
			return tmpAry, v.(string), nil
		}
	}

	return tmpAry, "", nil
}

func (obj _shopOriginApi) MergeImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginSetFinalImgReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.ShopOriginId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	originAbsolutePath := config.DiffusionFilePath + shopOrigin.OriginImg

	var shopOut model.ShopOut
	if err := shopOut.GetByMd5(oReq.OutImgMd5); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片数据失败")
		return
	}
	if shopOut.Path == "" {
		errmsg.Abort(c, errmsg.FAIL, "获取图片数据失败1")
		return
	}
	outAbsolutePath := config.DiffusionFilePath + shopOut.Path

	outImg, _, err := myimg.FileToImg(outAbsolutePath)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片数据失败2")
		return
	}
	//outImgW := outImg.Bounds().Size().X
	//outImgH := outImg.Bounds().Size().Y

	originImg, _, err := myimg.FileToImg(originAbsolutePath)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取原始图片数据失败2")
		return
	}

	resizeImg := myimg.ResizeImg(shopOrigin.CropWidth, shopOrigin.CropHeight, outImg, false)
	mergeImg := myimg.MergeImg(originImg, resizeImg, shopOrigin.CropX, shopOrigin.CropY)

	mergeAbsolutePath := service.ImgPath.GetSdMergePath(outAbsolutePath)
	if err := myimg.ImgToPngFile(mergeImg, mergeAbsolutePath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "合并图片失败")
	}

	result := make(map[string]interface{})
	result["shop_origin_id"] = oReq.ShopOriginId
	result["out_img_md5"] = oReq.OutImgMd5
	result["merge_img_url"] = config.DiffusionDomain + service.ImgPath.GetSdMergePath(shopOut.Path)
	msg = "设置成功"
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) UploadFinalImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	id := uint(0)
	idStr, _ := c.GetPostForm("id")
	if idStr != "" {
		if num, err := strconv.Atoi(idStr); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		} else {
			id = uint(num)
		}
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	f, err := c.FormFile("final_img")
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}

	md5Str := shopOrigin.OriginMd5
	if len(md5Str) != 32 {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图名称参数")
		return
	}

	path := service.ImgPath.GetFinalPath(shopOrigin.OriginImg, tools.GetFileExt(f.Filename))
	absolutePath := config.DiffusionFilePath + path

	if err := c.SaveUploadedFile(f, absolutePath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}
	if err := shopOrigin.SetFinalImg(path); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片路径设置失败")
		return
	}

	msg = "上传成功"
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["final_img_url"] = config.DiffusionDomain + shopOrigin.FinalImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) SetFinalImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginSetFinalImgReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.ShopOriginId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}
	var shopOut model.ShopOut
	if err := shopOut.GetByMd5(oReq.OutImgMd5); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片数据失败")
		return
	}
	if shopOut.Path == "" {
		errmsg.Abort(c, errmsg.FAIL, "获取图片数据失败1")
		return
	}
	outAbsolutePath := config.DiffusionFilePath + shopOut.Path

	if shopOrigin.FinalImg != "" {
		finalPath := shopOrigin.FinalImg
		finalAbsolutePath := config.DiffusionFilePath + finalPath
		if b, _ := tools.PathFileExists(finalAbsolutePath); b {
			baseName := path.Base(finalAbsolutePath) // 输出 name.html
			ext := path.Ext(baseName)                // 输出 .html
			tmp := "_" + time.Now().Format("20060102150405") + ext
			newAbsolutePath := strings.Replace(finalAbsolutePath, ext, tmp, -1)
			//if err := os.Rename(finalAbsolutePath, newAbsolutePath); err != nil {
			//	logger.Error(err)
			//	errmsg.Abort(c, errmsg.FAIL, "备份图片数据失败")
			//	return
			//}
			if err := tools.CopyFile(finalAbsolutePath, newAbsolutePath); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "拷贝备份图片失败")
				return
			}
		}
	}

	{
		finalPath := service.ImgPath.GetFinalPath(shopOrigin.OriginImg, tools.GetFileExt(shopOut.Path))
		finalAbsolutePath := config.DiffusionFilePath + finalPath
		outImg, _, err := myimg.FileToImg(outAbsolutePath)
		if err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "获取图片数据失败")
			return
		}
		err = myimg.ImgToPngFile(outImg, finalAbsolutePath)
		if err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存交付图片数据失败")
			return
		}
		//if err := tools.CopyFile(outAbsolutePath, finalAbsolutePath); err != nil {
		//	logger.Error(err)
		//	errmsg.Abort(c, errmsg.FAIL, "拷贝图片失败")
		//	return
		//}
		if err := shopOrigin.SetFinalImg(finalPath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "图片路径设置失败")
			return
		}
	}

	result := make(map[string]interface{})
	result["shop_origin_id"] = oReq.ShopOriginId
	result["out_img_md5"] = oReq.OutImgMd5
	result["final_img_url"] = config.DiffusionDomain + shopOrigin.FinalImg
	msg = "设置成功"
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) List(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	var oReq shopOriginListReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	shopOrigin := model.ShopOrigin{}
	ary := make([]shopOriginItem, 0)
	result := make(map[string]interface{})
	if total, err := shopOrigin.GetList(&ary, oReq.Id, oReq.UserId, oReq.MadeType, oReq.Title, oReq.Page, oReq.PageSize); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询失败")
		return
	} else {
		result["total"] = total
	}

	for i := 0; i < len(ary); i++ {
		//ary[i].PushJson = ""
		if ary[i].OriginImg != "" {
			ary[i].OriginImgUrl = config.DiffusionDomain + ary[i].OriginImg
			ary[i].OriginJpgImgUrl = config.DiffusionDomain + service.ImgPath.GetOriginJpgPath(ary[i].OriginImg)
			ary[i].OriginWhiteImgUrl = config.DiffusionDomain + service.ImgPath.GetOriginWhitePath(ary[i].OriginImg)

			if b, _ := tools.PathFileExists(config.DiffusionFilePath + service.ImgPath.GetOriginMaskPath(ary[i].OriginImg)); b {
				ary[i].OriginMaskImgUrl = config.DiffusionDomain + service.ImgPath.GetOriginMaskPath(ary[i].OriginImg)
			} else {
				ary[i].OriginMaskImgUrl = ""
			}

		}
		if ary[i].CropImg != "" {
			ary[i].CropImgUrl = config.DiffusionDomain + ary[i].CropImg
		}
		if ary[i].BodyImg != "" {
			ary[i].BodyImgUrl = config.DiffusionDomain + ary[i].BodyImg
		}
		if ary[i].BodyBgImg != "" {
			ary[i].BodyBgImgUrl = config.DiffusionDomain + ary[i].BodyBgImg
		}
		if ary[i].MaskImg != "" {
			ary[i].MaskImgUrl = config.DiffusionDomain + ary[i].MaskImg
		}
		if ary[i].MaskUpImg != "" {
			ary[i].MaskUpImgUrl = config.DiffusionDomain + ary[i].MaskUpImg
		}
		if ary[i].MapImg != "" {
			ary[i].MapImgUrl = config.DiffusionDomain + ary[i].MapImg
		}
		if ary[i].FinalImg != "" {
			ary[i].FinalImgUrl = config.DiffusionDomain + ary[i].FinalImg
		}
	}
	result["items"] = ary

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

package shopshow

import (
	"center-ai/enums"
	"center-ai/middleware"
	"center-ai/model"
	"center-ai/service"
	"center-ai/utils/config"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/myimg"
	"center-ai/utils/tools"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

type _shopOriginApi struct {
}

var ShopOriginApi _shopOriginApi

type shopOriginAddReq struct {
	Id     uint   `json:"id"`
	Title  string `json:"title"`
	Remark string `json:"remark"`
}

type shopOriginSetParamReq struct {
	Id          uint   `json:"id"`
	MadeType    int    `json:"made_type"`
	BodyBgColor string `json:"body_bg_color"`
	ShopFaceId  uint   `json:"shop_face_id"`
	ShopSceneId uint   `json:"shop_scene_id"`
	CropWidth   int    `json:"crop_width"`
	CropHeight  int    `json:"crop_height"`
}

type shopOriginMaskReq struct {
	Id         uint   `json:"id"`
	MaskBase64 string `json:"mask_base64"`
}

type shopOriginGenMaskReq struct {
	Id                   uint    `json:"id"`
	SegmentParm          string  `json:"segment_parm"`
	StabilityScoreThresh float64 `json:"stability_score_thresh"`
	PredIouThresh        float64 `json:"pred_iou_thresh"`
	StabilityScoreOffset int     `json:"stability_score_offset"`
	PointsPerBatch       int     `json:"points_per_batch"`
}

type shopOriginMaskStatusReq struct {
	ImgMd5 string `json:"img_md5"`
}

type shopOriginGenMaskUpReq struct {
	Id uint `json:"id"`
}

type shopOriginImg2ImgReq struct {
	Id       uint   `json:"id"`
	SdServer string `json:"sd_server"`
}

type shopOriginSdProgressReq struct {
	IdTask string `json:"id_task"`
}

type shopOriginListReq struct {
	Id       uint   `json:"id"`
	UserId   uint   `json:"user_id"`
	Sex      int    `json:"sex"`
	Title    string `json:"title"`
	Hash     string `json:"hash"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type shopOriginSetFinalImgReq struct {
	ShopOriginId uint   `json:"shop_origin_id"`
	OutImgMd5    string `json:"out_img_md5"`
}

type shopOriginItem struct {
	ID           uint           `json:"id"`
	UserId       uint           `json:"user_id"`
	Title        string         `json:"title"`
	OriginMd5    string         `json:"origin_md5"`
	OriginImg    string         `json:"-"`
	OriginImgUrl string         `json:"origin_img_url"`
	OriginWidth  int            `json:"origin_width"`
	OriginHeight int            `json:"origin_height"`
	MadeType     int            `json:"made_type"`
	ShopFaceId   uint           `json:"shop_face_id"`
	ShopSceneId  uint           `json:"shop_scene_id"`
	BodyBgColor  string         `json:"body_bg_color"`
	CropImg      string         `json:"-"`
	CropImgUrl   string         `json:"crop_img_url"`
	BodyImg      string         `json:"-"`
	BodyImgUrl   string         `json:"body_img_url"`
	BodyBgImg    string         `json:"-"`
	BodyBgImgUrl string         `json:"bodybg_img_url"`
	MaskImg      string         `json:"mask_img"`
	MaskImgUrl   string         `json:"mask_img_url"`
	MaskUpImg    string         `json:"-"`
	MaskUpImgUrl string         `json:"maskup_img_url"`
	MapImg       string         `json:"-"`
	MapImgUrl    string         `json:"map_img_url"`
	PushJson     string         `json:"push_json"`
	FinalImg     string         `json:"final_img"`
	FinalImgUrl  string         `json:"final_img_url"`
	BatchCount   int            `json:"batch_count"`
	Remark       string         `json:"remark"`
	CreatedAt    model.JsonTime `json:"created_at"`
}

func (obj _shopOriginApi) Add(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	var oReq shopOriginAddReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var shopOrigin model.ShopOrigin
	if oReq.Id > 0 {
		if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
			errmsg.Abort(c, errmsg.FAIL, "权限不足")
			return
		}
		if err := shopOrigin.GetById(oReq.Id); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
			return
		}
		shopOrigin.Title = oReq.Title
		shopOrigin.Remark = oReq.Remark

		if err := shopOrigin.Save(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存信息失败")
			return
		}
	} else {
		if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
			errmsg.Abort(c, errmsg.FAIL, "权限不足")
			return
		}

		shopOrigin = model.ShopOrigin{
			UserId:   claims.UserId,
			Title:    oReq.Title,
			PushJson: "{}",
			Remark:   oReq.Remark,
		}
		if err := shopOrigin.Save(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存信息失败")
			return
		}
		oMd5Str := fmt.Sprintf("%d,%s", shopOrigin.ID, shopOrigin.CreatedAt.Format("2006-01-02 15:04:05.000"))
		md5Str := tools.GetMd5(oMd5Str)
		if md5Str == "" {
			logger.Error("MD5字符串生成失败 shopOriginId", shopOrigin.ID)
			errmsg.Abort(c, errmsg.FAIL, "MD5字符串生成失败")
			return
		}
		if err := shopOrigin.SetOriginMd5(md5Str); err != nil {
			logger.Error(err, "设置MD5字符串生成失败 shopOriginId", shopOrigin.ID)
			errmsg.Abort(c, errmsg.FAIL, "设置MD5字符串失败")
			return
		}
	}
	msg = "添加成功"
	if oReq.Id > 0 {
		msg = "修改成功"
	}
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) UploadOriginImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	id := uint(0)
	idStr, _ := c.GetPostForm("id")
	if idStr != "" {
		if num, err := strconv.Atoi(idStr); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		} else {
			id = uint(num)
		}
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	f, err := c.FormFile("origin_img")
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}

	md5Str := shopOrigin.OriginMd5
	if len(md5Str) != 32 {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参考图名称生成失败")
		return
	}

	ext := path.Ext(f.Filename) // 输出 .html
	path := service.ImgPath.GetOriginPath(md5Str, ext)
	absolutePath := config.DiffusionFilePath + path
	directory := filepath.Dir(absolutePath) // 获取目录路径
	// 创建目录，存在则不创建，不存在则创建
	if err := os.MkdirAll(directory, 0755); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "目录创建失败")
		return
	}

	if err := c.SaveUploadedFile(f, absolutePath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}

	img, _, err := myimg.FileToImg(absolutePath)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}
	width := img.Bounds().Size().X
	height := img.Bounds().Size().Y

	if err := shopOrigin.SetOriginImg(path, width, height); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片路径设置失败")
		return
	}

	msg = "添加成功"
	if id > 0 {
		msg = "修改成功"
	}
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["origin_img_url"] = config.DiffusionDomain + shopOrigin.OriginImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) SetParam(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	var oReq shopOriginSetParamReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	if oReq.CropWidth == 0 {
		oReq.CropWidth = 1496
	}
	if oReq.CropHeight == 0 {
		oReq.CropHeight = 2000
	}
	if oReq.ShopSceneId > 0 {
		oReq.BodyBgColor = ""
	}

	var shopOrigin model.ShopOrigin

	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
		return
	}
	shopOrigin.BodyBgColor = oReq.BodyBgColor
	shopOrigin.MadeType = oReq.MadeType
	shopOrigin.ShopFaceId = oReq.ShopFaceId
	shopOrigin.ShopSceneId = oReq.ShopSceneId

	if err := shopOrigin.Save(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存信息失败")
		return
	}

	img, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.OriginImg)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取原始图失败")
		return
	}
	cropPath := "" + strings.Replace(shopOrigin.OriginImg, "origin_", "crop_", -1)
	absoluteCropPath := config.DiffusionFilePath + cropPath

	width := shopOrigin.OriginWidth
	height := shopOrigin.OriginHeight
	targetWidth := 1496
	targetHeight := 2000
	if width < 1496 && height < 2000 {
		// 计算宽度和高度的缩放比例，选择较小的那个比例以保持等比例放大
		widthRatio := float64(targetWidth) / float64(width)
		heightRatio := float64(targetHeight) / float64(height)
		scale := widthRatio
		if heightRatio < widthRatio {
			scale = heightRatio
		}

		// 计算新的宽度和高度
		newWidth := int(float64(width) * scale)
		newHeight := int(float64(height) * scale)
		img = myimg.ResizeImg(newWidth, newHeight, img, true)
	}

	cropImg := myimg.WindowImg(1496, 2000, img)
	if err := myimg.ImgToFile(cropImg, absoluteCropPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存裁剪图失败")
		return
	}
	width = cropImg.Bounds().Size().X
	height = cropImg.Bounds().Size().Y

	//if shopOrigin.OriginWidth > oReq.CropWidth || shopOrigin.OriginHeight > oReq.CropHeight {
	//	cropImg := myimg.ResizeImg(oReq.CropWidth, oReq.CropHeight, img, true)
	//	if err := myimg.ImgToFile(cropImg, config.DiffusionFilePath+cropPath); err != nil {
	//		logger.Error(err)
	//		errmsg.Abort(c, errmsg.FAIL, "保存裁剪图失败")
	//		return
	//	}
	//	width = cropImg.Bounds().Size().X
	//	height = cropImg.Bounds().Size().Y
	//} else {
	//	if err := myimg.ImgToFile(img, config.DiffusionFilePath+cropPath); err != nil {
	//		logger.Error(err)
	//		errmsg.Abort(c, errmsg.FAIL, "保存裁剪图失败")
	//		return
	//	}
	//}
	if err := shopOrigin.SetCropImg(cropPath, width, height); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存裁剪图信息失败")
		return
	}

	msg = "参数设置成功"
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["crop_img_url"] = config.DiffusionDomain + shopOrigin.CropImg

	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GenBodyImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginGenMaskReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}
	logger.Info("裁剪图路径：", shopOrigin.CropImg)
	if shopOrigin.CropImg == "" {
		logger.Error("裁剪图不存在", oReq)
		errmsg.Abort(c, errmsg.FAIL, "裁剪图不存在")
		return
	}

	if bodyImgUrl, err1 := service.AliMask.SegmentHDBody(config.DiffusionFilePath + shopOrigin.CropImg); err1 != nil {
		logger.Error(err1)
		errmsg.Abort(c, errmsg.FAIL, "生成主体图失败")
		return
	} else {

		//img, _, err := tools.GetImgFromUrl(bodyImgUrl)
		//if err != nil {
		//	logger.Error(err)
		//	errmsg.Abort(c, errmsg.FAIL, "下载主体图失败")
		//	return
		//}
		bodyPath := service.ImgPath.GetBodyPath(shopOrigin.OriginImg)
		absoluteBodyPath := config.DiffusionFilePath + bodyPath

		if err := tools.SaveImgFromUrl(bodyImgUrl, absoluteBodyPath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "下载主体图失败")
			return
		}

		//bodyImg := myimg.TurnToBackgroundColor(img, "0,0,0,0")
		//
		//if err := myimg.ImgToFile(bodyImg, absoluteBodyPath); err != nil {
		//	logger.Error(err)
		//	errmsg.Abort(c, errmsg.FAIL, "保存主体图失败")
		//	return
		//}
		if err := shopOrigin.SetBodyImg(bodyPath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存主体图路径失败")
			return
		}

		img, _, err := myimg.FileToImg(absoluteBodyPath)
		if err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "获取主体图失败")
			return
		}

		bodyBgImg := myimg.TurnToBackgroundColor(img, shopOrigin.BodyBgColor)

		bodyBgPath := service.ImgPath.GetBodyBgPath(shopOrigin.OriginImg)
		absoluteBodyBgPath := config.DiffusionFilePath + bodyBgPath

		if err := myimg.ImgToFile(bodyBgImg, absoluteBodyBgPath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存带背景主体图失败")
			return
		}
		if err := shopOrigin.SetBodyBgImg(bodyBgPath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存带背景主体图路径失败")
			return
		}
	}

	msg = "主体图生成完成"
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["body_img_url"] = config.DiffusionDomain + shopOrigin.BodyImg
	result["bodybg_img_url"] = config.DiffusionDomain + shopOrigin.BodyBgImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GenMaskImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginGenMaskReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}
	logger.Info("裁剪图路径：", shopOrigin.CropImg)
	if shopOrigin.CropImg == "" {
		logger.Error("裁剪图不存在", oReq)
		errmsg.Abort(c, errmsg.FAIL, "裁剪图不存在")
		return
	}

	//if bol, err := service.SegmentService.PushJson(shopOrigin.OriginMd5, shopOrigin.CropImg, oReq.StabilityScoreThresh, oReq.PredIouThresh, oReq.StabilityScoreOffset, oReq.PointsPerBatch); err != nil {
	//	logger.Error("bol", bol, err)
	//	errmsg.Abort(c, errmsg.FAIL, "数据推送失败")
	//	return
	//}

	imgPath := shopOrigin.CropImg
	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeHead {
		imgPath = shopOrigin.BodyBgImg
	}

	if bol, err := service.SegmentService.PushJsonSegment(shopOrigin.OriginMd5, imgPath, oReq.SegmentParm); err != nil {
		logger.Error("bol", bol, err)
		errmsg.Abort(c, errmsg.FAIL, "数据推送失败")
		return
	}

	msg = "正在解析蒙版"
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GetMaskStatus(c *gin.Context) {
	var code int
	var msg string
	var oReq shopOriginMaskStatusReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	result := make(map[string]interface{})
	logger.Info("mask getstatus ", oReq.ImgMd5)

	if outDataStr, err := service.SegmentService.HGetJsonStr(oReq.ImgMd5); err != nil {
		logger.Error("val:", outDataStr, err)
		code = 1
		msg = "状态获取失败"
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
		return
	} else {
		c.Data(200, "application/json", []byte(outDataStr))
		return
	}
}

func (obj _shopOriginApi) ClearMask(c *gin.Context) {
	var code int
	var msg string
	var oReq shopOriginMaskStatusReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.ImgMd5 == "" {
		logger.Error("参数为空")
		errmsg.Abort(c, errmsg.FAIL, "参数为空")
		return
	}

	result := make(map[string]interface{})
	result["img_md5"] = oReq.ImgMd5

	if outDataStr, err := service.SegmentService.HRemoveJson(oReq.ImgMd5); err != nil {
		logger.Error("val:", outDataStr, err)
		code = 1
		msg = "蒙版缓存清除失败"
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
		return
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "蒙版缓存清除成功",
			"result": result,
		})
	}
}

func (obj _shopOriginApi) UploadMaskImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginMaskReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	if oReq.MaskBase64 == "" {
		logger.Error("MaskBase64=为空")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 MaskBase64为空")
		return
	}
	if strings.HasPrefix(oReq.MaskBase64, "data:") {
		tmpAry := strings.Split(oReq.MaskBase64, ",")
		if len(tmpAry) != 2 {
			logger.Error("分隔Base64前缀出错")
			errmsg.Abort(c, errmsg.FAIL, "分隔Base64前缀出错")
			return
		}
		oReq.MaskBase64 = tmpAry[1]
	}

	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	maskImg, err := myimg.Base64ToImg(oReq.MaskBase64)
	if err != nil {
		logger.Error(err, "====", oReq.MaskBase64)
		errmsg.Abort(c, errmsg.FAIL, "base64转图片失败")
		return
	}

	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeHead {
		logger.Info("开始将蒙版转换为黑底", shopOrigin.ID)
		maskImg = myimg.TurnTransparentToBlack(maskImg)
	} else {
		maskImg = myimg.TurnTransparentToWhite(maskImg)
	}

	maskPath := service.ImgPath.GetMaskPath(shopOrigin.OriginImg)
	absoluteMaskPath := config.DiffusionFilePath + maskPath
	if err := myimg.ImgToFile(maskImg, absoluteMaskPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存蒙版图失败")
		return
	}
	if err := shopOrigin.SetMaskImg(maskPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存蒙版图路径失败")
		return
	}

	msg = "蒙版图上传成功"

	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["mask_img_url"] = config.DiffusionDomain + shopOrigin.MaskImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GenMaskFromBodyImg(c *gin.Context) {
	var code int
	var msg string
	result := make(map[string]interface{})
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginGenMaskUpReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	absoluteBodyPath := config.DiffusionFilePath + shopOrigin.BodyImg
	maskImgPath := service.ImgPath.GetMaskPath(shopOrigin.OriginImg)
	absoluteMaskImgPath := config.DiffusionFilePath + maskImgPath

	bodyImg, _, err := myimg.FileToImg(absoluteBodyPath)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取主体图失败")
		return
	}

	maskImg := myimg.TurnTransparentToWhite(bodyImg)

	if err := myimg.ImgToFile(maskImg, absoluteMaskImgPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存蒙版图失败")
		return
	}
	if err := shopOrigin.SetMaskUpImg(maskImgPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存蒙版图路径失败")
		return
	}

	msg = "蒙版图生成成功"

	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["mask_img_url"] = config.DiffusionDomain + shopOrigin.MaskImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GenMaskUpImg(c *gin.Context) {
	var code int
	var msg string
	result := make(map[string]interface{})
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginGenMaskUpReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	absoluteCropPath := config.DiffusionFilePath + shopOrigin.CropImg
	absoluteMaskImgPath := config.DiffusionFilePath + shopOrigin.MaskImg

	maskUpUrl, err := service.AliMask.Analyze(absoluteCropPath, absoluteMaskImgPath)
	if err != nil {
		logger.Error(err, absoluteCropPath, " ", absoluteMaskImgPath)
		result["origin_img_url"] = config.DiffusionDomain + shopOrigin.CropImg
		result["mask_img_url"] = config.DiffusionDomain + shopOrigin.MaskImg
		c.JSON(http.StatusOK, gin.H{
			"code":   errmsg.FAIL,
			"msg":    "生成精细化蒙版失败",
			"result": result,
		})
		return
	}
	maskUpBase64, err := tools.GetImgBase64FromUrl(maskUpUrl)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "下载精细化蒙版失败")
		return
	}
	maskUpImg, err := myimg.Base64ToImg(maskUpBase64)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "base64转图片失败")
		return
	}

	maskUpPath := service.ImgPath.GetMaskUpPath(shopOrigin.OriginImg)
	absoluteMaskUpPath := config.DiffusionFilePath + maskUpPath

	if err := myimg.ImgToFile(maskUpImg, absoluteMaskUpPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存精细化蒙版失败")
		return
	}
	if err := shopOrigin.SetMaskUpImg(maskUpPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "保存精细化蒙版路径失败")
		return
	}

	msg = "精细蒙版图生成成功"

	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["maskup_img_url"] = config.DiffusionDomain + shopOrigin.MaskUpImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) GenMapImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginGenMaskReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	if shopOrigin.BodyImg == "" {
		logger.Error("主体图不存在", oReq)
		errmsg.Abort(c, errmsg.FAIL, "主体图不存在")
		return
	}

	if shopOrigin.MaskUpImg == "" {
		logger.Error("精细蒙版图不存在", oReq)
		errmsg.Abort(c, errmsg.FAIL, "精细蒙版图不存在")
		return
	}

	bodyImg, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.BodyImg)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取主体图失败")
		return
	}
	maskUpImg, _, err := myimg.FileToImg(config.DiffusionFilePath + shopOrigin.MaskUpImg)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取蒙版图失败")
		return
	}
	mapImg := myimg.BodyFromMask(bodyImg, maskUpImg)
	mapPath := service.ImgPath.GetMapPath(shopOrigin.OriginImg)
	if err := myimg.ImgToFile(mapImg, config.DiffusionFilePath+mapPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "存储映射图失败")
		return
	}
	if err := shopOrigin.SetMapImg(mapPath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "存储映射图路径失败")
		return
	}

	msg = "映射图生成完成"
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["map_img_url"] = config.DiffusionDomain + shopOrigin.MapImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) SdImg2img(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginImg2ImgReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.Id <= 0 {
		logger.Error("id=0")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 id=0")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.Id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	initImages := make([]string, 0)
	initImages = append(initImages, "{==={InitImg}===}")

	parameters := make(map[string]interface{})
	parameters["id_task"] = "{==={CustomMd5}===}"
	parameters["init_images"] = initImages
	parameters["mask"] = "{==={MaskImg}===}"
	parameters["prompt"] = "<lora:外模6:1>, 1 girl,solo,(simple background:1.5)"
	parameters["negative_prompt"] = "(Hat:1.5), (worst quality, low quality:1.4), (bad_prompt_version2:0.8), (EasyNegative:1.1), (badhandv4:1.1), text, name, letters, watermark"

	parameters["steps"] = 20
	//parameters["sampler_name"] = "DPM++ 2S a Karras"
	parameters["sampler_name"] = "DDIM"
	parameters["cfg_scale"] = 7
	parameters["seed"] = -1
	parameters["width"] = 1496
	parameters["height"] = 2000
	parameters["denoising_strength"] = 0.75
	//parameters["clip_skip"] = 2
	parameters["mask_blur"] = 4
	parameters["batch_size"] = 1

	parameters["resize_mode"] = 0            //缩放模式 Resize mode
	parameters["inpainting_mask_invert"] = 0 //蒙版模式 Mask mode  仅在mask存在的时候使用 蒙版反转,字面意思就是翻转:黑白换色,是SD的mask Model 默认绘制蒙板内容
	parameters["inpainting_fill"] = 1        //蒙版蒙住的内容 Masked content
	parameters["inpaint_full_res"] = true    //重绘区域 Inpaint area 仅在mask存在的时候使用 绘制区域,默认:true 全图 ,false:仅蒙板

	{
		////模型要放另外一个地方
		overrideSettings := make(map[string]interface{})
		overrideSettings["sd_model_checkpoint"] = "chilloutmix_NiPrunedFp16Fix [59ffe2243a]"
		//overrideSettings["clip_skip"] = 2
		overrideSettings["CLIP_stop_at_last_layers"] = 2
		parameters["override_settings"] = overrideSettings
	}

	imagesPath := make(map[string]string)

	controlNet := make(map[string]interface{})
	controlnetUnits := make([]map[string]interface{}, 0)

	//if shopOrigin.ShopFaceId > 0 {
	//	var sdParam model.SdParm
	//	if err := sdParam.GetById(shopOrigin.ShopFaceId); err != nil {
	//		logger.Error(err)
	//		errmsg.Abort(c, errmsg.FAIL, "ShopFaceId 获取信息失败")
	//		return
	//	}
	//	mParameters := tools.GetMapFromJson(sdParam.Parameters)
	//	if _, ok := mParameters["prompt"]; ok {
	//		parameters["prompt"] = mParameters["prompt"]
	//		if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeHead {
	//			parameters["prompt"] = mParameters["prompt"]
	//		} else if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeHeadAndBackground {
	//			parameters["prompt"] = mParameters["prompt"]
	//		}
	//	} else {
	//		errmsg.Abort(c, errmsg.FAIL, "prompt  信息获取失败")
	//		return
	//	}
	//}
	//
	//if shopOrigin.ShopSceneId > 0 {
	//	var sdParam model.SdParm
	//	if err := sdParam.GetById(shopOrigin.ShopSceneId); err != nil {
	//		logger.Error(err)
	//		errmsg.Abort(c, errmsg.FAIL, "ShopFaceId 获取信息失败")
	//		return
	//	}
	//	mParameters := tools.GetMapFromJson(sdParam.Parameters)
	//	if _, ok := mParameters["prompt"]; ok {
	//		if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBackground {
	//			parameters["prompt"] = mParameters["prompt"]
	//		} else if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeHeadAndBackground {
	//			parameters["prompt"] = parameters["prompt"].(string) + "," + mParameters["prompt"].(string)
	//		}
	//
	//	} else {
	//		errmsg.Abort(c, errmsg.FAIL, "prompt  信息获取失败")
	//		return
	//	}
	//}

	fields := make(map[string]interface{})
	fields["sd_server"] = oReq.SdServer
	fields["sdapi"] = "Img2img"
	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBackground && shopOrigin.ShopSceneId > 0 {
		fields["sdapi"] = "Txt2img"
	}

	if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeHead {
		var sdParam model.SdParm
		if err := sdParam.GetById(shopOrigin.ShopFaceId); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "ShopFaceId 获取信息失败")
			return
		}

		if tmpParameters, err := service.SdClient.GenParameters(sdParam.Parameters, sdParam.ControlnetUnits, "openpose,depth_midas"); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "生成Parameters参数失败")
			return
		} else {
			parameters = tmpParameters
		}
		parameters["init_images"] = initImages
		//parameters["sampler_name"] = "DDIM"

		parametersStr := tools.GetJsonFromMap(parameters)

		if strings.Contains(parametersStr, "{==={InitImg}===}") {
			imagesPath["InitImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={BodyBgImg}===}") {
			imagesPath["BodyBgImg"] = shopOrigin.BodyBgImg
		}
		if strings.Contains(parametersStr, "{==={CropImg}===}") {
			imagesPath["CropImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={MaskImg}===}") {
			imagesPath["MaskImg"] = shopOrigin.MaskImg
		}
		if strings.Contains(parametersStr, "{==={MaskUpImg}===}") {
			imagesPath["MaskUpImg"] = shopOrigin.MaskImg
		}
		if strings.Contains(parametersStr, "{==={MapImg}===}") {
			imagesPath["MapImg"] = shopOrigin.MapImg
		}

		/*
			//parameters["negative_prompt"] = "(worst quality, low quality:1.4), (bad_prompt_version2:0.8), EasyNegative, badhandv4, text, name, letters, watermark, (Hat:1.5)"
			parameters["mask"] = "{==={MaskImg}===}"

			imagesPath["InitImg"] = shopOrigin.CropImg
			imagesPath["MaskImg"] = shopOrigin.MaskImg
			imagesPath["BodyBgImg"] = shopOrigin.BodyBgImg

			controlNet = make(map[string]interface{})
			controlNet["input_image"] = "{==={BodyBgImg}===}" //"上传的controllNet的图片,转成base64编码",
			//controlNet["mask"] = "{==={MaskImage}===}"
			controlNet["module"] = "openpose"
			controlNet["model"] = "control_v11p_sd15_openpose [cab727d4]"
			controlNet["weight"] = 1
			controlNet["resize_mode"] = 1
			controlNet["lowvram"] = false
			controlNet["processor_res"] = 512
			controlNet["guidance_start"] = 0
			controlNet["guidance_end"] = 1
			controlNet["pixel_perfect"] = "False"
			controlNet["control_mode"] = "Balanced"
			controlnetUnits = append(controlnetUnits, controlNet)

			controlNet = make(map[string]interface{})
			controlNet["input_image"] = "{==={BodyBgImg}===}" //"上传的controllNet的图片,转成base64编码",
			//controlNet["mask"] = "{==={MaskImage}===}"
			controlNet["module"] = "depth_midas" //只换头Control
			controlNet["model"] = "control_v11f1p_sd15_depth [cfd03158]"
			controlNet["weight"] = 1
			controlNet["resize_mode"] = 1
			controlNet["lowvram"] = false
			controlNet["processor_res"] = 512
			controlNet["guidance_start"] = 0
			controlNet["guidance_end"] = 1
			controlNet["pixel_perfect"] = "False"
			controlNet["control_mode"] = "Balanced"
			controlnetUnits = append(controlnetUnits, controlNet)*/

	} else if shopOrigin.MadeType == enums.MadeTypeEnum.ChangeBackground {

		if shopOrigin.ShopSceneId == 0 { //图生图接口 换纯色背景图 需要带颜色的主题图 主体黑白蒙版图
			var sdParam model.SdParm
			if err := sdParam.GetById(shopOrigin.ShopFaceId); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "ShopFaceId 获取信息失败")
				return
			}

			if tmpParameters, err := service.SdClient.GenParameters(sdParam.Parameters, sdParam.ControlnetUnits, "openpose,lineart_realistic"); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "生成Parameters参数失败")
				return
			} else {
				parameters = tmpParameters
			}
			parameters["init_images"] = initImages

			parametersStr := tools.GetJsonFromMap(parameters)

			if strings.Contains(parametersStr, "{==={InitImg}===}") {
				imagesPath["InitImg"] = shopOrigin.CropImg
			}
			if strings.Contains(parametersStr, "{==={BodyBgImg}===}") {
				imagesPath["BodyBgImg"] = shopOrigin.BodyBgImg
			}
			if strings.Contains(parametersStr, "{==={CropImg}===}") {
				imagesPath["CropImg"] = shopOrigin.CropImg
			}
			if strings.Contains(parametersStr, "{==={MaskImg}===}") {
				imagesPath["MaskImg"] = shopOrigin.MaskImg
			}
			if strings.Contains(parametersStr, "{==={MaskUpImg}===}") {
				imagesPath["MaskUpImg"] = shopOrigin.MaskImg
			}
			if strings.Contains(parametersStr, "{==={MapImg}===}") {
				imagesPath["MapImg"] = shopOrigin.MapImg
			}

			//imagesPath["InitImg"] = shopOrigin.BodyBgImg
			//imagesPath["MaskImg"] = shopOrigin.MaskImg
			//
			//imagesPath["BodyBgImg"] = shopOrigin.BodyBgImg
			////imagesPath["MapImg"] = shopOrigin.MapImg
			//
			//controlNet = make(map[string]interface{})
			//controlNet["input_image"] = "{==={BodyBgImg}===}" //"上传的controllNet的图片,转成base64编码",
			//controlNet["module"] = "openpose"
			//controlNet["model"] = "control_v11p_sd15_openpose [cab727d4]"
			//controlNet["weight"] = 1
			//controlNet["resize_mode"] = 1
			//controlNet["lowvram"] = false
			//controlNet["processor_res"] = 512
			//controlNet["guidance_start"] = 0
			//controlNet["guidance_end"] = 1
			//controlNet["pixel_perfect"] = "False"
			//controlNet["control_mode"] = "Balanced"
			//controlnetUnits = append(controlnetUnits, controlNet)
			//
			//controlNet = make(map[string]interface{})
			//controlNet["input_image"] = "{==={BodyBgImg}===}" //"上传的controllNet的图片,转成base64编码",
			//controlNet["module"] = "lineart_realistic"
			//controlNet["model"] = "control_v11p_sd15_lineart [43d4be0d]"
			//controlNet["weight"] = 1
			//controlNet["resize_mode"] = 1
			//controlNet["lowvram"] = false
			//controlNet["processor_res"] = 512
			//controlNet["guidance_start"] = 0
			//controlNet["guidance_end"] = 1
			//controlNet["pixel_perfect"] = "False"
			//controlNet["control_mode"] = "Balanced"
			//controlnetUnits = append(controlnetUnits, controlNet)

		} else { //这里是换非纯色背景 就是场景  文生图接口，只需要白底主体图
			delete(parameters, "mask")
			imagesPath["InitImg"] = shopOrigin.BodyBgImg
			imagesPath["BodyBgImg"] = shopOrigin.BodyBgImg

			controlNet = make(map[string]interface{})
			controlNet["input_image"] = "{==={BodyBgImg}===}" //"上传的controllNet的图片,转成base64编码",
			controlNet["module"] = "lineart_realistic"
			controlNet["model"] = "control_v11p_sd15_lineart [43d4be0d]"
			controlNet["weight"] = 1
			controlNet["resize_mode"] = 1
			controlNet["lowvram"] = false
			controlNet["processor_res"] = 512
			controlNet["guidance_start"] = 0
			controlNet["guidance_end"] = 1
			controlNet["pixel_perfect"] = "False"
			controlNet["control_mode"] = "Balanced"
			controlnetUnits = append(controlnetUnits, controlNet)
		}
	} else {
		var sdParam model.SdParm
		if err := sdParam.GetById(shopOrigin.ShopFaceId); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "ShopFaceId 获取信息失败")
			return
		}

		if tmpParameters, err := service.SdClient.GenParameters(sdParam.Parameters, sdParam.ControlnetUnits, "openpose,lineart_realistic"); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "生成Parameters参数失败")
			return
		} else {
			parameters = tmpParameters
		}
		parameters["init_images"] = initImages

		parametersStr := tools.GetJsonFromMap(parameters)

		if strings.Contains(parametersStr, "{==={InitImg}===}") {
			imagesPath["InitImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={BodyBgImg}===}") {
			imagesPath["BodyBgImg"] = shopOrigin.BodyBgImg
		}
		if strings.Contains(parametersStr, "{==={CropImg}===}") {
			imagesPath["CropImg"] = shopOrigin.CropImg
		}
		if strings.Contains(parametersStr, "{==={MaskImg}===}") {
			imagesPath["MaskImg"] = shopOrigin.MaskImg
		}
		if strings.Contains(parametersStr, "{==={MaskUpImg}===}") {
			imagesPath["MaskUpImg"] = shopOrigin.MaskUpImg
		}
		if strings.Contains(parametersStr, "{==={MapImg}===}") {
			imagesPath["MapImg"] = shopOrigin.MapImg
		}
		//
		//controlNet = make(map[string]interface{})
		//controlNet["input_image"] = "{==={BodyBgImg}===}" //"上传的controllNet的图片,转成base64编码",
		////controlNet["mask"] = "{==={MaskImage}===}"
		//controlNet["module"] = "openpose"
		//controlNet["model"] = "control_v11p_sd15_openpose [cab727d4]"
		//controlNet["weight"] = 1
		//controlNet["resize_mode"] = 1
		//controlNet["lowvram"] = false
		//controlNet["processor_res"] = 512
		//controlNet["guidance_start"] = 0
		//controlNet["guidance_end"] = 1
		//controlNet["pixel_perfect"] = "False"
		//controlNet["control_mode"] = "Balanced"
		//controlnetUnits = append(controlnetUnits, controlNet)
		//
		//controlNet = make(map[string]interface{})
		//controlNet["input_image"] = "{==={MapImg}===}" //"上传的controllNet的图片,转成base64编码",
		////controlNet["mask"] = "{==={MaskImage}===}"
		//controlNet["module"] = "lineart_realistic"
		//controlNet["model"] = "control_v11p_sd15_lineart [43d4be0d]"
		//controlNet["weight"] = 1
		//controlNet["resize_mode"] = 1
		//controlNet["lowvram"] = false
		//controlNet["processor_res"] = 512
		//controlNet["guidance_start"] = 0
		//controlNet["guidance_end"] = 1
		//controlNet["pixel_perfect"] = "False"
		//controlNet["control_mode"] = "Balanced"
		//controlnetUnits = append(controlnetUnits, controlNet)

	}

	//controlnet := make(map[string]interface{})
	//controlnet["args"] = controlnetUnits
	//alwayson_scripts := make(map[string]interface{})
	//alwayson_scripts["controlnet"] = controlnet
	//parameters["alwayson_scripts"] = alwayson_scripts
	//parameters["controlnet_units"] = controlnetUnits

	fields["custom_app"] = "shopshow"
	fields["custom_data"] = ""
	fields["custom_md5"] = "{==={CustomMd5}===}"
	fields["custom_path"] = service.ImgPath.GetOutputDirectory(shopOrigin.OriginImg) + "{==={CustomMd5}===}.png"
	//fields["custom_path"] = service.ImgPath.GetOutputDirectory(shopOrigin.OriginImg) + tools.GetUuId() + ".png"
	fields["parameters"] = tools.GetJsonFromMap(parameters)
	fields["images_path"] = imagesPath

	pushJson := tools.GetJsonFromMap(fields)
	if err := shopOrigin.SetPushJson(pushJson); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "设置推送模板出错")
		return
	}
	result := make(map[string]interface{})
	if outAry, err := obj.batchPush(claims.UserId, shopOrigin.ID); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "推送失败")
		return
	} else {
		result["outputs_md5"] = outAry
	}

	msg = "图片生成中..."

	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}
func (obj _shopOriginApi) SdProgress(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginSdProgressReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if oReq.IdTask == "" {
		logger.Error("oReq.IdTask 为空")
		errmsg.Abort(c, errmsg.FAIL, "参数错误 oReq.IdTask 为空")
		return
	}
	str, err := service.SdClient.Progress(oReq.IdTask)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "查询进度失败")
		return
	}
	result := make(map[string]interface{})
	result["progress"] = str
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
	logger.Info("Post Res:", str)
}
func (obj _shopOriginApi) batchPush(userId uint, id uint) ([]string, error) {

	tmpAry := make([]string, 0)
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(id); err != nil {
		logger.Error(err)
		return tmpAry, err
	}
	batchSize := 1
	batchIndex := shopOrigin.BatchCount + 1
	totalIndex := shopOrigin.TotalCount
	if err := shopOrigin.SetBatchCount(batchIndex, shopOrigin.TotalCount+batchSize); err != nil {
		logger.Error(err)
		return tmpAry, err
	}

	var outImages []model.ShopOut
	for i := 0; i < batchSize; i++ {
		oMd5Str := fmt.Sprintf("shopshow,%s,%d,%d", time.Now().Format("2006-01-02 15:04:05.000"), tools.GetRandom(1000, 9999), i)
		has := md5.Sum([]byte(oMd5Str))
		md5Str := hex.EncodeToString(has[:])
		if len(md5Str) != 32 {
			err := errors.New("md5位数不正确")
			logger.Error(err)
			return tmpAry, err
		}

		path := service.ImgPath.GetOutputDirectory(shopOrigin.OriginImg) + md5Str + ".png"
		logger.Info(path)
		totalIndex += 1

		endJson := strings.Replace(shopOrigin.PushJson, "{==={CustomMd5}===}", md5Str, -1)
		outImg := model.ShopOut{
			UserId:     userId,
			OrigWhere:  enums.OrigWhereEnum.Shopshow,
			OrigId:     shopOrigin.ID,
			BatchIndex: batchIndex,
			TotalIndex: totalIndex,
			Md5:        md5Str,
			PushJson:   endJson,
			Parameters: "{}",
			Info:       "{}",
			PushAt:     time.Now(),
			//Path:       path,
		}
		outImages = append(outImages, outImg)
	}

	if err := outImages[0].BatchCreate(outImages); err != nil {
		logger.Error(err)
		return tmpAry, err
	}

	//failCount := 0
	successCount := 0

	for _, item := range outImages {
		tmpAry = append(tmpAry, item.Md5)
		str, err := service.SdClient.Sd2img(item.PushJson)
		if err != nil {
			logger.Error(err)
			return tmpAry, err
		}
		logger.Info("Post Res:", str)
		successCount++
	}
	return tmpAry, nil
}

func (obj _shopOriginApi) UploadFinalImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	id := uint(0)
	idStr, _ := c.GetPostForm("id")
	if idStr != "" {
		if num, err := strconv.Atoi(idStr); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "参数错误")
			return
		} else {
			id = uint(num)
		}
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(id); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}

	f, err := c.FormFile("final_img")
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}

	md5Str := shopOrigin.OriginMd5
	if len(md5Str) != 32 {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图名称参数")
		return
	}

	path := service.ImgPath.GetFinalPath(shopOrigin.OriginImg, tools.GetFileExt(f.Filename))
	absolutePath := config.DiffusionFilePath + path

	if err := c.SaveUploadedFile(f, absolutePath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}
	if err := shopOrigin.SetFinalImg(path); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片路径设置失败")
		return
	}

	msg = "上传成功"
	result := make(map[string]interface{})
	result["id"] = shopOrigin.ID
	result["origin_md5"] = shopOrigin.OriginMd5
	result["final_img_url"] = config.DiffusionDomain + shopOrigin.FinalImg
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) SetFinalImg(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)

	if claims.UserId <= 0 {

	}

	var oReq shopOriginSetFinalImgReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(oReq.ShopOriginId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "参数错误")
		return
	}
	var shopOut model.ShopOut
	if err := shopOut.GetByMd5(oReq.OutImgMd5); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片数据失败")
		return
	}
	if shopOut.Path == "" {
		errmsg.Abort(c, errmsg.FAIL, "获取图片数据失败1")
		return
	}
	outAbsolutePath := config.DiffusionFilePath + shopOut.Path

	if shopOrigin.FinalImg != "" {
		finalPath := shopOrigin.FinalImg
		finalAbsolutePath := config.DiffusionFilePath + finalPath
		if b, _ := tools.PathFileExists(finalAbsolutePath); b {
			baseName := path.Base(finalAbsolutePath) // 输出 name.html
			ext := path.Ext(baseName)                // 输出 .html
			tmp := "_" + time.Now().Format("20060102150405") + ext
			newAbsolutePath := strings.Replace(finalAbsolutePath, ext, tmp, -1)
			if err := os.Rename(finalAbsolutePath, newAbsolutePath); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "备份图片数据失败")
				return
			}
		}
	}

	{
		finalPath := service.ImgPath.GetFinalPath(shopOrigin.OriginImg, tools.GetFileExt(shopOut.Path))
		finalAbsolutePath := config.DiffusionFilePath + finalPath
		if err := tools.CopyFile(outAbsolutePath, finalAbsolutePath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "拷贝图片失败")
			return
		}
		if err := shopOrigin.SetFinalImg(finalPath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "图片路径设置失败")
			return
		}
	}

	result := make(map[string]interface{})
	result["shop_origin_id"] = oReq.ShopOriginId
	result["out_img_md5"] = oReq.OutImgMd5
	result["final_img_url"] = config.DiffusionDomain + shopOrigin.FinalImg
	msg = "设置成功"
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _shopOriginApi) List(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	var oReq shopOriginListReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	shopOrigin := model.ShopOrigin{}
	ary := make([]shopOriginItem, 0)
	result := make(map[string]interface{})
	if total, err := shopOrigin.GetList(&ary, oReq.Id, oReq.UserId, oReq.Title, oReq.Page, oReq.PageSize); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询失败")
		return
	} else {
		result["total"] = total
	}

	for i := 0; i < len(ary); i++ {
		//ary[i].PushJson = ""
		if ary[i].OriginImg != "" {
			ary[i].OriginImgUrl = config.DiffusionDomain + ary[i].OriginImg
		}
		if ary[i].CropImg != "" {
			ary[i].CropImgUrl = config.DiffusionDomain + ary[i].CropImg
		}
		if ary[i].BodyImg != "" {
			ary[i].BodyImgUrl = config.DiffusionDomain + ary[i].BodyImg
		}
		if ary[i].BodyBgImg != "" {
			ary[i].BodyBgImgUrl = config.DiffusionDomain + ary[i].BodyBgImg
		}
		if ary[i].MaskImg != "" {
			ary[i].MaskImgUrl = config.DiffusionDomain + ary[i].MaskImg
		}
		if ary[i].MaskUpImg != "" {
			ary[i].MaskUpImgUrl = config.DiffusionDomain + ary[i].MaskUpImg
		}
		if ary[i].MapImg != "" {
			ary[i].MapImgUrl = config.DiffusionDomain + ary[i].MapImg
		}
		if ary[i].FinalImg != "" {
			ary[i].FinalImgUrl = config.DiffusionDomain + ary[i].FinalImg
		}
	}
	result["items"] = ary

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

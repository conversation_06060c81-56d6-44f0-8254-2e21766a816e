package shopshow

import (
	"center-ai/enums"
	"center-ai/middleware"
	"center-ai/model"
	"center-ai/service"
	"center-ai/utils/config"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
)

type _shopOutApi struct {
}

var ShopOutApi _shopOutApi

type shopOutItemReq struct {
	Id  uint   `json:"id"`
	Md5 string `json:"md5"`
}

type shopOutListReq struct {
	Id           uint   `json:"id"`
	Md5          string `json:"md5"`
	UserId       uint   `json:"user_id"`
	ShopOriginId uint   `json:"shop_origin_id"`
	ModelName    string `json:"model_name"`
	ShopFaceId   uint   `json:"shop_face_id"`
	Page         int    `json:"page"`
	PageSize     int    `json:"page_size"`
}

type shopOutItem struct {
	ID                uint           `json:"id"`
	UserId            uint           `json:"user_id"`
	OrigWhere         int            `json:"orig_where"`
	OrigId            uint           `json:"orig_id" `
	MadeType          int            `json:"made_type"`
	ModelName         string         `json:"model_name"`
	ShopFaceId        uint           `json:"shop_face_id"`
	TotalIndex        int            `json:"total_index"`
	BatchIndex        int            `json:"batch_index" `
	Parameters        string         `json:"parameters"`
	PushJson          string         `json:"push_json"`
	PushAt            model.JsonTime `json:"push_at"`
	Info              string         `json:"info"`
	PngInfo           string         `json:"png_info"`
	PngInfoFormat     string         `json:"png_info_format"`
	Progress          float64        `json:"progress"`
	Position          int64          `json:"position"`
	ProgressTxt       string         `json:"progress_txt"`
	Width             int            `json:"width"`
	Height            int            `json:"height"`
	Md5               string         `json:"md5"`
	Path              string         `json:"path"`
	MergePath         string         `json:"merge_path"`
	PathUrl           string         `json:"path_url"`
	CreatedAt         model.JsonTime `json:"created_at"`
	CropImgUrl        string         `json:"crop_img_url"`
	Warning           string         `json:"warning"`
	OriginImgUrl      string         `json:"origin_img_url"`
	OriginWhiteImgUrl string         `json:"origin_white_img_url"`
	OriginMaskImgUrl  string         `json:"origin_mask_img_url"`
}

func (obj _shopOutApi) GetItem(c *gin.Context) {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("GetItem奔溃:", e)
		}
	}()
	var code int
	var msg string
	var oReq shopOutItemReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var item model.ShopOut
	if oReq.Id > 0 {
		if err := item.GetByID(oReq.Id); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "获取图片信息失败")
			return
		}
	} else {
		if err := item.GetByMd5(oReq.Md5); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "获取图片信息失败")
			return
		}
	}

	imgUrl := ""

	progress := float64(-1)
	position := int64(-1)
	//sdServer := ""
	progressTxt := ""
	if item.Path == "" {
		if len(item.Info) < 10 {
			progress, position, progressTxt = service.GetProgress(item.Md5, "shopshow")
			//progress, sdServer = service.ShopShowService.GetProgress(item.Md5)
			//if progress == -2 { //在主队列中
			//	position = service.ShopShowService.GetProgressRank(item.Md5, item.PushJson)
			//}
			//if progress == -1 { //可能在目标队列中
			//	if sdServer != "" {
			//		position, _ = service.ShopShowService.GetProgressInTarget(sdServer, item.Md5)
			//	}
			//}
		} else {
			progress = -4
		}
	}

	if item.MadeType == enums.MadeTypeEnum.ChangeSence {
		if item.Path != "" {
			progress = float64(0.99)
		}
		if item.MergePath != "" {
			imgUrl = config.DiffusionDomain + item.MergePath
			progress = 1
		}
	} else if item.MadeType == enums.MadeTypeEnum.ChangeBigHead {
		if item.Path != "" {
			progress = float64(0.99)
		}
		if item.MergePath != "" {
			imgUrl = config.DiffusionDomain + item.MergePath
			progress = 1
		}
	} else if item.MadeType == enums.MadeTypeEnum.ChangeBigHeadAndBackColor {
		if item.Path != "" {
			progress = float64(0.49)
		}
		if item.MergePath != "" {
			if strings.Contains(item.MergePath, "_merge_sdbg.png") {
				imgUrl = config.DiffusionDomain + item.MergePath
				progress = 1
			} else {
				//imgUrl = config.DiffusionDomain + item.MergePath
				progress, position, progressTxt = service.GetProgress(item.Md5, "shopshow")
				if progress < 0 {
					progress = 0.5
				} else {
					progress = 0.5 + progress/float64(2)
				}
			}
		}
		if item.Path == "" && progress > 0 {
			progress = progress / float64(2)
		}
	} else {
		if item.Path != "" {
			imgUrl = config.DiffusionDomain + item.Path
			progress = 1
		}
	}

	resp := shopOutItem{
		ID:          item.ID,
		Md5:         item.Md5,
		UserId:      item.UserId,
		MadeType:    item.MadeType,
		ModelName:   item.ModelName,
		BatchIndex:  item.BatchIndex,
		ShopFaceId:  item.ShopFaceId,
		PathUrl:     imgUrl,
		OrigWhere:   item.OrigWhere,
		OrigId:      item.OrigId,
		Progress:    progress,
		Position:    position,
		ProgressTxt: progressTxt,
		CreatedAt:   model.JsonTime(item.CreatedAt),
		PushAt:      model.JsonTime(item.PushAt),
	}
	var shopOrigin model.ShopOrigin
	if err := shopOrigin.GetById(item.OrigId); err == nil {
		resp.OriginImgUrl = config.DiffusionDomain + service.ImgPath.GetOriginJpgPath(shopOrigin.OriginImg)
		if b, _ := tools.PathFileExists(config.DiffusionFilePath + service.ImgPath.GetOriginWhitePath(shopOrigin.OriginImg)); b {
			resp.OriginWhiteImgUrl = config.DiffusionDomain + service.ImgPath.GetOriginWhitePath(shopOrigin.OriginImg)
		}
		if b, _ := tools.PathFileExists(config.DiffusionFilePath + service.ImgPath.GetOriginMaskPath(shopOrigin.OriginImg)); b {
			resp.OriginMaskImgUrl = config.DiffusionDomain + service.ImgPath.GetOriginMaskPath(shopOrigin.OriginImg)
		}
	} else {
		logger.Error(err)
	}

	result := make(map[string]interface{})
	if strings.HasPrefix(item.Info, "{") {
		m := tools.GetMapFromJson(item.Info)
		if _, ok := m["warning"]; ok {
			resp.Warning = tools.GetJsonFromStruct(m["warning"].(map[string]interface{}))
		}
	}
	result["item"] = resp

	if item.Path != "" {
		parametersStr, err := service.SdClient.GetPngInfo(config.DiffusionFilePath + item.Path)
		if err != nil {
			logger.Error(err, "获取图片信息失败 ", item.Path)
		} else {
			resp.PngInfo = parametersStr
			mParm, err := service.SdClient.AnalyzePngInfo(parametersStr)
			if err != nil {
				logger.Error(err, "解析图片信息失败 ", item.Path)
			} else {
				resp.PngInfoFormat = tools.GetJsonFromMap(mParm)
			}
		}
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "完成",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}
func (obj _shopOutApi) List(c *gin.Context) {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("List奔溃:", e)
		}
	}()
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	var oReq shopOutListReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	shopOut := model.ShopOut{}
	ary := make([]shopOutItem, 0)
	result := make(map[string]interface{})

	if total, err := shopOut.GetList(&ary, oReq.Id, oReq.Md5, oReq.UserId, enums.OrigWhereEnum.Shopshow, oReq.ShopOriginId, oReq.ShopFaceId, oReq.ModelName, oReq.Page, oReq.PageSize); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询失败")
		return
	} else {
		result["total"] = total
		for i := 0; i < len(ary); i++ {
			progress := float64(-1)
			position := int64(-1)
			//sdServer := ""
			progressTxt := ""
			imgUrl := ""
			{
				item := ary[i]
				if item.Path == "" {
					if len(item.Info) < 10 {
						progress, position, progressTxt = service.GetProgress(item.Md5, "shopshow")
					} else {
						progress = -4
					}
				}

				if item.MadeType == enums.MadeTypeEnum.ChangeSence {
					if item.Path != "" {
						progress = float64(0.99)
					}
					if item.MergePath != "" {
						imgUrl = config.DiffusionDomain + item.MergePath
						progress = 1
					}
				} else if item.MadeType == enums.MadeTypeEnum.ChangeBigHead {
					if item.Path != "" {
						progress = float64(0.99)
					}
					if item.MergePath != "" {
						imgUrl = config.DiffusionDomain + item.MergePath
						progress = 1
					}
				} else if item.MadeType == enums.MadeTypeEnum.ChangeBigHeadAndBackColor {
					if item.Path != "" {
						progress = float64(0.49)
					}
					if item.MergePath != "" {
						if strings.Contains(item.MergePath, "_merge_sdbg.png") {
							imgUrl = config.DiffusionDomain + item.MergePath
							progress = 1
						} else {
							//imgUrl = config.DiffusionDomain + item.MergePath
							progress, position, progressTxt = service.GetProgress(item.Md5, "shopshow")
							if progress < 0 {
								progress = 0.5
							} else {
								progress = 0.5 + progress/float64(2)
							}
						}
					}
					if item.Path == "" && progress > 0 {
						progress = progress / float64(2)
					}
				} else {
					if item.Path != "" {
						imgUrl = config.DiffusionDomain + item.Path
						progress = 1
					}
				}
				ary[i].Progress = progress
				ary[i].Position = position
				ary[i].ProgressTxt = progressTxt
				ary[i].PathUrl = imgUrl
			}

			if len(ary[i].Info) > 10 {
				m := tools.GetMapFromJson(ary[i].Info)
				if _, ok := m["warning"]; ok {
					ary[i].Warning = tools.GetJsonFromStruct(m["warning"].(map[string]interface{}))
				}
			}

			if total > 1 {
				ary[i].Parameters = ""
				ary[i].Info = ""
				ary[i].PushJson = ""
			} else {
				var shopOrigin model.ShopOrigin
				if err := shopOrigin.GetById(ary[i].OrigId); err != nil {
					logger.Error(err)
				} else {
					if shopOrigin.CropImg != "" {
						ary[i].CropImgUrl = config.DiffusionDomain + shopOrigin.CropImg
					}
				}
				item := ary[i]
				if item.Path != "" {
					parametersStr, err := service.SdClient.GetPngInfo(config.DiffusionFilePath + item.Path)
					if err != nil {
						logger.Error(err, "获取图片信息失败 ", item.Path)
					} else {
						ary[i].PngInfo = parametersStr
						mParm, err := service.SdClient.AnalyzePngInfo(parametersStr)
						if err != nil {
							logger.Error(err, "解析图片信息失败 ", item.Path)
						} else {
							ary[i].PngInfoFormat = tools.GetJsonFromMap(mParm)
						}
					}
				}
			}
		}
	}

	result["items"] = ary

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

package sd

import (
	"center-ai/enums"
	"center-ai/middleware"
	"center-ai/model"
	"center-ai/service"
	"center-ai/utils/config"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/myimg"
	"center-ai/utils/myredis"
	"center-ai/utils/tools"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

type _sdParmApi struct {
}

var SdParmApi _sdParmApi

type sdParmAddReq struct {
	Id              uint   `json:"id"`
	OuterNo         string `json:"outer_no"`
	BusinessType    int    `json:"business_type"`
	Sex             int    `json:"sex"`
	Nationality     string `json:"nationality"`
	AgeSect         string `json:"age_sect"`
	SdModelId       uint   `json:"sd_model_id"`
	Prompt          string `json:"prompt"`
	Parameters      string `json:"parameters"`
	NegativePrompt  string `json:"negative_prompt"`
	ControlnetUnits string `json:"controlnet_units"`
	Title           string `json:"title"`
	OrderIndex      int    `json:"order_index"`
	Remark          string `json:"remark"`
	CoverBase64     string `json:"cover_base64"`
	State           int    `json:"state"`
}

type sdParmListReq struct {
	Id           uint   `json:"id"`
	OuterNo      string `json:"outer_no"`
	BusinessType int    `json:"business_type"`
	Sex          int    `json:"sex"`
	Nationality  string `json:"nationality"`
	AgeSect      string `json:"age_sect"`
	Title        string `json:"title"`
	Kw           string `json:"kw"`
	State        int    `json:"state"`
	Page         int    `json:"page"`
	PageSize     int    `json:"page_size"`
}

type sdParmItem struct {
	Id              uint           `json:"id"`
	UserId          uint           `json:"user_id"`
	OuterNo         string         `json:"outer_no"`
	BusinessType    uint           `json:"business_type"`
	Sex             int            `json:"sex"`
	Nationality     string         `json:"nationality"`
	AgeSect         string         `json:"age_sect"`
	SdModelId       uint           `json:"sd_model_id"`
	Prompt          string         `json:"prompt"`
	NegativePrompt  string         `json:"negative_prompt"`
	Parameters      string         `json:"parameters"`
	ControlnetUnits string         `json:"controlnet_units"`
	Title           string         `json:"title"`
	OrderIndex      int            `json:"order_index"`
	Cover           string         `json:"-"`
	CoverUrl        string         `json:"cover_url"`
	Remark          string         `json:"remark"`
	CreatedAt       model.JsonTime `json:"created_at"`
	UpdatedAt       model.JsonTime `json:"updated_at"`
	State           int            `json:"state"`
}

func (obj _sdParmApi) Add(c *gin.Context) {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("Add奔溃:", e)
		}
	}()
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	var oReq sdParmAddReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var sdParm model.SdParm
	if oReq.Id > 0 {
		if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
			errmsg.Abort(c, errmsg.FAIL, "权限不足")
			return
		}
		if err := sdParm.GetById(oReq.Id); err != nil {
			logger.Error(err, oReq.Id)
			errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
			return
		}

		logJson := tools.GetJsonFromStruct(sdParm)
		operationLog := model.OperationLog{
			OperatorUserId: claims.UserId,
			LogType:        enums.OperationLogTypeEnum.ModifySdParm,
			OrigWhere:      enums.OperationOrigWhereEnum.SdParm,
			OrigId:         sdParm.ID,
			Ip:             tools.GetClientIp(c.Request.Header),
			LogJson:        logJson,
			UserEnv:        "{}",
		}
		if err := operationLog.Save(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "备份信息失败")
			return
		}

		if sdParm.UserId == 0 {
			sdParm.UserId = claims.UserId
		}
		sdParm.Title = oReq.Title
		sdParm.OuterNo = oReq.OuterNo
		sdParm.Sex = oReq.Sex
		sdParm.Nationality = oReq.Nationality
		sdParm.AgeSect = oReq.AgeSect
		sdParm.SdModelId = oReq.SdModelId
		sdParm.Prompt = oReq.Prompt
		sdParm.NegativePrompt = oReq.NegativePrompt
		sdParm.Parameters = oReq.Parameters
		sdParm.ControlnetUnits = oReq.ControlnetUnits
		sdParm.Title = oReq.Title
		sdParm.Remark = oReq.Remark
		sdParm.State = oReq.State

		if err := sdParm.Save(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存信息失败")
			return
		}
	} else {
		if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
			errmsg.Abort(c, errmsg.FAIL, "权限不足")
			return
		}
		if oReq.ControlnetUnits == "" {
			oReq.ControlnetUnits = "{}"
		}
		if oReq.Parameters == "" {
			oReq.Parameters = "{}"
		}
		sdParm = model.SdParm{
			UserId:          claims.UserId,
			BusinessType:    oReq.BusinessType,
			OuterNo:         oReq.OuterNo,
			Title:           oReq.Title,
			Sex:             oReq.Sex,
			Nationality:     oReq.Nationality,
			AgeSect:         oReq.AgeSect,
			SdModelId:       oReq.SdModelId,
			Prompt:          oReq.Prompt,
			NegativePrompt:  oReq.NegativePrompt,
			Parameters:      oReq.Parameters,
			ControlnetUnits: oReq.ControlnetUnits,
			Remark:          oReq.Remark,
			State:           oReq.State,
		}
		if err := sdParm.Save(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存信息失败")
			return
		}
	}
	if oReq.CoverBase64 == "" {
		logger.Info(sdParm.ID, "oReq.CoverBase64为空，不更新")
	}

	if oReq.CoverBase64 != "" {
		logger.Info(sdParm.ID, "开始更新封面图")
		oMd5Str := fmt.Sprintf("%d,%s", sdParm.ID, sdParm.CreatedAt.Format("2006-01-02 15:04:05.000"))

		path := "center/res/sd/cover/" + tools.GetMd5(oMd5Str) + ".jpg"
		oPath := "center/res/sd/cover/" + tools.GetMd5(oMd5Str) + "_o.png"

		absolutePath := config.DiffusionFilePath + path
		directory := filepath.Dir(absolutePath) // 获取目录路径
		// 创建目录，存在则不创建，不存在则创建
		if err := os.MkdirAll(directory, 0755); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "目录创建失败")
			return
		}

		if img, err := myimg.Base64ToImg(oReq.CoverBase64); err != nil {
			//logger.Error(err, oReq.CoverBase64)
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "base64转图片失败")
			return
		} else {
			if err = myimg.ImgToPngFile(img, config.DiffusionFilePath+oPath); err != nil {
				logger.Error("保存原始图片出错")
				errmsg.Abort(c, errmsg.FAIL, "保存原始图片出错")
				return
			}
			logger.Info(sdParm.ID, "   x:", img.Bounds().Size().X, "   y:", img.Bounds().Size().Y)
			small := myimg.ResizeImg(768, 1024, img, true)
			if err := myimg.ImgToFile(small, absolutePath); err != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "缩略图保存失败")
			} else {
				logger.Info(sdParm.ID, "  缩略图生成成功")
			}
		}

		//if err := myimg.Base64ToFile(oReq.CoverBase64, absolutePath); err != nil {
		//	logger.Error(err)
		//	errmsg.Abort(c, errmsg.FAIL, "保存封面图片失败")
		//	return
		//}
		logger.Info(sdParm.ID, "   开始设置封面path:", path)
		if sdParm.Cover == path {
			logger.Info(sdParm.ID, "  封面路径一样，不更新")
		} else {
			if err := sdParm.SetCover(path); err != nil {
				logger.Error(sdParm.ID, err)
				errmsg.Abort(c, errmsg.FAIL, "设置封面信息失败")
				return
			} else {
				logger.Info(sdParm.ID, "  封面路径设置成功")
			}
		}
	}
	result := make(map[string]interface{})
	result["id"] = sdParm.ID
	msg = "添加成功"
	if oReq.Id > 0 {
		msg = "修改成功"
	}
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _sdParmApi) List(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	var oReq sdParmListReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	sdParm := model.SdParm{}
	ary := make([]sdParmItem, 0)
	result := make(map[string]interface{})
	if total, err := sdParm.GetList(&ary, oReq.Id, oReq.OuterNo, oReq.BusinessType, oReq.Sex, oReq.Nationality, oReq.AgeSect, oReq.Kw, oReq.State, oReq.Page, oReq.PageSize); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询失败")
		return
	} else {
		result["total"] = total
	}

	for i := 0; i < len(ary); i++ {
		if ary[i].Cover != "" {
			ary[i].CoverUrl = config.DiffusionDomain + ary[i].Cover
		}
	}

	result["items"] = ary

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj _sdParmApi) ReloadModel(c *gin.Context) {

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	curStr := ""
	if str, err := service.SdClient.SdModels(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "重载失败")
		return
	} else {
		if strings.HasPrefix(str, "[") && strings.Contains(str, "model_name") {
			er := myredis.Set(enums.AigcRedisKeyEnum.SdApiModels, str, -1)
			if er != nil {
				logger.Error(er)
				errmsg.Abort(c, errmsg.FAIL, "设置失败")
				return
			} else {
				curStr = str
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code":   0,
		"msg":    "重载完成",
		"result": curStr,
	})

}

func (obj _sdParmApi) ListModel(c *gin.Context) {

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	ss := myredis.Get(enums.AigcRedisKeyEnum.SdApiModels)

	if ss == "" {
		logger.Error("err")
		errmsg.Abort(c, errmsg.FAIL, "获取模型列表为空")
		return
	} else {
		c.Data(200, "application/json", []byte(ss))
		return
	}
}

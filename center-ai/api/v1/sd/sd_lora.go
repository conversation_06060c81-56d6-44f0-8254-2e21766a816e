package sd

import (
	"center-ai/enums"
	"center-ai/middleware"
	"center-ai/model"
	"center-ai/utils/config"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/myimg"
	"center-ai/utils/tools"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path/filepath"
)

type _sdLoraApi struct {
}

var SdLoraApi _sdLoraApi

type sdLoraAddReq struct {
	Id          uint   `json:"id"`
	Title       string `json:"title"`
	Hash        string `json:"hash"`
	Path        string `json:"path"`
	Remark      string `json:"remark"`
	CoverBase64 string `json:"cover_base64"`
}

type sdLoraListReq struct {
	Id       uint   `json:"id"`
	Title    string `json:"title"`
	Hash     string `json:"hash"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type sdLoraItem struct {
	ID        uint           `json:"id"`
	Title     string         `json:"title"`
	Hash      string         `json:"hash"`
	Path      string         `json:"path"`
	Cover     string         `json:"-"`
	CoverUrl  string         `json:"cover_url"`
	Remark    string         `json:"remark"`
	CreatedAt model.JsonTime `json:"created_at"`
}

func (obj _sdLoraApi) Add(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	var oReq sdLoraAddReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var sdLora model.SdLora
	if oReq.Id > 0 {
		if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
			errmsg.Abort(c, errmsg.FAIL, "权限不足")
			return
		}
		if err := sdLora.GetById(oReq.Id); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
			return
		}
		sdLora.Title = oReq.Title
		sdLora.Remark = oReq.Remark
		sdLora.Hash = oReq.Hash
		sdLora.Path = oReq.Path
		if err := sdLora.Save(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存信息失败")
			return
		}
	} else {
		if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
			errmsg.Abort(c, errmsg.FAIL, "权限不足")
			return
		}
		sdLora = model.SdLora{
			Title:  oReq.Title,
			Hash:   oReq.Hash,
			Path:   oReq.Path,
			Remark: oReq.Remark,
		}
		if err := sdLora.Save(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存信息失败")
			return
		}
	}

	if oReq.CoverBase64 != "" {
		oMd5Str := fmt.Sprintf("%d,%s", sdLora.ID, sdLora.CreatedAt.Format("2006-01-02 15:04:05.000"))

		path := "center/res/sd_lora_" + tools.GetMd5(oMd5Str) + ".jpg"
		absolutePath := config.DiffusionFilePath + path
		directory := filepath.Dir(absolutePath) // 获取目录路径
		// 创建目录，存在则不创建，不存在则创建
		if err := os.MkdirAll(directory, 0755); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "目录创建失败")
			return
		}
		if err := myimg.Base64ToFile(oReq.CoverBase64, absolutePath); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "保存封面图片失败")
			return
		}
		if err := sdLora.SetCover(path); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "设置封面信息失败")
			return
		}
	}
	result := make(map[string]interface{})
	result["id"] = sdLora.ID
	msg = "添加成功"
	if oReq.Id > 0 {
		msg = "修改成功"
	}
	if code == 0 {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  msg,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj _sdLoraApi) List(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	var oReq sdModelListReq
	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	sdModel := model.SdModel{}
	ary := make([]sdModelItem, 0)
	result := make(map[string]interface{})
	if total, err := sdModel.GetList(&ary, oReq.Id, oReq.Title, oReq.Hash, oReq.Page, oReq.PageSize); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询失败")
		return
	} else {
		result["total"] = total
	}

	for i := 0; i < len(ary); i++ {
		if ary[i].Cover != "" {
			ary[i].CoverUrl = config.DiffusionDomain + ary[i].Cover
		}
	}

	result["items"] = ary

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

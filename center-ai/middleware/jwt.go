package middleware

import (
	"center-ai/utils/config"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"errors"
	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
	"time"
)

type JWT struct {
	JwtKey []byte
}

func NewJWT() *JWT {
	return &JWT{
		[]byte(config.JwtKey),
	}
}

type MyClaims struct {
	Mobile   string `json:"mobile"`
	Username string `json:"username"`
	UserId   uint   `json:"id"`
	//PermOperates []string `json:"perm_operates"`
	jwt.StandardClaims
}

// 定义错误
var (
	TokenExpired     error  = errors.New("Token已过期,请重新登录")
	TokenNotValidYet error  = errors.New("Token无效,请重新登录")
	TokenMalformed   error  = errors.New("Token不正确,请重新登录")
	TokenInvalid     error  = errors.New("这不是一个token,请重新登录")
	TokenIssuer      string = "Center"
)

func GetMyClaims(userId uint, userName string, mobile string) (MyClaims, error) {
	claims := MyClaims{
		UserId:   userId,
		Username: userName,
		Mobile:   tools.FormatMobileStar(mobile),
		StandardClaims: jwt.StandardClaims{
			NotBefore: time.Now().Unix() - 100,
			ExpiresAt: time.Now().Unix() + 31536000000,
			Issuer:    TokenIssuer,
		},
	}
	return claims, nil
}

func (j *JWT) SetToken(userId uint, userName string, mobile string) (string, error) {
	claims := MyClaims{
		UserId:   userId,
		Username: userName,
		Mobile:   tools.FormatMobileStar(mobile),
		StandardClaims: jwt.StandardClaims{
			NotBefore: time.Now().Unix() - 100,
			ExpiresAt: time.Now().Unix() + 31536000000,
			Issuer:    TokenIssuer,
		},
	}
	token, err := j.CreateToken(claims)
	if err != nil {
		return "", err
	}
	return TokenIssuer + " " + token, nil
}

// CreateToken 生成token
func (j *JWT) CreateToken(claims MyClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.JwtKey)
}

// ParserToken 解析token
func (j *JWT) ParserToken(tokenString string) (*MyClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &MyClaims{}, func(token *jwt.Token) (interface{}, error) {
		return j.JwtKey, nil
	})

	if err != nil {
		logger.Error(err, "   tokenString:===", tokenString, "===")
		if ve, ok := err.(*jwt.ValidationError); ok {
			if ve.Errors&jwt.ValidationErrorMalformed != 0 {
				return nil, TokenMalformed
			} else if ve.Errors&jwt.ValidationErrorExpired != 0 {
				// Token is expired
				return nil, TokenExpired
			} else if ve.Errors&jwt.ValidationErrorNotValidYet != 0 {
				return nil, TokenNotValidYet
			} else {
				return nil, TokenInvalid
			}
		}
	}

	if token != nil {
		if claims, ok := token.Claims.(*MyClaims); ok && token.Valid {
			return claims, nil
		}
		logger.Error(TokenInvalid, token)
		return nil, TokenInvalid
	}
	logger.Error(TokenInvalid, token)
	return nil, TokenInvalid
}

func GetClaimsByToken(tokenString string) (*MyClaims, error) {

	if tokenString == "" {
		return nil, errors.New("token 字符串为空")
	}

	checkToken := strings.Split(tokenString, " ")
	if len(checkToken) == 0 {
		return nil, errors.New("token 字符串为空")
	}

	if len(checkToken) == 2 && checkToken[0] != TokenIssuer {
		return nil, errors.New("token 格式不正确")
	}

	token := checkToken[0]
	if len(checkToken) > 1 {
		token = checkToken[1]
	}

	j := NewJWT()
	// 解析token
	return j.ParserToken(token)
}

// JwtToken jwt中间件
func JwtToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		tokenHeader := c.Request.Header.Get("Authorization")
		if tokenHeader == "" {
			c.JSON(http.StatusOK, gin.H{
				"code": errmsg.TokenInvalid,
				"msg":  "Token不存在，请重新登录",
			})
			c.Abort()
			return
		}

		checkToken := strings.Split(tokenHeader, " ")
		if len(checkToken) == 0 {
			c.JSON(http.StatusOK, gin.H{
				"code": errmsg.TokenInvalid,
				"msg":  "TOKEN不正确,请重新登陆",
			})
			c.Abort()
			return
		}

		if len(checkToken) == 2 && checkToken[0] != TokenIssuer {
			c.JSON(http.StatusOK, gin.H{
				"code": errmsg.TokenInvalid,
				"msg":  "TOKEN不正确,请重新登陆",
			})
			c.Abort()
			return
		}

		token := checkToken[0]
		if len(checkToken) > 1 {
			token = checkToken[1]
		}

		j := NewJWT()
		// 解析token
		claims, err := j.ParserToken(token)
		if err != nil {
			logger.Error(err, "token:===", token, "===")
			if err == TokenExpired {
				c.JSON(http.StatusOK, gin.H{
					"code": errmsg.TokenInvalid,
					"msg":  "token授权已过期,请重新登录",
					"data": nil,
				})
				c.Abort()
				return
			}
			// 其他错误
			c.JSON(http.StatusOK, gin.H{
				"code": errmsg.TokenInvalid,
				"msg":  err.Error(),
				"data": nil,
			})
			c.Abort()
			return
		}
		c.Set("claims", claims)
		c.Next()
	}
}

// JwtToken jwt中间件
func JwtTokenAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		tokenHeader := c.Request.Header.Get("Authorization")
		if tokenHeader == "" {
			c.JSON(http.StatusOK, gin.H{
				"code": errmsg.TokenInvalid,
				"msg":  "Token不存在，请重新登录",
			})
			c.Abort()
			return
		}

		checkToken := strings.Split(tokenHeader, " ")
		if len(checkToken) == 0 {
			c.JSON(http.StatusOK, gin.H{
				"code": errmsg.TokenInvalid,
				"msg":  "TOKEN不正确,请重新登陆",
			})
			c.Abort()
			return
		}

		if len(checkToken) == 2 && checkToken[0] != TokenIssuer {
			c.JSON(http.StatusOK, gin.H{
				"code": errmsg.TokenInvalid,
				"msg":  "TOKEN不正确,请重新登陆",
			})
			c.Abort()
			return
		}

		token := checkToken[0]
		if len(checkToken) > 1 {
			token = checkToken[1]
		}

		j := NewJWT()
		// 解析token
		claims, err := j.ParserToken(token)
		if err != nil {
			if err == TokenExpired {
				c.JSON(http.StatusOK, gin.H{
					"code": errmsg.TokenInvalid,
					"msg":  "token授权已过期,请重新登录",
					"data": nil,
				})
				c.Abort()
				return
			}
			// 其他错误
			c.JSON(http.StatusOK, gin.H{
				"code": errmsg.TokenInvalid,
				"msg":  err.Error(),
				"data": nil,
			})
			c.Abort()
			return
		}

		//path := c.Request.URL.Path
		//if !strings.Contains(path, "/permission/verify") {
		//	aryPermission := GetPermissionOperates(path, tokenHeader)
		//	logger.Info(path, "  aryPermission:", aryPermission)
		//	claims.PermOperates = aryPermission
		//}

		SetPermissionOperates(c)

		c.Set("claims", claims)
		c.Next()
	}
}

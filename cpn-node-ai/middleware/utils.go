package middleware

import (
	"cpn-ai/common"
	"cpn-ai/common/utils"
	"github.com/gin-gonic/gin"
	"net/http"
)

func abortWithMessage(c *gin.Context, statusCode int, message string) {
	c.<PERSON>(statusCode, gin.H{
		"error": gin.H{
			"message": utils.MessageWithRequestId(message, c.GetString(common.RequestIdKey)),
			"type":    "one_api_error",
		},
	})
	c.Abort()
	common.LogError(c.Request.Context(), message)
}

func abortWithMsg(c *gin.Context, code int, msg string) {
	c.J<PERSON>(http.StatusOK, gin.H{
		"code": code,
		"msg":  msg,
	})
	c.Abort()
	common.LogError(c.Request.Context(), msg)
}

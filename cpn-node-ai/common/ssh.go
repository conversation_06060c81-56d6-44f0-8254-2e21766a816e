package common

import (
	"cpn-ai/common/logger"
	"errors"
	"fmt"
	"golang.org/x/crypto/ssh"
	"time"
)

type SshServer struct {
	Host     string      `json:"host"`
	Port     int         `json:"port"`
	User     string      `json:"user"`
	Password string      `json:"password"`
	Client   *ssh.Client `json:"client"`
}

func (o *SshServer) SshDial() (*ssh.Client, error) {
	//remoteServer := "***************"
	//sshPort := 22
	//sshUser := "root"
	//sshPassword := "Zeyun1234!@#$" // 或者使用密钥进行认证

	remoteServer := o.Host
	sshPort := o.Port
	sshUser := o.User
	sshPassword := o.Password

	// SSH连接配置
	config := &ssh.ClientConfig{
		Timeout: time.Second * 10,
		User:    sshUser,
		Auth: []ssh.AuthMethod{
			ssh.Password(sshPassword),
			// 如果使用密钥认证，可以添加密钥认证的方式
			// ssh.PublicKeys(privateKey),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 不验证主机key（慎用）
	}

	// 建立SSH连接
	client, err := ssh.Dial("tcp", fmt.Sprintf("%s:%d", remoteServer, sshPort), config)
	if err != nil {
		fmt.Println("Failed to dial: ", err)
		return nil, err
	}
	o.Client = client
	logger.Info("ssh success:", o.Host, ":", o.Port)
	return client, nil
}

func ExecuteRemoteCommand(client *ssh.Client, command string) (string, error) {
	// 创建一个新的会话
	session, err := client.NewSession()
	if err != nil {
		return "", fmt.Errorf("failed to create session: %v", err)
	}
	defer session.Close()

	// 执行远程命令
	output, err := session.CombinedOutput(command)
	if err != nil {
		return "", fmt.Errorf("failed to run command: %v", err)
	}

	return string(output), nil
}

func (o *SshServer) ExecCommand(command string) (string, error) {
	// 创建一个新的会话
	if o.Client == nil {
		err := errors.New("client is nil")
		logger.Error(err)
		return "", err
	}
	session, err := o.Client.NewSession()
	if err != nil {
		fmt.Println("Failed to create session: ", err)
		logger.Error(err)
		return "", err
	}
	defer session.Close()
	logger.Info("session new success,rum command:", command)
	// 执行远程命令
	output, err := session.CombinedOutput(command)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	return string(output), nil
}

func (o *SshServer) Close() error {
	if o.Client != nil {
		return o.Client.Close()
	}
	return nil
}

//{
//
//	// 创建一个新的会话
//	session, err := client.NewSession()
//	if err != nil {
//	fmt.Println("Failed to create session: ", err)
//	return
//	}
//	defer session.Close()
//
//	// 设置标准输入、输出和错误输出
//	session.Stdout = os.Stdout
//	session.Stderr = os.Stderr
//	session.Stdin = os.Stdin
//
//	// 执行远程命令
//	command := "ls -l" // 你想要执行的命令
//	err = session.Run(command)
//	if err != nil {
//	fmt.Println("Failed to run: ", err)
//	return
//	}
//
//}

package common

import "sync"

// Queue 结构体定义
type Queue struct {
	items []interface{}
	lock  sync.Mutex
	Cond  *sync.Cond
}

// Enqueue 方法用于将元素放入队列尾部
func (q *Queue) Enqueue(item interface{}) bool {
	q.lock.Lock()
	defer q.lock.Unlock()

	// 检查元素是否已存在
	for _, v := range q.items {
		if v == item {
			return false // 如果元素已存在，直接返回，不添加
		}
	}
	q.items = append(q.items, item)
	//q.Cond.Signal()
	q.Cond.Broadcast()
	return true
}

// Dequeue 方法用于从队列头部取出元素
func (q *Queue) Dequeue() interface{} {
	q.lock.Lock()
	defer q.lock.Unlock()
	if len(q.items) == 0 {
		return nil
	}
	item := q.items[0]
	q.items = q.items[1:]
	return item
}

// Front 方法用于获取队列头部元素
func (q *Queue) Front() interface{} {
	q.lock.Lock()
	defer q.lock.Unlock()
	if len(q.items) == 0 {
		return nil
	}
	return q.items[0]
}

// Contains 方法用于检测队列中是否包含某个元素
func (q *Queue) Contains(item interface{}) bool {
	q.lock.Lock() // 加锁，确保线程安全
	defer q.lock.Unlock()
	if q.items == nil {
		return false
	}
	for _, v := range q.items {
		if v == item {
			return true // 如果元素已存在，返回 true
		}
	}
	return false // 如果元素不存在，返回 false
}

// IsEmpty 方法用于判断队列是否为空
func (q *Queue) IsEmpty() bool {
	return len(q.items) == 0
}

// Size 方法用于获取队列的大小
func (q *Queue) Size() int {
	return len(q.items)
}

func (q *Queue) Items() []interface{} {
	if q.items == nil {
		return make([]interface{}, 0)
	}
	return q.items
}

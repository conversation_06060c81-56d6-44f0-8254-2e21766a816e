package enums

type userTypeEnum_ struct {
	<PERSON>kno<PERSON>, <PERSON><PERSON><PERSON>, System, Other int
}

var UserTypeEnum = userTypeEnum_{
	Unknow:  0, //未知
	Suanyun: 1, //
	System:  2, //
	Other:   3, //
}

func (obj userTypeEnum_) Name(v int) string {
	if val, ok := GpuStatusName[v]; ok {
		//存在
		return val
	}
	return ""
}

var UserTypeNameEnum = map[int]string{
	0: "未知",
	1: "算云",
	2: "系统",
	3: "第三方",
}

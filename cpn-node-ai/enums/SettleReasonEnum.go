package enums

type settleReasonEnum_ struct {
	CreateSubInst, Shutdown, Hourly, Usage2Sub, Sub2Usage, Renewal int
}

var SettleReasonEnum = settleReasonEnum_{
	CreateSubInst: 1, //创建订阅实例
	Shutdown:      2, //关机结算
	Hourly:        3, //整点结算
	Usage2Sub:     4, //按量转订阅
	Sub2Usage:     5, //订阅转按量
	Renewal:       6, //续费
}

func (obj settleReasonEnum_) Name(v int) string {
	if val, ok := settleReasonNameEnum[v]; ok {
		//存在
		return val
	}

	return ""
}

var settleReasonNameEnum = map[int]string{
	1: "实例订阅",
	2: "关机结算",
	3: "整点结算",
	4: "按量转订阅",
	5: "订阅转按量",
	6: "续费",
}

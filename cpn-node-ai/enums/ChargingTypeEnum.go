package enums

type chargingTypeEnum_ struct {
	Usage, Day, Week, Month int
}

var ChargingTypeEnum = chargingTypeEnum_{
	Usage: 1, //按量
	Day:   2, //包日
	Week:  3, //包周
	Month: 4, //包月
}

func (obj chargingTypeEnum_) Name(v int) string {
	if val, ok := ChargingTypeNameEnum[v]; ok {
		//存在
		return val
	}
	return ""
}

var ChargingTypeNameEnum = map[int]string{
	1: "按量",
	2: "包日",
	3: "包周",
	4: "包月",
}

package enums

import (
	"reflect"
	"time"
)

type subDurationEnum_ struct {
	W1, M1, Y1 string
}

var SubDurationEnum = subDurationEnum_{
	W1: "W1", //一周
	M1: "M1", //一月
	Y1: "Y1", //一年
}

func (c subDurationEnum_) Get(id string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if typeVo.Field(i).Name == id {
			return vo.Field(i).Interface().(string)
		}
	}
	return ""
}

func (c subDurationEnum_) GetExpiresDate(id string, curDate time.Time) time.Time {
	switch id {
	case c.W1:
		return curDate.AddDate(0, 0, 7)
	case c.M1:
		return curDate.AddDate(0, 1, 0)
	case c.Y1:
		return curDate.AddDate(1, 0, 0)

	}
	return curDate
}

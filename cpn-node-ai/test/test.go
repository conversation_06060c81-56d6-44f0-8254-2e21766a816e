package main

import (
	"cpn-ai/common/utils"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"regexp"
)

func getDockerSshMappingPort(ports string) int {

	//0.0.0.0:17110->22/tcp, :::17110->22/tcp
	// 创建正则表达式匹配模式
	re := regexp.MustCompile(`\d:(\d{5})->22/tcp`)

	// 查找匹配的字符串
	matches := re.FindAllStringSubmatch(ports, -1)
	for _, match := range matches {
		if len(match) >= 2 {
			fmt.Println(match[1])
			return utils.String2Int(match[1])
		}
	}
	return 0
}
func encrypt(plainText string, key []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 使用随机生成的 IV（初始化向量）
	ciphertext := make([]byte, aes.BlockSize+len(plainText))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}

	// 使用 AES 加密模式进行加密
	mode := cipher.NewCBCEncrypter(block, iv)
	copy(ciphertext[aes.BlockSize:], []byte(plainText))
	mode.CryptBlocks(ciphertext[aes.BlockSize:], ciphertext[aes.BlockSize:])

	// 返回 Base64 编码的密文
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func decrypt(ciphertext string, key []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 解码 Base64 得到密文
	decodedCiphertext, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	// 分离 IV 和实际密文
	iv := decodedCiphertext[:aes.BlockSize]
	ciphertextData := decodedCiphertext[aes.BlockSize:]

	// 使用 AES 解密模式进行解密
	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(ciphertextData, ciphertextData)

	// 返回解密后的明文
	return string(ciphertextData), nil
}

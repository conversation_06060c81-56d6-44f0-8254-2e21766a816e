package service

import (
	"bufio"
	"cpn-ai/common/logger"
	"fmt"
	"os"
	"path/filepath"
)

//ssh frp@117.187.188.3 -p 3984
//
//passwd: Zeyun123asd!@#$

const (
	serverAddr   = "117.187.188.3"
	serverPort   = 7000
	tomlFilePath = "root/frp/frpc.toml"
)

func WriteFrpcToml(lines []string) error {

	directory := filepath.Dir(tomlFilePath) // 获取目录路径
	// 创建目录，存在则不创建，不存在则创建
	if err := os.MkdirAll(directory, 0755); err != nil {
		logger.Error(err)
		return err
	}
	// 创建或打开文件
	file, err := os.Create(tomlFilePath)
	if err != nil {
		fmt.Println("无法创建文件:", err)
		return err
	}
	defer file.Close()

	// 创建一个写入器
	writer := bufio.NewWriter(file)

	// 将配置信息写入文件，并在每条记录之后添加换行符
	for _, line := range lines {
		_, err := writer.WriteString(line + "\n")
		if err != nil {
			fmt.Println("写入文件出错:", err)
			return err
		}
	}

	// 刷新缓冲区，确保所有数据都写入文件
	err = writer.Flush()
	if err != nil {
		//fmt.Println("刷新缓冲区出错:", err)
		return err
	}
	return nil
}

func WriteFrpcTomlContent(content string) error {
	directory := filepath.Dir(tomlFilePath) // 获取目录路径
	// 创建目录，存在则不创建，不存在则创建
	if err := os.MkdirAll(directory, 0755); err != nil {
		logger.Error(err)
		return err
	}
	// 创建或打开文件
	file, err := os.Create(tomlFilePath)
	if err != nil {
		fmt.Println("无法创建文件:", err)
		return err
	}
	defer file.Close()

	// 创建一个写入器
	writer := bufio.NewWriter(file)

	// 将配置信息写入文件，并在每条记录之后添加换行符
	if _, err := writer.WriteString(content); err != nil {
		logger.Error(err)
		return err
	}

	// 刷新缓冲区，确保所有数据都写入文件
	err = writer.Flush()
	if err != nil {
		//fmt.Println("刷新缓冲区出错:", err)
		return err
	}
	return nil
}

func GenFrpcToml(local2remotePort map[int]int) string {
	lines := make([]string, 0)
	lines = append(lines, fmt.Sprintf(`serverAddr = "%s"`, serverAddr))
	lines = append(lines, fmt.Sprintf(`serverPort = %d`, serverPort))
	for k, v := range local2remotePort {
		tmp := genProxieToml(k, v)
		lines = append(lines, tmp...)
	}

	content := ""
	for _, line := range lines {
		content += line + "\n"
	}
	return content
}

func genProxieToml(localPort int, remotePort int) []string {
	line := make([]string, 0)
	line = append(line, "\n[[proxies]]")
	line = append(line, `name = "ssh"`)
	line = append(line, `type = "tcp"`)
	line = append(line, `localIP = "127.0.0.1"`)
	line = append(line, fmt.Sprintf(`localPort = %d`, localPort))
	line = append(line, fmt.Sprintf(`remotePort = %d`, remotePort))
	return line
}

package service

import (
	"bufio"
	"context"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/service/tasklog"
	"cpn-ai/structs"
	"fmt"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/client"
	"github.com/docker/docker/pkg/jsonmessage"
	"sync/atomic"
)

type DockerService struct {
	Host   string         `json:"host"`
	Client *client.Client `json:"-"`
}

func (o *DockerService) NewClient(host string) (*client.Client, error) {
	o.Host = host
	hostUrl := fmt.Sprintf("tcp://%s:2375", o.Host)
	// 创建 Docker API 客户端
	if cli, err := client.NewClientWithOpts(client.WithHost(hostUrl), client.WithVersion("1.41")); err != nil {
		logger.Error(err, " host", host)
		return nil, err
	} else {
		o.Client = cli
		return cli, nil
	}
}

func (o *DockerService) ContainerCommit(ctx context.Context, containerId string, imagePath string) error {
	command := enums.DockerCommandEnum.Commit + " " + containerId
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	NodeService.CommandRunning.Store(commandKey, structs.RunningCommand{Command: command, StartTime: jsontime.Now()})
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	logger.Info("开始提交镜像", containerId)
	if r, err := o.Client.ContainerCommit(ctx, containerId, container.CommitOptions{}); err != nil {
		logger.Error(err, "   "+containerId)
		return err
	} else {
		fmt.Println(r.ID)
		return nil
	}
}

func (o *DockerService) ImagePull(ctx context.Context, imageName string) error {
	command := enums.DockerCommandEnum.Pull + " " + imageName
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	NodeService.CommandRunning.Store(commandKey, structs.RunningCommand{Command: command, StartTime: jsontime.Now()})
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	logger.Info("开始拉取镜像", imageName)
	if reader, err := o.Client.ImagePull(ctx, imageName, image.PullOptions{}); err != nil {
		logger.Error(err, "   "+imageName)
		return err
	} else {
		defer reader.Close()
		progressM := make(map[string]jsonmessage.JSONProgress)
		scanner := bufio.NewScanner(reader)
		for scanner.Scan() {
			line := scanner.Text()
			//{"status":"Pulling from public/34d7cb39fdc840bfb0af8565333a67c2","id":"1.0"}
			//{"status":"Already exists","progressDetail":{},"id":"43f89b94cd7d"}
			//{"status":"Pulling fs layer","progressDetail":{},"id":"d38c33e9afaf"}
			//{"status":"Downloading","progressDetail":{"current":540036,"total":1612724203},"progress":"[\\u003e                                                  ]    540kB/1.613GB","id":"d38c33e9afaf"}
			//{"status":"Verifying Checksum","progressDetail":{},"id":"d38c33e9afaf"}
			//{"status":"Download complete","progressDetail":{},"id":"d38c33e9afaf"}
			//{"status":"Extracting","progressDetail":{"current":557056,"total":1612724203},"progress":"[\\u003e                                                  ]  557.1kB/1.613GB","id":"d38c33e9afaf"}
			//{"status":"Extracting","progressDetail":{"current":655654912,"total":1612724203},"progress":"[====================\u003e                              ]  655.7MB/1.613GB","id":"d38c33e9afaf"}
			//{"status":"Pull complete","progressDetail":{},"id":"d38c33e9afaf"}
			//{"status":"Digest: sha256:c0908b57284180fe6d0ff9bb07384b9ba99cb3cbd346033a5e12196b1476160b"}
			//{"status":"Status: Downloaded newer image for hub.suanyun.cn/public/34d7cb39fdc840bfb0af8565333a67c2:1.0"}

			var message jsonmessage.JSONMessage
			if err := utils.GetStructFromJson(&message, line); err != nil {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在拉取镜像", line, nil)
			} else {
				coverStatus := ""
				if message.Status == "Downloading" {
					coverStatus = message.Status
					progressM["Downloading_"+message.ID] = *message.Progress
				}
				if message.Status == "Extracting" {
					coverStatus = message.Status
					progressM["Extracting_"+message.ID] = *message.Progress
				}

				current := int64(0)
				total := int64(0)
				for _, progress := range progressM {
					current += progress.Current
					total += progress.Total
				}

				percent := float64(0)
				if total > 0 {
					percent = float64(current) / float64(total)
				}

				taskProgress := tasklog.TaskProgress{
					Status:      message.Status,
					CoverStatus: coverStatus,
					Current:     current,
					Total:       total,
					Percent:     percent,
				}

				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在拉取镜像", line, taskProgress)
			}

		}
		if err := scanner.Err(); err != nil {
			logger.Error(err, imageName)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在拉取镜像", err.Error(), nil)
		}
	}
	return nil
}

func (o *DockerService) ImagePush(ctx context.Context, imageStr string) error {
	command := enums.DockerCommandEnum.Push + " " + imageStr
	commandKey := utils.GetUUID()
	atomic.AddInt32(&NodeService.CommandRunningCount, 1)
	NodeService.CommandRunning.Store(commandKey, structs.RunningCommand{Command: command, StartTime: jsontime.Now()})
	defer func() {
		atomic.AddInt32(&NodeService.CommandRunningCount, -1)
		NodeService.CommandRunning.Delete(commandKey)
	}()

	logger.Info("开始推送镜像", imageStr)
	if reader, err := o.Client.ImagePush(ctx, imageStr, image.PushOptions{}); err != nil {
		logger.Error(err, "   "+imageStr)
		return err
	} else {
		defer reader.Close()
		progressM := make(map[string]jsonmessage.JSONProgress)
		scanner := bufio.NewScanner(reader)
		for scanner.Scan() {
			line := scanner.Text()
			//{"status":"Pulling from public/34d7cb39fdc840bfb0af8565333a67c2","id":"1.0"}
			//{"status":"Already exists","progressDetail":{},"id":"43f89b94cd7d"}
			//{"status":"Pulling fs layer","progressDetail":{},"id":"d38c33e9afaf"}
			//{"status":"Downloading","progressDetail":{"current":540036,"total":1612724203},"progress":"[\\u003e                                                  ]    540kB/1.613GB","id":"d38c33e9afaf"}
			//{"status":"Verifying Checksum","progressDetail":{},"id":"d38c33e9afaf"}
			//{"status":"Download complete","progressDetail":{},"id":"d38c33e9afaf"}
			//{"status":"Extracting","progressDetail":{"current":557056,"total":1612724203},"progress":"[\\u003e                                                  ]  557.1kB/1.613GB","id":"d38c33e9afaf"}
			//{"status":"Extracting","progressDetail":{"current":655654912,"total":1612724203},"progress":"[====================\u003e                              ]  655.7MB/1.613GB","id":"d38c33e9afaf"}
			//{"status":"Pull complete","progressDetail":{},"id":"d38c33e9afaf"}
			//{"status":"Digest: sha256:c0908b57284180fe6d0ff9bb07384b9ba99cb3cbd346033a5e12196b1476160b"}
			//{"status":"Status: Downloaded newer image for hub.suanyun.cn/public/34d7cb39fdc840bfb0af8565333a67c2:1.0"}

			var message jsonmessage.JSONMessage
			if err := utils.GetStructFromJson(&message, line); err != nil {
				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在推送镜像", line, nil)
			} else {
				coverStatus := ""
				if message.Status == "Downloading" {
					coverStatus = message.Status
					progressM["Downloading_"+message.ID] = *message.Progress
				}
				if message.Status == "Extracting" {
					coverStatus = message.Status
					progressM["Extracting_"+message.ID] = *message.Progress
				}

				current := int64(0)
				total := int64(0)
				for _, progress := range progressM {
					current += progress.Current
					total += progress.Total
				}

				percent := float64(0)
				if current >= total {
					percent = float64(100)
				} else if total > 0 {
					percent = float64(current) / float64(total)
				}

				taskProgress := tasklog.TaskProgress{
					Status:      message.Status,
					CoverStatus: coverStatus,
					Current:     current,
					Total:       total,
					Percent:     percent,
				}

				tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在推送镜像", line, taskProgress)
			}

		}
		if err := scanner.Err(); err != nil {
			logger.Error(err, imageStr)
			tasklog.Save(ctx, tasklog.NormalTaskStateEnum.Progress, "正在推送镜像", err.Error(), nil)
		}
	}
	return nil
}

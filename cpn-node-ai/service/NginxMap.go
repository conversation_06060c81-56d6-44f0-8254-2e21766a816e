package service

import (
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"errors"
	"fmt"
	"io"
	"unsafe"
)

func NginxAddInstance(virtualId uint, instanceUuid string) (string, error) {
	msg := ""
	var docker DockerItem
	if virtual, ok := NodeService.GetVirtual(virtualId); ok {
		for _, tmpDocker := range virtual.GetDockers() {
			if tmpDocker.InstanceUuid == instanceUuid {
				docker = tmpDocker
				break
			}
			if tmpDocker.StartupMark == instanceUuid {
				docker = tmpDocker
				break
			}
		}
	}

	if docker.ID == "" {
		msg = "Docker不存在"
		logger.Error(msg, "virtualId：", virtualId, " instanceUuId：", instanceUuid)
		return msg, errors.New(msg)
	}

	if len(docker.MapPorts) == 0 {
		msg = "无需映射端口"
		logger.Error(msg, " instanceUuId：", instanceUuid)
		return msg, nil
	}

	mapKey := docker.InstanceUuid

	idx := 0
	for _, port := range docker.MapPorts {
		webUrl := GetMapUrl(docker, port)
		if str, err := nginxHandle("add", mapKey+port, webUrl); err != nil {
			msg += fmt.Sprintf("端口%s未映射成功", mapKey+port)
			logger.Error(msg, str, err)
		} else {
			idx++
		}
	}

	if idx < len(docker.MapPorts) {
		logger.Error(fmt.Sprintf("端口未全部映射成功%d/%d", idx, len(docker.MapPorts)))
		return msg, errors.New("端口未全部映射成功")
	}
	return "映射成功", nil
}

func NginxAddStartupMark(virtualId uint, startupMark string) (string, error) {
	msg := ""
	var docker DockerItem
	if virtual, ok := NodeService.GetVirtual(virtualId); ok {
		for _, tmpDocker := range virtual.GetDockers() {
			if tmpDocker.StartupMark == startupMark {
				docker = tmpDocker
				break
			}
		}
	}

	if docker.ID == "" {
		msg = "Docker不存在"
		logger.Error(msg, virtualId, "   ", startupMark)
		return msg, errors.New(msg)
	}

	if len(docker.MapPorts) == 0 {
		msg = "无需映射端口"
		logger.Error(msg, startupMark)
		return msg, nil
	}

	idx := 0
	for _, port := range docker.MapPorts {
		webUrl := GetMapUrl(docker, port)
		if webUrl == "" {
			logger.Error("webUrl is empty", docker.StartupMark, "  ", port)
			continue
		}
		if str, err := nginxHandle("add", startupMark+port, webUrl); err != nil {
			msg += fmt.Sprintf("端口%s未映射成功", startupMark+port)
			logger.Error(msg, str, err)
		} else {
			idx++
		}
	}

	if idx < len(docker.MapPorts) {
		logger.Error(fmt.Sprintf("端口未全部映射成功%d/%d", idx, len(docker.MapPorts)))
		return msg, errors.New("端口未全部映射成功")
	}
	return "映射成功", nil
}

func NginxGetByKey(key string) (string, error) {
	msg := ""
	if key == "" {
		msg = "参数不能为空"
		return msg, errors.New(msg)
	}
	str, err := nginxHandle("get", key, "")
	if err != nil {
		logger.Error(err, "  str:", str)
		return "", err
	}
	logger.Info("get nginx get:", key, "：", str)
	return str, nil
}

func NginxRemoveInstance(virtualId uint, podId uint, instanceUuid string) (string, []string, []string, error) {
	msg := ""
	removeKeys := make([]string, 0)
	errKeys := make([]string, 0)
	if len(instanceUuid) != 32 {
		msg = "实例Uuid长度不正确"
		logger.Error(msg, "virtualId：", virtualId, " instanceUuId：", instanceUuid)
		return msg, removeKeys, errKeys, errors.New(msg)
	}
	//var docker DockerItem
	//if virtual, ok := NodeService.Virtuals[virtualId]; ok {
	//	for _, tmpDocker := range virtual.Dockers {
	//		if tmpDocker.InstanceUuid == instanceUuid {
	//			docker = tmpDocker
	//			break
	//		}
	//		if tmpDocker.StartupMark == instanceUuid {
	//			docker = tmpDocker
	//			break
	//		}
	//	}
	//}
	//
	//if docker.ID != "" {
	//	msg = "Docker还存在"
	//	logger.Error(msg, "virtualId：", virtualId, " instanceUuId：", instanceUuid)
	//	return msg, removeKeys, errKeys, errors.New(msg)
	//}
	instKey := instanceUuid

	hostPorts := make([]string, 0) //88 89 71

	if err := NodeService.LoadPods(podId); err != nil {
		logger.Error("载入Pod失败", err, " podId:", podId)
	}

	removeKeys = append(removeKeys, "lastusetime_"+instKey)
	if podValue, ok := NodeService.Pods.Load(podId); !ok {
		msg = "Pod不存在"
		logger.Error(msg, "virtualId：", virtualId, "podId：", podId, " instanceUuId：", instanceUuid)
		return msg, removeKeys, errKeys, errors.New(msg)
	} else {
		pod := podValue.(PodItem)
		if pod.PortMap != nil {
			for k, _ := range pod.PortMap {
				hostPorts = append(hostPorts, k)
				removeKeys = append(removeKeys, instKey+k)
				removeKeys = append(removeKeys, "lastusetime_"+instKey+k)
			}
		}
		if pod.PortMap == nil || len(pod.PortMap) == 0 || true {
			logger.Info("加入默认removeKeys")
			removeKeys = append(removeKeys, instKey+"88")
			removeKeys = append(removeKeys, "lastusetime_"+instKey+"88")
			removeKeys = append(removeKeys, instKey+"89")
			removeKeys = append(removeKeys, "lastusetime_"+instKey+"89")
		}
	}
	logger.Info("removeKeys:", utils.GetJsonFromStruct(removeKeys))

	for _, key := range removeKeys {
		if str, err := nginxHandle("remove", key, ""); err != nil {
			msg = fmt.Sprintf("地址映射%s移除失败", key)
			logger.Error(msg, str, err)
			errKeys = append(errKeys, key)
		} else {
			logger.Info("RemoveNginxKey:", key, " str:", str)

		}
	}
	if len(errKeys) == 0 {
		msg = fmt.Sprintf("地址映射已全部移除(%d)", len(removeKeys))
		return msg, removeKeys, errKeys, nil
	} else {
		msg = fmt.Sprintf("地址映射未全部移除")
		logger.Error(msg, " instanceUuid:", instanceUuid, "  ", utils.GetJsonFromStruct(errKeys))
		return msg, removeKeys, errKeys, errors.New("地址映射未全部移除")
	}
}

func NginxRemoveStartupMark(virtualId uint, startupMark string) (string, error) {
	msg := ""

	var docker DockerItem
	if virtual, ok := NodeService.GetVirtual(virtualId); ok {
		for _, tmpDocker := range virtual.GetDockers() {
			if tmpDocker.StartupMark == startupMark {
				docker = tmpDocker
				break
			}
		}
	}

	if docker.ID == "" {
		msg = "Docker不存在"
		logger.Error(msg, virtualId, "   ", startupMark)
		return msg, errors.New(msg)
	}

	idx := 0
	for _, port := range docker.MapPorts {
		if str, err := nginxHandle("remove", startupMark+port, ""); err != nil {
			msg = fmt.Sprintf("地址映射%s移除失败", startupMark+port)
			logger.Error(msg, str, err)
		} else {
			idx++
		}
	}

	if idx < len(docker.MapPorts) {
		msg = fmt.Sprintf("地址映射未全部移除成功(%d/%d)", idx, len(docker.MapPorts))
		logger.Error(msg)
		return msg, errors.New(msg)
	}

	//str, err = nginxHandle("remove", "lastusetime_"+instanceUuid, instance.WebUrl)
	//if err != nil {
	//	logger.Error(err, "  str:", str)
	//	return "", err
	//}
	msg = fmt.Sprintf("地址映射已全部移除(%d)", len(docker.MapPorts))
	return msg, nil
}

func NginxRemoveKey(key string) error {
	if str, err := nginxHandle("remove", key, ""); err != nil {
		msg := fmt.Sprintf("地址映射%s移除失败", key)
		logger.Error(msg, str, err)
		return err
	} else {
		return nil
	}
}

func NginxList() (string, error) {
	if str, err := nginxHandle("list", "", ""); err != nil {
		msg := fmt.Sprintf("地址映射列表失败")
		logger.Error(msg, str, err)
		return "", err
	} else {
		return str, nil
	}
}

func NginxListInstance(virtualId uint, instanceUuid string) (map[string]string, error) {
	msg := ""

	var docker DockerItem
	if virtual, ok := NodeService.GetVirtual(virtualId); ok {
		for _, tmpDocker := range virtual.GetDockers() {
			if tmpDocker.InstanceUuid == instanceUuid {
				docker = tmpDocker
				break
			}
		}
	}
	mm := make(map[string]string)
	if docker.ID == "" {
		msg = "Docker不存在"
		logger.Error(msg, "virtualId：", virtualId, " instanceUuId：", instanceUuid)
		return mm, errors.New(msg)
	}

	for _, port := range docker.MapPorts {
		webUrl := GetMapUrl(docker, port)
		if str, err := nginxHandle("get", instanceUuid+port, webUrl); err != nil {
			msg += fmt.Sprintf("端口%s移除失败", instanceUuid+port)
			logger.Error(msg, str, err)
			mm[port] = ""
		} else {
			mm[port] = str
		}
	}
	return mm, nil
}

func NginxListStartupMark(virtualId uint, startupMark string) (map[string]string, error) {
	msg := ""

	var docker DockerItem
	if virtual, ok := NodeService.GetVirtual(virtualId); ok {
		for _, tmpDocker := range virtual.GetDockers() {
			if tmpDocker.StartupMark == startupMark {
				docker = tmpDocker
				break
			}
		}
	}
	mm := make(map[string]string)
	if docker.ID == "" {
		msg = "Docker不存在"
		logger.Error(msg, virtualId, "   ", startupMark)
		return mm, errors.New(msg)
	}

	for _, port := range docker.MapPorts {
		webUrl := GetMapUrl(docker, port)
		if str, err := nginxHandle("get", startupMark+port, webUrl); err != nil {
			msg += fmt.Sprintf("端口%s移除失败", startupMark+port)
			logger.Error(msg, str, err)
			mm[port] = ""
		} else {
			mm[port] = str
		}
	}
	return mm, nil
}

func nginxHandle(action string, k string, v string) (string, error) {
	msg := ""
	headers := make(map[string]string, 0)
	headers["Content-Type"] = "text/html; charset=utf-8"
	headers["User-Agent"] = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.2 Safari/605.1.15"
	headers["accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
	headers["Authorization"] = "ac4f334sddbfe701dda3783124ef6822a"
	domain := "https://www.suanyun.cn"
	if config.Env == enums.EnvEnum.PRODUCTION || config.Env == enums.EnvEnum.ONLINE {
		domain = "http://*************:6004"
	}
	if config.HttpPort == ":7004" || config.HttpPort == ":7005" || config.HttpPort == ":7006" || config.HttpPort == ":7007" || config.HttpPort == ":7008" || config.HttpPort == ":7009" {
		domain = "http://localhost:6004"
	}
	if config.HttpPort == ":7010" || config.HttpPort == ":7011" || config.HttpPort == ":7012" || config.HttpPort == ":7013" || config.HttpPort == ":7014" || config.HttpPort == ":7015" {
		domain = "http://localhost:6004"
	}
	domain = "http://localhost:6004"
	url := fmt.Sprintf(`%s/setpodmapping?a=%s&k=%s&v=%s`, domain, action, k, v)
	res, err := utils.Get(url, headers, nil)
	defer res.Body.Close()
	if err != nil {
		msg = "nginx映射设置失败"
		logger.Error(msg, err, url)
		return "", err
	}
	content, err := io.ReadAll(res.Body)
	if err != nil {
		msg = "读取返回内容失败"
		//logger.Error(msg, err, url)
		return "", err
	}
	str := (*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	return *str, nil
}

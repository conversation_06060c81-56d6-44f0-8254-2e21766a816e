server {
    listen 80;
    server_name *.suanyun.cn;
    proxy_http_version  1.1;  #1.1版本才支持wss

    #启用支持websocket连接的配置
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_connect_timeout 60; #配置等待服务器响应时间
    proxy_read_timeout 600;
    proxy_send_timeout 600;
    proxy_redirect off;

    location /capture {
        content_by_lua_block {
           local res = ngx.location.capture("/loadpodmapping", { method = ngx.HTTP_GET})
          if res.status == ngx.HTTP_OK then
            ngx.say(res.body)
          end
        }
    }

    location /loadpodmapping {
      set_by_lua_block $instanceUuid {
          local pattern = "(.+).suanyun.cn"
          local result = string.match(ngx.var.host, pattern)
          if result then
                  local l = string.len(result)
                  if l>=16 then
                      return result
                  end
          end
          return ""
      }
      #proxy_pass http://127.0.0.1:6004/setpodmapping?page=1&size=20;
      proxy_pass http://127.0.0.1:6001/api/sys/weburl2nginx?instance_uuid=$instanceUuid&action=add;
      proxy_set_header authorization "ac4f334sddbfe701dda3783124ef6822a";

      #return 200 "Captured value: ";
    }

    location / {
        set_by_lua_block $redirected_url {
              local scheme=ngx.var.scheme
              local host = ngx.var.host
              local referer = ngx.var.http_referer
              local requestUrl = ngx.var.request_uri
              -- ngx.say(host)
              -- ngx.say(referer)
              -- ngx.say(requestUrl)

              local pattern = "(.+).suanyun.cn"
              local result = string.match(ngx.var.host, pattern)
              if result then
                  local l = string.len(result)
                  if l>=16 then
                      -- ngx.say(result)
                      --local lastusetime ="/api/sys/lastusetime?instance_uuid="..result
                      -- local reps = ngx.location.capture('/loadpodmapping')

                      local dict = ngx.shared.my_shared_data
                      local v = dict:get(result)
                      if v then
                          local lastChar = string.sub(v, -1)
                          if lastChar == '/' then
                              v = string.sub(v, 1, -2)
                          end


                          -- ngx.say(v)
                          local firstChar = string.sub(requestUrl, 1, 1)
                          if firstChar == '/' then
                              local l = string.len(requestUrl)
                              requestUrl = string.sub(requestUrl, 2, l)
                          end
                          local redirectedUrl = v..requestUrl

                          -- 设置最后使用时间
                          local lastusetimeKey = "lastusetime_"..result
                          dict:set(lastusetimeKey, os.time())

                          return v
                          -- ngx.say(redirectedUrl)
                      else
                          return ""
                          -- ngx.say("no data")
                          -- ngx.exit(ngx.HTTP_OK)
                      end
                  end
              end
              return result
        }


        content_by_lua_block {
           if ngx.var.redirected_url=="" then
              -- ngx.say("kong")
              local res = ngx.location.capture("/loadpodmapping", { method = ngx.HTTP_GET})
              -- ngx.say(res.status)
              if res.status == ngx.HTTP_OK then
                 -- ngx.say(res.body)
                 ngx.header.content_type = "text/plain; charset=utf-8"
                 ngx.say("请刷新页面重试")
              end
           end
        }




        #return 200 $redirected_endurl;

        #proxy_pass http://192.168.200.173:13088/;
        #proxy_pass $redirected_url;
        if ($redirected_url) {
            proxy_pass $redirected_url;
        }
    }
}

server
{
    listen 80;
    server_name suanyun.cn www.suanyun.cn aaa.suanyun.cn;
    index index.php index.html index.htm default.php default.htm default.html;
    root /www/wwwroot/suanyun.cyuai.com/dist;
    #try_files $uri $uri/ @404_handler;  # 使用 @404_handler 处理 404 错误



    #SSL-START SSL相关配置，请勿删除或修改下一行带注释的404规则
    #error_page 404/404.html;
    #SSL-END

    #ERROR-PAGE-START  错误页配置，可以注释、删除或修改
    #error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #PHP-INFO-START  PHP引用配置，可以注释或修改
    include enable-php-00.conf;
    #PHP-INFO-END

    #REWRITE-START URL重写规则引用,修改后将导致面板设置的伪静态规则失效
    include /www/server/panel/vhost/rewrite/suanyun.cn.conf;
    #REWRITE-END

location @404_handler {
    content_by_lua_block {

        local host = ngx.var.host
        local referer = ngx.var.http_referer
        local requestUrl = ngx.var.request_uri
        ngx.say(host)
        ngx.say(referer)
        ngx.say(requestUrl)

        local pattern = "(.+).suanyun.cn"
        local result = string.match(ngx.var.host, pattern)
        if result then
          local l = string.len(result)
          if l>=16 then
              ngx.say(result)
              local dict = ngx.shared.my_shared_data
              local v = dict:get(result)
              if v then
                  ngx.say(v)
                  local firstChar = string.sub(requestUrl, 1, 1)
                  if firstChar == '/' then
                      local l = string.len(requestUrl)
                      requestUrl = string.sub(requestUrl, 2, l)
                  end
                  local redirectedUrl = v..requestUrl
                  ngx.say(redirectedUrl)
              end
          end

        end

    }

}

location /setpodmapping {
  set $parm '';
  content_by_lua_block{
          local request_method = ngx.var.request_method
          local headers = ngx.req.get_headers()
          --for key, value in pairs(headers) do
           --   ngx.say(key .. ": " .. value)
          --end
          local token = headers["authorization"] or ""
          if token~="ac4f334sddbfe701dda3783124ef6822a" then
            ngx.say("no permission")
            return
          end
          --ngx.say(token)
          if request_method == "GET" then
                  local args = ngx.req.get_uri_args()
                  local a = args["a"]
                  local k = args["k"]
                  local v = args["v"]

                  local dict = ngx.shared.my_shared_data
                  if a=="list" then
                    -- 获取字典中所有键的列表
                    local keys = dict:get_keys(0)  -- 这里的 0 表示获取所有键
                    -- 遍历所有键并获取对应的值
                    for _, key in ipairs(keys) do
                        local value = dict:get(key)
                        ngx.say(key.."  "..value)
                    end
                  elseif a=="add" then
                    dict:set(k, v)
                    ngx.say(k.." "..v)
                  elseif a=="remove" then
                    dict:delete(k)
                    ngx.say(k.."  "..v)
                  elseif a=="get" then
                    local value = dict:get(k)
                    if value ~= nil then
                        -- 键存在，执行相应逻辑
                        ngx.say(k.."  "..dict:get(k))
                    else
                        -- 键不存在，执行相应逻辑
                        ngx.say("Key does not exist.")
                    end

                  end

          elseif request_method == "POST" then
                  ngx.req.read_body()
                  local arg = ngx.req.get_post_args()["b"] or 0
                  ngx.var.parm = arg
                  ngx.say(arg)
          end
  }
  # return 200 "Captured value: $parm";
}



    location /v1/
    {
        proxy_pass http://127.0.0.1:6001/v1/;
        proxy_set_header Remote_addr $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    location /api/
    {
        proxy_pass http://127.0.0.1:6001/api/;
        proxy_set_header Remote_addr $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    location ^~/conversation {
        root /www/wwwroot/suanyun.cyuai.com;
        index index.html;
        try_files $uri $uri/ /conversation/index.html;
    }
    #解决vue包路径不存在，刷新页面404的问题
    error_page 404 =200 /;
    location /docs/ {
        # 在 docs 目录下启用 error_page 404
        error_page 404 =200 /;
        try_files $uri $uri/ /docs/index.html;
    }
    location /console/ {
      error_page 404 =200 /;
    }


    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }

    #一键申请SSL证书验证目录相关设置
    location ~ \.well-known{
        allow all;
    }

    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      30d;
        error_log /dev/null;
        access_log /dev/null;
    }

    location ~ .*\.(js|css)?$
    {
        expires      12h;
        error_log /dev/null;
        access_log /dev/null;
    }
    access_log  /www/wwwlogs/suanyun.cn.log;
    error_log  /www/wwwlogs/suanyun.cn.error.log;
}
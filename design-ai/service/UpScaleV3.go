package service

import (
	"design-ai/enums"
	"design-ai/model"
	"design-ai/utils/logger"
	"design-ai/utils/myredis"
	"design-ai/utils/tools"
	"encoding/json"
	"errors"
	"fmt"
	"time"
)

type upscale_v3 struct {
}

func (d *upscale_v3) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("UpScaleService奔溃:", e)
		}
	}()
	logger.Info("UpScaleService.Run 开始循环获取")
	for {
		value, err := d.PopRedisQueue()
		if err != nil {
			logger.Error("UpScaleService.Run PopRedisQueue to Retry:", value, err)
			continue
		}
		if value == "" {
			continue
		}
		logger.Info("接收到超分回传数据:", value)
		if err := d.UpdateToImage(value); err != nil {
			logger.Error(err)
		}
	}
}

func (d *upscale_v3) UpdateToImage(value string) error {

	outImgPath := ""
	outImgMd5 := ""
	mSdOutput := tools.GetMapFromJson(value)
	if val, ok := mSdOutput["error_info"]; ok && val.(string) != "" {
		mResult := tools.GetMapFromJson(val.(string))
		if path, exists := mResult["out_image_path"]; exists {
			outImgPath = path.(string)
		}
	}

	if val, ok := mSdOutput["result"]; ok && val.(string) != "" {
		mResult := tools.GetMapFromJson(val.(string))
		if path, exists := mResult["out_image_path"]; exists {
			outImgPath = path.(string)
		}
	} else {
		logger.Error(" key result  is not exist!", value)
	}

	if outImgPath != "" {

	}

	if md5, ok := mSdOutput["custom_md5"]; ok {
		outImgMd5 = md5.(string)
	}

	if outImgMd5 == "" {
		err := errors.New("path字段不为空")
		logger.Error(err, outImgMd5)
		return err
	}

	var resp upscaleOutData_
	if err := json.Unmarshal([]byte(value), &resp); err != nil {
		logger.Error("Json数据解析失败", value)
		return errors.New("解析数据失败")
	}

	customData := upscaleCustomData_{}
	if resp.CustomData != "" {
		if err := json.Unmarshal([]byte(resp.CustomData), &customData); err != nil {
			logger.Error(err)
			return err
		}
	}

	imgId := customData.ImgId
	level := customData.Level
	path := resp.SisPath
	if imgId == 0 || level == 0 || len(path) == 0 {
		logger.Error("Json解析出来的数据不正确", value, " ", imgId, " ", level, " ", path)
		return errors.New("解析出来的数据不正确")
	}
	diffImg := model.OutImg{}
	if err := diffImg.SetUpscalePath(imgId, level, path); err != nil {
		logger.Error("")
		return err
	}
	if err := d.RemoveRedisUpscaleState(imgId, level); err != nil {
		logger.Error(err)
	} else {
		logger.Info("移除RemoveRedisUpscaleState imgId:", imgId, " level:", level)
	}
	return nil
}

func (d *upscale_v3) GetUpScaleParameters(imgId uint, level int, upScaleW int, upScaleH int) (map[string]interface{}, error) {
	mapObj := make(map[string]interface{})

	//mapObj["custom_app"] = "roodesign"
	//mapObj["image_path"] = imgPath
	mapObj["upscaler_1"] = "Lanczos"
	mapObj["upscaler_2"] = "ESRGAN_4x"
	mapObj["extras_upscaler_2_visibility"] = 1
	mapObj["resize_mode"] = 0 //按比列缩放？
	mapObj["upscaling_resize"] = 4
	mapObj["upscaling_resize_w"] = upScaleW
	mapObj["upscaling_resize_h"] = upScaleH

	if mParameters, err := SdClient.GenUpscaleParameter(mapObj); err != nil {
		logger.Error(err)
		return nil, err
	} else {
		return mParameters, nil
	}
}

func (d *upscale_v3) PushRedisQueue(json string) (int64, error) { //Push到超分队列
	size, err := myredis.LPush(enums.AigcRedisKeyEnum.UpScalingIn, json)
	if err != nil {
		logger.Error(err)
	}
	return size, err
}

func (d *upscale_v3) PopRedisQueue() (string, error) { //获取超分回传数据
	value, err := myredis.BRPop(enums.AigcRedisKeyEnum.UpScalingOut)
	return value, err
}

func (d *upscale_v3) PushRedisUpscaleState(imgId uint, level int) error { //Push到超分队列
	field := fmt.Sprintf("%d_%d", imgId, level)
	value := time.Now().Format("2006-01-02 15:04:05")
	_, err := myredis.HSet(enums.AigcRedisKeyEnum.UpScalingState, field, value)
	if err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func (d *upscale_v3) GetRedisUpscaleState(imgId uint, level int) (time.Time, error) {
	field := fmt.Sprintf("%d_%d", imgId, level)

	value, err := myredis.HGet(enums.AigcRedisKeyEnum.UpScalingState, field)
	if err != nil && err.Error() != "redis: nil" {
		return time.Time{}, err
	}
	if value != "" {
		t, err := time.ParseInLocation("2006-01-02 15:04:05", value, time.Local)
		if err != nil {
			logger.Error(err)
			return time.Time{}, err
		}
		return t, nil
	}
	return time.Time{}, nil
}

func (d *upscale_v3) RemoveRedisUpscaleState(imgId uint, level int) error { //获取超分回传数据
	field := fmt.Sprintf("%d_%d", imgId, level)
	n, err := myredis.HDel(enums.AigcRedisKeyEnum.UpScalingState, field)
	if err != nil {
		logger.Error(err, n)
		return err
	}
	return nil
}

var UpScaleServiceV3 upscale_v3

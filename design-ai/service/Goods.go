package service

import (
	"design-ai/enums"
	"design-ai/model"
	"design-ai/utils/logger"
	"design-ai/utils/myredis"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
)

type ImgInfo struct {
	Width               int    `json:"width"`
	Height              int    `json:"height"`
	RelativePathFile    string `json:"relative_path_file"`
	RelativeOutPathFile string `json:"relative_out_path_file"`
	Md5                 string `json:"md5"`
	UserId              uint   `json:"user_id"`
	GoodId              uint   `json:"good_id"`
}

type goodUpscale_v2 struct {
}

func (d *goodUpscale_v2) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("goodUpScaleService奔溃:", e)
		}
	}()
	logger.Info("goodUpScaleService.Run 开始循环获取")
	for {
		value, err := d.PopRedisQueue()
		if err != nil {
			logger.Error("goodUpScaleService.Run PopRedisQueue to Retry:", value, err)
			continue
		}
		if value == "" {
			continue
		}
		logger.Info("goodUpScale接收到超分回传数据:", value)
		if err := d.UpdateToImage(value); err != nil {
			logger.Error(err)
		}
	}
}

type goodsOutData_ struct {
	CustomApp   string `json:"custom_app"`
	CustomData  string `json:"custom_data"`
	SisPath     string `json:"sis_path"`
	ExecuteTime int    `json:"execute_time"`
}

type goodsCustomData_ struct {
	Md5              string `json:"md5"`
	UserId           uint   `json:"user_id"`
	RelativePathFile string `json:"relative_path_file"`
	Width            int    `json:"width"`
	Height           int    `json:"height"`
	GoodId           uint   `json:"good_id"`
}

func (d *goodUpscale_v2) UpdateToImage(value string) error {
	var resp goodsOutData_
	if err := json.Unmarshal([]byte(value), &resp); err != nil {
		logger.Error("Json数据解析失败", value)
		return errors.New("解析数据失败")
	}

	customData := goodsCustomData_{}
	if resp.CustomData != "" {
		if err := json.Unmarshal([]byte(resp.CustomData), &customData); err != nil {
			logger.Error(err)
			return err
		}
	}

	md5 := customData.Md5
	userID := customData.UserId
	path := resp.SisPath
	inputPath := customData.RelativePathFile
	if len(md5) == 0 || len(path) == 0 || userID == 0 || len(inputPath) == 0 {
		logger.Error("Json解析出来的数据不正确", value, " ", md5, " ", path, " ", userID, " ", inputPath)
		return errors.New("解析出来的数据不正确")
	}
	outImg := model.GoodsOutImg{
		UserId:    userID,
		Md5:       md5,
		OutPath:   path,
		InputPath: inputPath,
		Width:     customData.Width,
		Height:    customData.Height,
		GoodId:    customData.GoodId,
	}
	if err := outImg.Save(); err != nil {
		logger.Error("生成图片保存失败")
		return err
	}
	// if err := d.RemoveRedisUpscaleState(imgId, level); err != nil {
	// 	logger.Error(err)
	// } else {
	// 	logger.Info("移除RemoveRedisUpscaleState imgId:", imgId, " level:", level)
	// }
	return nil
}

func (d *goodUpscale_v2) GetUpScaleJson(imgPath string, info ImgInfo, prompt string) (string, error) {
	mapObj := make(map[string]interface{})

	mapObj["custom_app"] = "goodshow"
	mapObj["model_name"] = "zeyun.ckpt"
	mapObj["model_hash"] = "461478dee8"
	mapObj["prompt"] = "(perfume bottle on a circular marble platform surrounded by thyme, in front of the amalfi coast), (RAW photo, best quality, high quality), (realistic, photo-realistic), (8k wallpaper), ultra-detailed, "
	// mapObj["upscaling_resize_w"] = info.Width * 2
	// mapObj["upscaling_resize_h"] = info.Height * 2
	mapObj["resize_mode"] = 1 //按比列缩放
	mapObj["custom_path"] = strings.Replace(info.RelativeOutPathFile, mapObj["custom_app"].(string)+"/", "", 1)
	mapObj["control_net_units"] = "{\"lineart_standard\":{\"weight\":1.0}}"
	mapObj["control_net_input_image_path"] = imgPath

	// mapObj["control_net_input_image_path"] = imgPath

	bytesCustomData, err := json.Marshal(info)
	if err != nil {
		logger.Error("json.Marshal(m) error:", err)
		return "", err
	}
	mapObj["custom_data"] = string(bytesCustomData)

	bytesData, err := json.Marshal(mapObj)
	if err != nil {
		logger.Error("json.Marshal(m) error:", err)
		return "", err
	}
	return string(bytesData), nil
}

func (d *goodUpscale_v2) PushRedisQueue(json string) (int64, error) { //Push到生图队列
	size, err := myredis.LPush(enums.AigcRedisKeyEnum.GoodsPush, json)
	if err != nil {
		logger.Error(err)
	}
	return size, err
}

func (d *goodUpscale_v2) PopRedisQueue() (string, error) { //获取超分回传数据
	value, err := myredis.BRPop(enums.AigcRedisKeyEnum.GoodsPop)
	return value, err
}

func (d *goodUpscale_v2) PushRedisUpscaleState(imgId uint, level int) error { //Push到超分队列
	field := fmt.Sprintf("%d_%d", imgId, level)
	value := time.Now().Format("2006-01-02 15:04:05")
	_, err := myredis.HSet(enums.AigcRedisKeyEnum.GoodsUpScalingState, field, value)
	if err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func (d *goodUpscale_v2) GetRedisUpscaleState(imgId uint, level int) (time.Time, error) {
	field := fmt.Sprintf("%d_%d", imgId, level)

	value, err := myredis.HGet(enums.AigcRedisKeyEnum.GoodsUpScalingState, field)
	if err != nil && err.Error() != "redis: nil" {
		return time.Time{}, err
	}
	if value != "" {
		t, err := time.ParseInLocation("2006-01-02 15:04:05", value, time.Local)
		if err != nil {
			logger.Error(err)
			return time.Time{}, err
		}
		return t, nil
	}
	return time.Time{}, nil
}

func (d *goodUpscale_v2) RemoveRedisUpscaleState(imgId uint, level int) error { //获取超分回传数据
	field := fmt.Sprintf("%d_%d", imgId, level)
	n, err := myredis.HDel(enums.AigcRedisKeyEnum.GoodsUpScalingState, field)
	if err != nil {
		logger.Error(err, n)
		return err
	}
	return nil
}

var GoodUpScaleServiceV2 goodUpscale_v2

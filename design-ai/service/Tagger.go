package service

import (
	"design-ai/enums"
	"design-ai/utils/config"
	"design-ai/utils/logger"
	"design-ai/utils/myhttp"
	"design-ai/utils/tools"
	"errors"
	"strings"
)

type Tagger struct {
	Success bool          `json:"success"`
	Caption TaggerCaption `json:"caption"`
}

type TaggerCaption struct {
	Tag    map[string]float64 `json:"tag"`
	Rating map[string]float64 `json:"rating"`
}

func (d *Tagger) Interrogate(imageBase64 string) error {

	parameters := make(map[string]interface{})
	parameters["image"] = imageBase64
	parameters["model"] = "wd14-vit.v2"
	parameters["threshold"] = 0.3
	//oReq := service.SdInput{
	//	SdServer:   "https://96c68c4c160447f0823061a9e52fe48388.hz01.suanyun.cn/",
	//	Parameters: tools.GetJsonFromStruct(parameters),
	//}

	sdInput := make(map[string]interface{})
	sdInput["parameters"] = tools.GetJsonFromStruct(parameters)
	sdInput["sd_server"] = "http://192.168.200.185:10088/"

	url := "http://192.168.200.4:5206/sdapi/sd_tagger"
	if config.Env == enums.EnvEnum.ONLINE {
		url = "http://192.168.200.4:5207/sdapi/sd_tagger"
	}
	if ss, err := myhttp.Post(url, sdInput); err != nil {
		logger.Error(err)
		return err
	} else {
		mm := tools.GetMapFromJson(ss)
		if _, ok := mm["caption"]; ok {
			mmCaption := mm["caption"].(map[string]interface{})
			strCaptioin := tools.GetJsonFromStruct(mmCaption)
			var caption TaggerCaption
			if err := tools.GetStructFromJson(&caption, strCaptioin); err != nil {
				logger.Error(err)
				return err
			} else {
				d.Caption = caption
				d.Success = true
				return nil
			}
		} else {
			logger.Error("caption not exists ss:", ss)
			return errors.New("caption not exists")
		}
	}
}

func (d *Tagger) Load(json string) error {
	mm := tools.GetMapFromJson(json)
	if _, ok := mm["caption"]; ok {
		mmCaption := mm["caption"].(map[string]interface{})
		strCaptioin := tools.GetJsonFromStruct(mmCaption)
		var caption TaggerCaption
		if err := tools.GetStructFromJson(&caption, strCaptioin); err != nil {
			logger.Error(err)
			return err
		} else {
			d.Caption = caption
			d.Success = true
			return nil
		}
	} else {
		logger.Error("caption not exists ss:", json)
		return errors.New("caption not exists")
	}
}

func (d *Tagger) IsLineArt() bool {

	//greyscale灰度的,  monochrome单色  lineart
	num := 0
	caption := d.Caption
	if val, ok := caption.Tag["greyscale"]; ok {
		if val >= 0.3 {
			num += 1
		}
	}
	if val, ok := caption.Tag["monochrome"]; ok {
		if val >= 0.3 {
			num += 1
		}
	}
	if val, ok := caption.Tag["lineart"]; ok {
		if val >= 0.3 {
			return true
		}
	}
	if num == 2 {
		return true
	}

	return false
}

func (d *Tagger) IsGeneral() bool { //一般（正常）的
	if val, ok := d.Caption.Rating["general"]; ok {
		if val >= 0.6 {
			return true
		}
	}
	return false
}

func (d *Tagger) IsExplicit() bool { //露骨（色情）的
	if val, ok := d.Caption.Rating["explicit"]; ok {
		if val >= 0.6 {
			return true
		}
	}
	return false
}

func (d *Tagger) IsQuestionable() bool { //可疑（有争议）的
	if val, ok := d.Caption.Rating["questionable"]; ok {
		if val >= 0.6 {
			return true
		}
	}
	return false
}

func (d *Tagger) IsSensitive() bool { //敏感内容的
	if val, ok := d.Caption.Rating["sensitive"]; ok {
		if val >= 0.6 {
			return true
		}
	}
	return false
}

func (d *Tagger) Tags() string {
	aryTag := make([]string, 0)
	for key, value := range d.Caption.Tag {
		if value >= 0.3 {
			aryTag = append(aryTag, key)
		}
	}
	return strings.Join(aryTag, ",")
}

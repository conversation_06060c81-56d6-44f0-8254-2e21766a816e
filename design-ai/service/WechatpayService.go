package service

import (
	"context"
	"design-ai/utils/config"
	"design-ai/utils/logger"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/wechat/v3"
	"github.com/shopspring/decimal"
	"time"
)

type wechatpayService_ struct {
	ctx                        context.Context
	client                     *wechat.ClientV3
	appId                      string
	mchId                      string
	mchCertificateSerialNumber string
	mchAPIv3Key                string
	privateKeyContent          string
}

func (o *wechatpayService_) setUp() {
	if o.client == nil {
		o.ctx = context.Background()
		o.appId = "wx539d95b01ee28d2e"
		o.mchId = "1635958245"
		o.mchCertificateSerialNumber = "180AA7D0285165A8299952C92F9F9C28B0B39C25"
		o.mchAPIv3Key = "cyuai888888888888888888888888888"
		o.privateKeyContent =
			`
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

		//path := "./config/apiclient_key.pem"
		//certPath := "./config/apiclient_cert.pem"
		//_, err := os.Stat(certPath)
		//if err != nil {
		//	path = "/Users/<USER>/Downloads/1635958245_20221212_cert/apiclient_key.pem"
		//	certPath = "/Users/<USER>/Downloads/1635958245_20221212_cert/apiclient_cert.pem"
		//}
		//content, err := ioutil.ReadFile(certPath)
		//if err != nil {
		//	logger.Error(err, path)
		//}
		//logger.Info(content)

		client, e := wechat.NewClientV3(o.mchId, o.mchCertificateSerialNumber, o.mchAPIv3Key, o.privateKeyContent)
		if e != nil {
			logger.Error(e)
			return
		}
		o.client = client
		// 设置微信平台证书和序列号，如开启自动验签，请忽略此步骤
		//client.SetPlatformCert([]byte(""), "")

		// 启用自动同步返回验签，并定时更新微信平台API证书
		if err := o.client.AutoVerifySign(); err != nil {
			logger.Error(err)
			return
		}

		// 打开Debug开关，输出日志
		o.client.DebugSwitch = gopay.DebugOn
	}
}
func (o *wechatpayService_) Init() {
	o.setUp()
}

func (o *wechatpayService_) PaySignOfApp(prepayid string) (app *wechat.AppPayParams, err error) {
	o.setUp()
	app, err = o.client.PaySignOfApp(o.appId, prepayid)
	if err != nil {
		logger.Error(err)
	}
	return app, nil
}

/*
func (o *wechatpayService_) PaySignOfApp11(prepayid, nonceStr string) (app *wechat.AppPayParams, err error) {
	ts := util.Int642String(time.Now().Unix())

	_str := o.appId + "\n" + ts + "\n" + nonceStr + "\n" + prepayid + "\n"

	h := sha256.New()
	h.Write([]byte(_str))

	priKey, err := xpem.DecodePrivateKey([]byte(o.privateKeyContent))
	result, err := rsa.SignPKCS1v15(rand.Reader, priKey, crypto.SHA256, h.Sum(nil))
	if err != nil {
		logger.Error(err)
	}
	sign := base64.StdEncoding.EncodeToString(result)

	//sign, err := o.client.rsaSign(_str)
	//if err != nil {
	//	return nil, err
	//}

	app = &wechat.AppPayParams{
		Appid:     o.appId,
		Partnerid: o.mchId,
		Prepayid:  prepayid,
		Package:   "Sign=WXPay",
		Noncestr:  nonceStr,
		Timestamp: ts,
		Sign:      sign,
	}
	return app, nil
}

func (o *wechatpayService_) GeneratePaymentSign(noncestr, prepayid, timestamp string) string {
	//timestamp := fmt.Sprintf("%s", time.Now().Unix())
	sb := strings.Builder{}
	sb.WriteString("appid=" + o.appId)
	sb.WriteString("&noncestr=" + noncestr)
	sb.WriteString("&package=" + "Sign=WXPay")
	sb.WriteString("&partnerid=" + o.mchId)
	sb.WriteString("&prepayid=" + prepayid)
	sb.WriteString("&timestamp=" + timestamp)
	sb.WriteString("&key=" + o.mchAPIv3Key)

	strToHash := sb.String()
	logger.Info("微信支付需要md5的字符串:", strToHash)
	hash := md5.Sum([]byte(strToHash))
	sign := hex.EncodeToString(hash[:])
	logger.Info("微信支付md5:", strings.ToUpper(sign))
	return strings.ToUpper(sign)
}


func (o *wechatpayService_) GetSign(m map[string]interface{}) string {
	o.setUp()
	var (
		str  string
		keys []string
	)

	// 遍历参数，提取key
	for k := range m {
		if k != "sign" {
			keys = append(keys, k)
		}
	}

	// 参数按照key排序
	sort.Strings(keys)

	// 拼接参数
	for _, k := range keys {
		v := m[k]
		if v != nil && strings.TrimSpace(v.(string)) != "" {
			str += k + "=" + v.(string) + "&"
		}
	}

	// 拼接API密钥
	str += "key=" + o.mchAPIv3Key

	// 计算MD5签名
	h := md5.New()
	h.Write([]byte(str))
	return strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
}*/

func (o *wechatpayService_) V3DecryptNotifyCipherText(ciphertext string, nonce string, additional string) (result *wechat.V3DecryptResult, err error) {
	o.setUp()
	return wechat.V3DecryptNotifyCipherText(ciphertext, nonce, additional, o.mchAPIv3Key)
}

func (o *wechatpayService_) TradeQueryByOutTradeNo(outTradeNo string) (wxRsp *wechat.QueryOrderRsp, err error) {
	o.setUp()
	return o.client.V3TransactionQueryOrder(o.ctx, wechat.OutTradeNo, outTradeNo)
}

func (o *wechatpayService_) TradePayJsapi(openId string, outTradeNo string, amount decimal.Decimal, desc string) (*wechat.PrepayRsp, error) {
	o.setUp()
	oneHundred, _ := decimal.NewFromString("100")
	amountFen := amount.Mul(oneHundred)
	amountInt := amountFen.BigInt().Int64()

	expire := time.Now().Add(10 * time.Minute).Format(time.RFC3339)
	// 初始化 BodyMap
	bm := make(gopay.BodyMap)
	bm.Set("sp_appid", o.appId).
		Set("sp_mchid", o.mchId).
		Set("sub_mchid", o.mchId).
		Set("description", desc).
		Set("out_trade_no", outTradeNo).
		Set("time_expire", expire).
		Set("notify_url", config.Domain+"api/v1/wechatpay/notify").
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", amountInt).
				Set("currency", "CNY")
		}).
		SetBodyMap("payer", func(bm gopay.BodyMap) {
			bm.Set("sp_openid", openId)
		})
	logger.Info(bm.JsonBody())
	// 请求支付下单，成功后得到结果
	wxRsp, err := o.client.V3TransactionJsapi(o.ctx, bm)
	if err != nil {
		logger.Info("client.V3TransactionH5(%+v),error:%+v", bm, err)
		return wxRsp, err
	}
	return wxRsp, err
}

func (o *wechatpayService_) TradePayApp(outTradeNo string, amount decimal.Decimal, desc string) (*wechat.PrepayRsp, error) {
	o.setUp()
	oneHundred, _ := decimal.NewFromString("100")
	amountFen := amount.Mul(oneHundred)
	amountInt := amountFen.BigInt().Int64()
	//{"code":"PARAM_ERROR","detail":{"location":"body","value":""},"message":"输入源“/body/appid”映射到字段“公众号ID”必填性规则校验失败，此字段为必填项"}
	expire := time.Now().Add(10 * time.Minute).Format(time.RFC3339)
	// 初始化 BodyMap
	bm := make(gopay.BodyMap)
	bm.Set("appid", o.appId).
		//Set("sp_mchid", o.mchId).
		Set("mchid", o.mchId).
		Set("description", desc).
		Set("out_trade_no", outTradeNo).
		Set("time_expire", expire).
		Set("notify_url", "https://design.cyuai.com/api/v1/wechatpay/notify").
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", amountInt).
				Set("currency", "CNY")
		})

	//logger.Info(bm.JsonBody())
	//o.client.V3PartnerTransactionNative()
	//o.client.V3PartnerTransactionAppRequest
	//o.client.V3PartnerTransactionApp()
	// 请求支付下单，成功后得到结果
	//wxRsp, err := o.client.V3TransactionJsapi(o.ctx, bm)
	wxRsp, err := o.client.V3TransactionApp(o.ctx, bm)
	if err != nil {
		logger.Error("client.V3TransactionH5(%+v),error:%+v", bm, err)
		return wxRsp, err
	}
	return wxRsp, err
}

//用于生成微信支付的 Native 支付码。Native 支付是指用户通过扫描商户生成的二维码来完成支付的一种方式，一般用于实体店、餐饮店等线下场景。
func (o *wechatpayService_) TradePayNative(outTradeNo string, amount decimal.Decimal, desc string) (*wechat.NativeRsp, error) {
	o.setUp()
	oneHundred, _ := decimal.NewFromString("100")
	amountFen := amount.Mul(oneHundred)
	amountInt := amountFen.BigInt().Int64()

	expire := time.Now().Add(10 * time.Minute).Format(time.RFC3339)
	// 初始化参数Map
	bm := make(gopay.BodyMap)
	bm.Set("appid", o.appId).
		//Set("sp_mchid", o.mchId).
		//Set("sub_mchid", o.mchId).
		Set("description", desc).
		Set("out_trade_no", outTradeNo).
		Set("time_expire", expire).
		//Set("notify_url", config.Domain+"api/v1/wechatpay/notify").
		Set("notify_url", "https://design.cyuai.com/api/v1/wechatpay/notify").
		SetBodyMap("amount", func(b gopay.BodyMap) {
			b.Set("total", amountInt).
				Set("currency", "CNY")
		})

	//logger.Info(bm.JsonBody())
	// 请求支付下单，成功后得到结果
	wxRsp, err := o.client.V3TransactionNative(o.ctx, bm)
	if err != nil {
		logger.Error("TradePayNative", bm, err)
		return wxRsp, err
	}
	return wxRsp, err
}

func (o *wechatpayService_) TradePayH5(outTradeNo string, amount decimal.Decimal, desc string) (*wechat.H5Rsp, error) {
	o.setUp()
	oneHundred, _ := decimal.NewFromString("100")
	amountFen := amount.Mul(oneHundred)
	amountInt := amountFen.BigInt().Int64()

	//expire := time.Now().Add(10 * time.Minute).Format(time.RFC3339)
	// 初始化参数Map
	bm := make(gopay.BodyMap)
	bm.Set("appid", o.appId).
		Set("mchid", o.mchId).
		//Set("sub_mchid", o.mchId).
		Set("description", desc).
		Set("out_trade_no", outTradeNo).
		//Set("time_expire", expire).
		Set("notify_url", "https://design.cyuai.com/api/v1/wechatpay/notify").
		//Set("redirect_url", "https://design.cyuai.com/api_online/v1/wechatpay/notify").
		SetBodyMap("amount", func(b gopay.BodyMap) {
			b.Set("total", amountInt).
				Set("currency", "CNY")
		}).
		SetBodyMap("scene_info", func(b gopay.BodyMap) {
			b.Set("payer_client_ip", "************").
				Set("device_id", "").
				SetBodyMap("h5_info", func(b gopay.BodyMap) {
					b.Set("type", "Wap").
						Set("app_name", "AI效果图").
						Set("app_url", "https://design.cyuai.com").
						Set("bundle_id", "design.cyuai.aigc")
				})
		})
	logger.Info(bm.JsonBody())
	// 请求支付下单，成功后得到结果
	wxRsp, err := o.client.V3TransactionH5(o.ctx, bm)
	if err != nil {
		logger.Info("client.V3TransactionH5(%+v),error:%+v", bm, err)
		return wxRsp, err
	}
	return wxRsp, err
}

var WechatpayService wechatpayService_

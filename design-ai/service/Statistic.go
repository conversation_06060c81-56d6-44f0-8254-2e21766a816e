package service

import (
	"design-ai/model"
	"design-ai/utils/logger"
	"errors"
	"time"
)

type statisticSerivce struct {
}

func (s *statisticSerivce) StatisticAll(start time.Time, end time.Time) (model.Statistic, error) {
	var statistic model.Statistic

	//统计注册用户
	var user model.User
	userTotal, err := user.StatisticUser(start, end)
	if err != nil {
		logger.Error(err)
		return statistic, errors.New("用户数量统计失败")

	}
	//统计付款充值笔数
	var recharge model.Recharge
	rechargeTotal, amount, err := recharge.StatisticRecharge(start, end)
	if err != nil {
		logger.Error(err)
		return statistic, errors.New("充值次数统计失败")

	}

	//统计设计
	var design model.Design
	designTotal, err := design.StatisticDesign(start, end)
	if err != nil {
		logger.Error(err)
		return statistic, errors.New("设计次数统计失败")

	}

	//统计出图
	var outImg model.OutImg
	outImgTotal, err := outImg.StatisticOutImg(start, end)
	if err != nil {
		logger.Error(err)
		return statistic, errors.New("出图数量统计失败")
	}

	staDay := start.Format("2006-01-01")
	if err := statistic.GetByStaDay(staDay); err != nil {
		logger.Error(err, "无法获取当日统计")
		statistic = model.Statistic{
			StaDay:         staDay,
			RegCount:       int(userTotal),
			RechargeCount:  int(rechargeTotal),
			RechargeAmount: amount,
			DesignCount:    int(designTotal),
			OutImgCount:    int(outImgTotal),
		}
	} else {
		statistic.StaDay = staDay
		statistic.RegCount = int(userTotal)
		statistic.RechargeCount = int(rechargeTotal)
		statistic.RechargeAmount = amount
		statistic.DesignCount = int(designTotal)
		statistic.OutImgCount = int(outImgTotal)
	}
	if err := statistic.Save(); err != nil {
		logger.Error(err)
		return statistic, errors.New("统计生成失败")
	}
	return statistic, nil
}

var StatisticSerivce statisticSerivce

package model

import (
	"design-ai/enums"
	"design-ai/utils/logger"
	"errors"
	"time"

	"gorm.io/gorm"
)

type CoinBalanceRes struct {
	ID             uint      `json:"ID"`
	CreateAt       time.Time `json:"create_at"`
	UserId         uint      `json:"user_id"`
	OrderType      string    `json:"order_type"`
	OrderNo        string    `json:"order_no"`
	BeforeOccurred int       `json:"before_occurred"`
	OccurredAmount int       `json:"occurred_amount"`
	AfterOccurred  int       `json:"after_occurred"`
	Show           string    `json:"show"`
	Remark         string    `json:"remark"`
	Operator       string    `json:"operator"`
	OperatorId     uint      `json:"operator_id"`
}

type CoinBalance struct {
	gorm.Model
	UserId         uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	OrderType      int    `json:"order_type" gorm:"type:int;not null;default:0;comment:业务类型"`
	OrderNo        string `json:"order_no" gorm:"type:varchar(50);not null;default:'';comment:业务单号"`
	BeforeOccurred int    `json:"before_occurred" gorm:"type:int;not null;default:0;comment:变动前额度"`
	OccurredAmount int    `json:"occurred_amount" gorm:"type:int;not null;default:0;comment:发生额"`
	AfterOccurred  int    `json:"after_occurred" gorm:"type:int;not null;default:0;comment:变动后额度"`
	Show           string `json:"show" gorm:"type:varchar(50);not null;default:'';comment:前端显示文本"`
	Remark         string `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	Operator       string `json:"operator" gorm:"type:varchar(50);not null;default:'';comment:操作者"`
	OperatorId     uint   `json:"operator_id" gorm:"type:bigint;not null;default:0;comment:操作者ID"`
}

func (CoinBalance) TableName() string {
	return "T_CoinBalance"
}

func (balance *CoinBalance) GetList(dest interface{}, userId uint, kw string, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(balance).Where("user_id=?", userId)
	if kw != "" {
		tx.Where("remark like ?", "%"+kw+"%")
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (balance *CoinBalance) GetListByManager(userId uint, page int, pageSize int) ([]CoinBalanceRes, int64, error) {
	var total int64
	resArr := make([]CoinBalanceRes, 0)
	arr := make([]CoinBalance, 0)
	tx := db.Debug().Model(balance).Where("1=1")
	if userId > 0 {
		tx.Where("user_id=? ", userId)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return nil, 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(&arr)

	for _, item := range arr {
		resArr = append(resArr, CoinBalanceRes{
			ID:             item.ID,
			CreateAt:       item.CreatedAt,
			UserId:         item.UserId,
			OrderType:      enums.OrderTypeMap[item.OrderType],
			OrderNo:        item.OrderNo,
			BeforeOccurred: item.BeforeOccurred,
			OccurredAmount: item.OccurredAmount,
			AfterOccurred:  item.AfterOccurred,
			Show:           item.Show,
			Remark:         item.Remark,
			Operator:       item.Operator,
			OperatorId:     item.OperatorId,
		})
	}
	return resArr, total, tx.Error
}

//func (o *CoinBalance) GetList(userId uint, kw string,  page int, pageSize int) ([]CoinBalance, int64, error) {
//	ary := make([]CoinBalance, 0)
//	var total int64
//	tx := db.Debug().Model(o).Where("user_id=? ", userId)
//	if userId > 0 {
//		tx.Where("user_id=?", userId)
//	}
//	if origId > 0 {
//		tx.Where("orig_id=?", origId)
//	}
//	if roomType > 0 {
//		tx.Where("room_type=?", roomType)
//	}
//	if roomStyle > 0 {
//		tx.Where("room_style=?", roomStyle)
//	}
//	if page == 1 {
//		if err = tx.Count(&total).Error; err != nil {
//			return nil, 0, err
//		}
//	}
//	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(&ary)
//	return ary, total, tx.Error
//}

func (balance *CoinBalance) New(tx *gorm.DB) error {

	var user User
	if err := db.First(&user, balance.UserId).Error; err != nil || user.ID == 0 {
		logger.Error(" orderNo:", balance.OrderNo, " balance.OccurredAmount:", balance.OccurredAmount, " userID:", user.ID)
		return err
	}

	if err := balance.checkBalance(); err != nil {
		return err
	}

	after := user.Coin + balance.OccurredAmount
	balance.BeforeOccurred = user.Coin
	balance.AfterOccurred = after

	if err := tx.Model(&user).Updates(map[string]interface{}{"coin": after}).Error; err != nil {
		return err
	}

	if err := tx.Save(balance).Error; err != nil {
		return err
	}
	return nil
}

func (balance *CoinBalance) GetBalanceObject(orderNo string, userId uint, orderType int, occurred int, show string, remark string, operatorId uint, operator string) error {
	balance.OrderNo = orderNo
	balance.UserId = userId
	balance.OrderType = orderType
	balance.Show = show
	balance.Remark = remark
	balance.Operator = operator
	balance.OperatorId = operatorId
	balance.OccurredAmount = occurred
	err := balance.checkBalance()
	if err != nil {
		return err
	}
	return nil
}

func (balance *CoinBalance) checkBalance() error {
	if len(balance.OrderNo) == 0 {
		return errors.New("单号不正确")
	}
	if balance.UserId == 0 {
		return errors.New("未找到用户")
	}
	if len(balance.OrderNo) == 0 {
		return errors.New("业务单号为空")
	}
	if balance.OccurredAmount == 0 {
		return errors.New("没有发生金额")
	}
	if balance.OrderType == 0 {
		return errors.New("没有业务类型")
	}
	if balance.OrderType == enums.OrderTypeEnum.ManagerAdd || balance.OrderType == enums.OrderTypeEnum.RechargeBuy || balance.OrderType == enums.OrderTypeEnum.UserGift || balance.OrderType == enums.OrderTypeEnum.Subscribe {
		if balance.OccurredAmount <= 0 {
			return errors.New("金额与业务类型不匹配")
		}
	}
	if balance.OrderType == enums.OrderTypeEnum.Cost || balance.OrderType == enums.OrderTypeEnum.ManagerCost {
		if balance.OccurredAmount >= 0 {
			return errors.New("金额与业务类型不匹配")
		}
	}
	return nil
}

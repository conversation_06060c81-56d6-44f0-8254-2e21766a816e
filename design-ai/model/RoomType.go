package model

import (
	"gorm.io/gorm"
)

type RoomType struct {
	gorm.Model
	RefImg     string `json:"ref_img" gorm:"type:varchar(200);not null;default:'';comment:风格图片"`
	Title      string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	Keys       string `json:"keys" gorm:"type:varchar(200);not null;default:'';comment:lora匹配关键字 用英文逗号隔开"`
	Remark     string `json:"remark" gorm:"type:varchar(100);not null;default:'';comment:后台备注"`
	OrderIndex int    `json:"order_index" gorm:"type:int;not null;default:0;comment:序号(越小越前面)"`
	State      int    `json:"state" gorm:"type:int;not null;default:0;comment:状态(1可用)"`
}

func (RoomType) TableName() string {
	return "T_RoomType"
}

func (o *RoomType) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *RoomType) GetList(dest interface{}, state int, page int, pageSize int) error {
	tx := db.Debug().Model(o).Where("state=?", state).Order("order_index asc").Scan(dest)
	return tx.Error
}

func (o *RoomType) Save() error {
	return db.Save(o).Error
}

func (o *RoomType) SetState(state int) error {
	return db.Model(o).Updates(RoomType{State: state}).Error
}

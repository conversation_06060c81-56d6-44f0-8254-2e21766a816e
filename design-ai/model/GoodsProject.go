package model

import (
	"gorm.io/gorm"
)

type GoodsProject struct {
	gorm.Model
	Name        string `json:"name" gorm:"type:varchar(50);not null;default:'';comment:项目名称"`
	UserId      uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	Description string `json:"description" gorm:"type:varchar(100);not null;default:'';comment:产品描述"`
	Uuid        string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:唯一字符串"`
	Scene       string `json:"scene" gorm:"type:json;comment:场景数据" `
	RefImg      string `json:"ref_img" gorm:"type:varchar(100);not null;default:'';comment:项目封面"`
}

func (GoodsProject) TableName() string {
	return "T_GoodsProject"
}

func (o *GoodsProject) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *GoodsProject) GetByUuid(uuid string) error {
	err := db.Debug().Where("uuid=?", uuid).First(o).Error
	return err
}

func (o *GoodsProject) GetList(dest interface{}, userId uint, page int, pageSize int) (int64, error) {
	tx := db.Debug().Model(o).Where("user_id = ?", userId)
	var total int64
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *GoodsProject) Save() error {
	return db.Save(o).Error
}

func (o *GoodsProject) Del() error {
	err := db.Debug().Delete(o).Error
	return err
}

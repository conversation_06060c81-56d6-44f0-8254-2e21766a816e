package manage

import (
	"design-ai/enums"
	"design-ai/middleware"
	"design-ai/model"
	"design-ai/utils/errmsg"
	"design-ai/utils/logger"
	"design-ai/utils/tools"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"net/http"
	"time"
)

type redeemApi_ struct {
}

type redeemReq struct {
	MemberCardCount  int    `json:"member_card_count"`
	RechargeBagCount int    `json:"recharge_bag_count"`
	AgentUserId      uint   `json:"agent_user_id"`
	AgentMobile      string `json:"agent_mobile"`
	Operator         string `json:"operator"`
	SmsCode          string `json:"sms_code"`
	PaymentInfo      string `json:"payment_info"`
}

type exchangeReq struct {
	RedeemCode string `json:"redeem_code"`
}
type redeemQueryReq struct {
	GroupId        uint   `json:"group_id"`
	AgentUserId    uint   `json:"agent_user_id"`
	ExchangeUserId uint   `json:"exchange_user_id"`
	RedeemCode     string `json:"redeem_code"`
	State          int    `json:"state"`
	Page           int    `json:"page"`
	PageSize       int    `json:"page_size"`
}

type redeemItemResp struct {
	GroupId        uint            `json:"group_id" gorm:"type:bigint;not null;default:0;comment:生成组ID"`
	GroupName      string          `json:"group_name" gorm:"type:varchar(50);not null;default:'';comment:兑换码生成的批次名称"`
	RedeemCode     string          `json:"redeem_code" gorm:"type:varchar(100);not null;default:'';comment:兑换码"`
	RedeemType     int             `json:"redeem_type" gorm:"type:int;not null;default:0;comment:兑换类型 1高级会员 2充值包"`
	Coin           int             `json:"coin" gorm:"type:int;not null;default:0;comment:可以增加的星星数量"`
	Price          decimal.Decimal `json:"amount" gorm:"type:decimal(16,2);not null;default:0;comment:零售价格"`
	AgentUserId    uint            `json:"agent_user_id" gorm:"type:bigint;not null;default:0;comment:代理商用户ID"`
	PayTime        time.Time       `json:"pay_time" gorm:"type:datetime;default:'1900-01-01';comment:用户购买时间"`
	ExchangeUserId uint            `json:"exchange_user_id" gorm:"type:bigint;not null;default:0;comment:兑换人用户ID"`
	ExchangeTime   time.Time       `json:"exchange_time" gorm:"type:datetime;default:'1900-01-01';comment:用户兑换时间"`
	Remark         string          `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注(由管理员添加)"`
	AgentRemark    string          `json:"agent_remark" gorm:"type:varchar(50);not null;default:'';comment:备注(由代理商添加)"`
	OrderNo        string          `json:"order_no" gorm:"type:varchar(50);comment:兑换订单编号"`
	State          int             `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态：0待生效 1生效待售(已经在代理商名下) 2已出售 3已兑换 4已收回"`
}

func (obj redeemApi_) RedeemList(c *gin.Context) {
	var code int
	var msg string
	var oReq redeemQueryReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	er := c.ShouldBindJSON(&oReq)
	if er != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if oReq.PageSize < 1 {
		oReq.PageSize = 1
	}
	if oReq.Page < 1 {
		oReq.Page = 1
	}

	var redeem model.Redeem
	ary := make([]redeemItemResp, 0)
	res, total, err := redeem.GetListByManager(oReq.GroupId, oReq.AgentUserId, oReq.ExchangeUserId, oReq.RedeemCode, oReq.State, oReq.Page, oReq.PageSize)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询出错")
		return
	}
	for _, item := range res {
		tmp := redeemItemResp{
			GroupId:        item.GroupId,
			GroupName:      item.GroupName,
			RedeemCode:     item.RedeemCode,
			RedeemType:     item.RedeemType,
			Coin:           item.Coin,
			Price:          item.Price,
			AgentUserId:    item.AgentUserId,
			PayTime:        item.PayTime,
			ExchangeUserId: item.ExchangeUserId,
			ExchangeTime:   item.ExchangeTime,
			Remark:         item.Remark,
			AgentRemark:    item.AgentRemark,
			OrderNo:        item.OrderNo,
			State:          item.State,
		}
		ary = append(ary, tmp)
	}

	result := make(map[string]interface{})
	result["items"] = ary
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj redeemApi_) Gen(c *gin.Context) {
	var code int
	var msg string
	var oReq redeemReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if claims.UserId != 1 {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	if err := c.ShouldBindJSON(&oReq); err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if oReq.AgentUserId <= 0 || !tools.IsMobile(oReq.AgentMobile) {
		logger.Error("请正确输入代理商信息:", oReq)
		errmsg.Abort(c, errmsg.FAIL, "请正确输入代理商信息")
		return
	}

	if oReq.MemberCardCount <= 0 && oReq.RechargeBagCount <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请正确输入正确的数量")
		return
	}

	var agentUser model.User
	if err := agentUser.GetByID(oReq.AgentUserId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "用户ID不正确")
		return
	}
	if agentUser.Mobile != oReq.AgentMobile {
		errmsg.Abort(c, errmsg.FAIL, "用户ID 代理商手机号码不匹配")
		return
	}
	if oReq.Operator == "" {
		errmsg.Abort(c, errmsg.FAIL, "请输入操作员")
		return
	}

	groupName := fmt.Sprintf("%s-%d-%s", time.Now().Format("20060102150405"), oReq.AgentUserId, oReq.AgentMobile[len(oReq.AgentMobile)-4:])
	if err := model.Transactions.GenRedeem(groupName, oReq.MemberCardCount, oReq.RechargeBagCount, oReq.AgentUserId, claims.UserId, oReq.Operator, oReq.PaymentInfo); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "生成兑换码失败")
		return
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  "兑换码生成成功",
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

var RedeemApi redeemApi_

package manage

import (
	"design-ai/model"
	"design-ai/utils/config"
	"design-ai/utils/errmsg"
	"fmt"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
)

func DownloadImg(c *gin.Context) {
	md5 := c.Param("md5")
	tp := c.Param("tp")
	// claims := c.Value("claims").(*middleware.MyClaims)
	// if claims.UserId <= 0 {
	// 	errmsg.Abort(c, errmsg.FAIL, "请先登录")
	// 	return
	// }
	var filePath string
	if tp == "input" {
		var design model.Design
		if err := design.GetByMd5(md5); err != nil {
			errmsg.Abort(c, errmsg.FAIL, "找不到该文件")
			return
		}

		filePath = design.InputImgPath
	} else if tp == "output" {
		var output model.OutImg
		if err := output.GetByMd5(md5); err != nil {
			errmsg.Abort(c, errmsg.FAIL, "找不到该文件")
			return
		}

		filePath = output.Path
	} else if tp == "tmp" {
		var tmp model.TempImg
		if err := tmp.GetByMd5(md5); err != nil {
			errmsg.Abort(c, errmsg.FAIL, "找不到该文件")
			return
		}

		filePath = tmp.Path
	}

	if _, errByOpenFile := os.Open(config.DiffusionFilePath + filePath); errByOpenFile != nil {
		errmsg.Abort(c, errmsg.FAIL, "无法找到该文件"+config.DiffusionFilePath+filePath)
		return
	}
	download := c.Param("download")
	if download != "0" {
		filePathArr := strings.Split(filePath, ".")
		filename := filePathArr[len(filePathArr)-1]
		c.Header("Content-Type", "application/octet-stream")
		c.Header("Content-Disposition", "attachment; filename="+filename)
		c.Header("Content-Transfer-Encoding", "binary")
	}
	c.File(config.DiffusionFilePath + filePath)
}

func GetImgByPath(c *gin.Context) {
	path := c.Query("path")
	fmt.Print("path>>>>>" + path)
	c.File(config.DiffusionFilePath + path)
}

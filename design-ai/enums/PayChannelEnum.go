package enums

import "reflect"

type payChannelEnum_ struct {
	AliPay, WechatPay, EnterpriseNetPay, AppleIap, OtherTransfer, BackRecharge string
}

var PayChannelEnum = payChannelEnum_{
	AliPay:           "0",
	WechatPay:        "1",
	EnterpriseNetPay: "2",
	AppleIap:         "3",
	OtherTransfer:    "8",
	BackRecharge:     "9",
}

var PayChannelNameEnum = payChannelEnum_{
	AliPay:           "支付宝",
	WechatPay:        "微信支付",
	AppleIap:         "IOS iap支付",
	EnterpriseNetPay: "企业网银",
	OtherTransfer:    "其他账户划账",
	BackRecharge:     "后台充值",
}

func (c payChannelEnum_) GetKey(value string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if vo.Field(i).Interface().(string) == value {
			return typeVo.Field(i).Name
		}
	}
	return ""
}

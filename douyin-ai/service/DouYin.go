package service

import (
	"bytes"
	"center-ai/enums"
	"center-ai/model"
	"center-ai/utils/config"
	"center-ai/utils/logger"
	"center-ai/utils/myhttp"
	"center-ai/utils/tools"
	"compress/gzip"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"io"
	"log"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/gorilla/websocket"
	"github.com/tidwall/gjson"

	dyproto "center-ai/protobuf"
)

type douyin_ struct {
	RoomTask  map[uint]*DouyinRoom
	GiftPrice map[uint]int64
}

var DouyinService douyin_

func (d *douyin_) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("Design奔溃:", e)
		}
	}()

	if d.RoomTask == nil {
		d.RoomTask = make(map[uint]*DouyinRoom, 0)
	}

	logger.Info("Douyin.Run 开始循环监控直播间")
	var douyinRoom model.DouyinRoom
	var arr = make([]model.DouyinRoom, 0)
	if _, err := douyinRoom.GetAllList(&arr, 1, 100); err != nil {
		logger.Error(err)
	}

	for _, v := range arr {
		//logger.Info("开始检测直播间EnterId:", v.EnterId, "  ", v.Monitor)
		if v.Monitor == 0 { //停止监控
			if _, ok := d.RoomTask[v.EnterId]; ok {
				//停止监测
				if err := d.RoomTask[v.EnterId].Close(); err != nil {
					logger.Error(err, "关闭检测失败 roomId:", v.RoomId)
				}
			}
		} else if v.Monitor == 1 {
			if _, ok := d.RoomTask[v.EnterId]; ok {
				//已存在，不处理
				logger.Info("直播监测中EnterId:", v.EnterId, "  ", v.Monitor)

				// 计算时间间隔
				duration := time.Now().Sub(d.RoomTask[v.EnterId].LastAckTime)
				// 将时间间隔转换为秒
				seconds := duration.Seconds()
				if seconds > 60*5 {
					if r, err := NewDouyinRoom(v.EnterId); err != nil {
						if strings.Contains(err.Error(), "直播已结束") {
							logger.Info("[", v.EnterId, "]直播已结束")
						} else {
							logger.Error("链接抖音直播间失败,roomId:", v.RoomId)
						}
						continue
					} else {
						if r.RoomStatus == 4 {
							logger.Info("[", v.EnterId, "]直播已结束 移除队列")
							d.RoomTask[v.EnterId].RoomStatus = 4
							delete(d.RoomTask, v.EnterId)
						}
					}
				}
			} else {
				if r, err := NewDouyinRoom(v.EnterId); err != nil {
					logger.Error("链接抖音直播间失败,roomId:", v.RoomId)
					continue
				} else {
					if r == nil {
						if r, err = NewDouyinRoom(v.EnterId); err != nil {
							logger.Error("链接抖音直播间失败,roomId:", v.RoomId)
							continue
						}
						if r == nil {
							logger.Error("r==nil enterId:", v.EnterId)
							continue
						}
					}
					if r.RoomStatus == 4 {
						logger.Info("直播已结束EnterId:", v.EnterId, "  ", v.Monitor)
					} else {
						if err := r.Connect(); err != nil {
							logger.Error("直播间链接失败EnterId:", v.EnterId, err)
						} else {
							d.RoomTask[v.EnterId] = r
							logger.Info("直播间链接成功EnterId:", v.EnterId, "  ", v.Monitor)
						}

					}
				}
			}
		}
	}
	logger.Info("循环结束")
}

func (d *douyin_) LoadGiftPrice() {
	if d.GiftPrice == nil {
		d.GiftPrice = make(map[uint]int64, 0)
	}
	var arr = make([]model.DouyinGift, 0)
	var gift model.DouyinGift
	_, err := gift.GetList(&arr, 0, "", 1, 1000)
	if err != nil {
		logger.Error(err)
		return
	}
	for _, val := range arr {
		//if _, ok := d.GiftPrice[val.GiftId]; ok {
		//} else {
		//	d.GiftPrice[val.GiftId] = val.Price
		//}
		d.GiftPrice[val.GiftId] = int64(val.Price)
	}
	logger.Info("LoadGiftPrice加载完成 共", len(arr), "条数据")
}

func (d *douyin_) GetGiftFromDouyin() (int, error) {
	rawUrl := "https://live.douyin.com/webcast/gift/list/"
	params := url.Values{}
	params.Add("aid", "6383")
	resText, err := myhttp.Get(rawUrl, params)
	if err != nil {
		logger.Error(err)
		return 0, err
	}
	//logger.Info(resText)

	gifts := gjson.Get(resText, "data.gifts").Array()
	for i := 0; i < len(gifts); i++ {
		id := gifts[i].Get("id").Uint()
		diamondCount := gifts[i].Get("diamond_count").Int()
		name := gifts[i].Get("name").String()
		icons := gifts[i].Get("icon.url_list").Array()
		iconUrl := ""
		if len(icons) > 0 {
			iconUrl = icons[0].Str
		}
		fmt.Println(id, name, diamondCount, iconUrl)
		var gift model.DouyinGift
		if err := gift.GetByGiftId(uint(id)); err != nil {
			if err == gorm.ErrRecordNotFound {
				gift := model.DouyinGift{
					GiftId:   uint(id),
					Price:    int(diamondCount),
					GiftName: name,
					Avatar:   iconUrl,
				}
				if err1 := gift.Save(); err1 != nil {
					logger.Error(err1)
					return len(gifts), err1
				}
			} else {
				logger.Error(err)
				return len(gifts), err
			}
		} else {
			if name != "" {
				gift.GiftName = name
			}
			if diamondCount > 0 {
				gift.Price = int(diamondCount)
			}
			if iconUrl != "" {
				gift.Avatar = iconUrl
			}
			if err1 := gift.Save(); err1 != nil {
				logger.Error(err1)
				return len(gifts), err1
			}
		}
	}
	return len(gifts), nil
	//enterIdStr := gjson.Get(appJson, "pathname").String()
}

type DouyinRoom struct {
	// 房间地址
	Url string

	Ttwid string

	RoomStore string

	RoomAutoId uint

	EnterId uint

	RoomId string

	RoomTitle string

	NickName string

	RoomStatus int64

	wsConnect *websocket.Conn
	wsClosed  bool

	AckCount    uint
	LastAckTime time.Time
}

func NewDouyinRoom(enterId uint) (*DouyinRoom, error) {
	u := fmt.Sprintf("https://live.douyin.com/%d", enterId)

	h := map[string]string{
		"accept":     "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
		"User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36",
		"cookie":     "__ac_nonce=0638733a400869171be51",
	}
	req, err := http.NewRequest("GET", u, nil)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	for k, v := range h {
		req.Header.Set(k, v)
	}
	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	defer res.Body.Close()
	data := res.Cookies()
	var ttwid string
	for _, c := range data {
		if c.Name == "ttwid" {
			ttwid = c.Value
			break
		}
	}
	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	resText := string(body)

	re := regexp.MustCompile(`<script id="RENDER_DATA" type="application/json">(.*?)</script>`)
	match := re.FindStringSubmatch(resText)

	roomId := ""
	//web_rid := ""
	roomTitle := ""
	nickName := ""
	status := ""
	if match == nil || len(match) < 2 {

		re1 := regexp.MustCompile(`\\"roomId\\":\\"(\d+)\\"`)
		match1 := re1.FindStringSubmatch(resText)
		if len(match1) > 1 {
			roomId = match1[1]
		} else {
			fmt.Println("No match found roomId")
		}

		//re1 = regexp.MustCompile(`\\"web_rid\\":\\"(\d+)\\"`)
		//match1 = re1.FindStringSubmatch(resText)
		//if len(match1) > 1 {
		//	web_rid = match1[1]
		//} else {
		//	fmt.Println("No match found web_rid")
		//}
		//
		//if !strings.Contains(u, fmt.Sprintf("%s", web_rid)) {
		//	logger.Error(u, web_rid)
		//}

		re1 = regexp.MustCompile(`\\"nickname\\":\\"([^"]*?)\\",`)
		match1 = re1.FindStringSubmatch(resText)
		if len(match1) > 1 {
			nickName = match1[1]
		} else {
			fmt.Println("No match found title")
		}

		re1 = regexp.MustCompile(`\\"title\\":\\"([^"]*?)\\",\\"user_count_str\\"`)
		match1 = re1.FindStringSubmatch(resText)
		if len(match1) > 1 {
			roomTitle = match1[1]
		} else {
			fmt.Println("No match found title")
		}

		re1 = regexp.MustCompile(`\\"status\\":(\d+),`)
		match1 = re1.FindStringSubmatch(resText)
		if len(match1) > 1 {
			status = match1[1]
		} else {
			fmt.Println("No match found status")
		}

		if roomId == "" || roomTitle == "" || status == "" {
			logger.Error(u, "No match json")
			return nil, errors.New("no match json")
		} else {
			var room model.DouyinRoom
			if err := room.GetByEnterId(enterId); err != nil {
				if err != gorm.ErrRecordNotFound {
					logger.Error(err, roomId)
				}
			} else {
				if err := room.SetTitle(roomId, nickName, roomTitle); err != nil {
					logger.Error(err, roomId)
				}
			}

			num, _ := strconv.Atoi(status)

			return &DouyinRoom{
				RoomAutoId:  room.ID,
				EnterId:     enterId,
				Url:         u,
				Ttwid:       ttwid,
				RoomStore:   "",
				RoomId:      roomId,
				RoomTitle:   roomTitle,
				RoomStatus:  int64(num),
				NickName:    nickName,
				LastAckTime: time.Now(),
			}, nil

		}
	}

	resText = match[1]
	resText, err = url.QueryUnescape(resText)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	appJson := gjson.Get(resText, "app").String()
	//enterIdStr := gjson.Get(appJson, "pathname").String()
	//enterId := tools.ParseUint(enterIdStr[1:])

	initialStateJson := gjson.Get(appJson, "initialState").String()
	roomStoreJson := gjson.Get(initialStateJson, "roomStore").String()
	liveRoomId := gjson.Get(roomStoreJson, "roomInfo.roomId").String()
	liveNickName := gjson.Get(roomStoreJson, "roomInfo.nickname").String()
	liveRoomTitle := gjson.Get(roomStoreJson, "roomInfo.room.title").String()
	liveRoomStatus := gjson.Get(roomStoreJson, "roomInfo.room.status").Int()

	if u != "" && roomStoreJson != "" && liveRoomId != "" && liveRoomTitle != "" {

		var room model.DouyinRoom
		if err := room.GetByEnterId(enterId); err != nil {
			if err != gorm.ErrRecordNotFound {
				logger.Error(err, liveRoomId)
			}
		} else {
			if err := room.SetTitle(liveRoomId, liveNickName, liveRoomTitle); err != nil {
				logger.Error(err, liveRoomId)
			}
		}

		return &DouyinRoom{
			RoomAutoId:  room.ID,
			EnterId:     enterId,
			Url:         u,
			Ttwid:       ttwid,
			RoomStore:   roomStoreJson,
			RoomId:      liveRoomId,
			RoomTitle:   liveRoomTitle,
			RoomStatus:  liveRoomStatus,
			NickName:    liveNickName,
			LastAckTime: time.Now(),
		}, nil
	}
	return nil, fmt.Errorf("房间初始化失败！")
}

func DouyinSearch(keyword string) (*string, error) {
	//u := fmt.Sprintf("https://www.douyin.com/aweme/v1/web/discover/search/?device_platform=webapp&aid=6383&channel=channel_pc_web&search_channel=aweme_user_web&keyword=%s&search_source=search_sug&query_correct_type=1&is_filter_search=0&from_group_id=&offset=0&count=10&pc_client_type=1&version_code=170400&version_name=17.4.0&cookie_enabled=true&screen_width=1792&screen_height=1120&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Safari&browser_version=16.2&browser_online=true&engine_name=WebKit&engine_version=605.1.15&os_name=Mac+OS&os_version=10.15.7&cpu_core_num=8&device_memory=&platform=PC",keyword)
	u := fmt.Sprintf("https://www.douyin.com/search/%s?source=search_sug&type=user", keyword)
	//u := fmt.Sprintf("https://live.douyin.com/%s", keyword)

	h := map[string]string{
		"accept":     "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
		"User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36",
		"cookie":     "__ac_nonce=0657fc52400f480a73912",
	}
	req, err := http.NewRequest("GET", u, nil)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	for k, v := range h {
		req.Header.Set(k, v)
	}
	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	defer res.Body.Close()
	data := res.Cookies()
	var ttwid string
	for _, c := range data {
		if c.Name == "ttwid" {
			ttwid = c.Value
			break
		}
	}
	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	resText := string(body)

	fmt.Println(ttwid, resText)
	return nil, fmt.Errorf("房间初始化失败！")
}

func (r *DouyinRoom) Connect() error {
	wsUrl := "wss://webcast3-ws-web-lq.douyin.com/webcast/im/push/v2/?app_name=douyin_web&version_code=180800&webcast_sdk_version=1.3.0&update_version_code=1.3.0&compress=gzip&internal_ext=internal_src:dim|wss_push_room_id:%s|wss_push_did:%s|dim_log_id:202302171547011A160A7BAA76660E13ED|fetch_time:1676620021641|seq:1|wss_info:0-1676620021641-0-0|wrds_kvs:WebcastRoomStatsMessage-1676620020691146024_WebcastRoomRankMessage-1676619972726895075_AudienceGiftSyncData-1676619980834317696_HighlightContainerSyncData-2&cursor=t-1676620021641_r-1_d-1_u-1_h-1&host=https://live.douyin.com&aid=6383&live_id=1&did_rule=3&debug=false&endpoint=live_pc&support_wrds=1&im_path=/webcast/im/fetch/&user_unique_id=%s&device_platform=web&cookie_enabled=true&screen_width=1440&screen_height=900&browser_language=zh&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0%20(Macintosh;%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/110.0.0.0%20Safari/537.36&browser_online=true&tz_name=Asia/Shanghai&identity=audience&room_id=%s&heartbeatDuration=0&signature=00000000"
	wsUrl = strings.Replace(wsUrl, "%s", r.RoomId, -1)
	h := http.Header{}
	h.Set("cookie", "ttwid="+r.Ttwid)
	h.Set("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

	wsConn, wsResp, err := websocket.DefaultDialer.Dial(wsUrl, h)
	if err != nil {
		logger.Error(err)
		return err
	}
	log.Println(wsResp.StatusCode)
	r.wsConnect = wsConn
	r.wsClosed = false
	go r.read()
	go r.send()
	//r.setAckTime()
	return nil
}

func (r *DouyinRoom) Close() error {
	return r.wsConnect.Close()
}

func (r *DouyinRoom) read() {
	logger.Info(r.EnterId, "开始循环获取消息")
	for {
		_, data, err := r.wsConnect.ReadMessage()
		if err != nil {
			if strings.Contains(err.Error(), "close") {
				r.wsClosed = true
				logger.Error(err, r.EnterId, " websocket has closed，delete")
				delete(DouyinService.RoomTask, r.EnterId)
				logger.Info("webSocket read 退出", r.EnterId)
				return
			} else {
				logger.Error(err, r.EnterId)
				time.Sleep(time.Second * 2)
				continue
			}
		}

		var msgPack dyproto.PushFrame
		_ = proto.Unmarshal(data, &msgPack)
		//log.Println(msgPack)
		decompressed, _ := degzip(msgPack.Payload)
		var payloadPackage dyproto.Response
		_ = proto.Unmarshal(decompressed, &payloadPackage)
		if payloadPackage.NeedAck {
			//r.sendAck(msgPack.LogId, payloadPackage.InternalExt)
		}
		//logger.Info("MessagesList_Len:", len(payloadPackage.MessagesList))
		for _, msg := range payloadPackage.MessagesList {
			//log.Println(r.EnterId, "[===msg===]", "msgId:", msg.MsgId, " method:", msg.Method, "  msgType:", msg.MsgType)
			switch msg.Method {
			case "WebcastRoomStreamAdaptationMessage":
				//log.Println("WebcastRoomStreamAdaptationMessage")
				//hexString := hex.EncodeToString(msg.Payload)
				//log.Println("[===WebcastRoomStreamAdaptationMessage hexString===]", hexString)
			case "WebcastRoomStatsMessage":
				//log.Println("WebcastRoomStatsMessage")

				//hexString := hex.EncodeToString(msg.Payload)
				//log.Println("[===WebcastRoomStatsMessage hexString===]", hexString)
			case "WebcastChatMessage":
				r.LastAckTime = time.Now()
				r.parseChatMsg(msg.Payload)
			case "WebcastGiftMessage":
				//log.Println("[msg]", msg)
				r.LastAckTime = time.Now()
				r.parseGiftMsg(msg.Payload)
			case "WebcastLikeMessage":
				r.LastAckTime = time.Now()
				r.parseLikeMsg(msg.Payload)
			case "WebcastMemberMessage":
				r.LastAckTime = time.Now()
				r.parseEnterMsg(msg.Payload)
			}
		}
	}
}

func (r *DouyinRoom) send() {
	logger.Info(r.EnterId, "开始循环发送消息")
	for {
		if r.wsClosed {
			logger.Info("webSocket send 退出", r.EnterId)
			return
		}
		pingPack := &dyproto.PushFrame{
			PayloadType: "bh",
		}
		data, _ := proto.Marshal(pingPack)
		err := r.wsConnect.WriteMessage(websocket.BinaryMessage, data)
		if err != nil {
			logger.Error(err, r.EnterId, "发送心跳失败")
			if strings.Contains(err.Error(), "use of closed network connection") {
				r.wsClosed = true
				return
			}
		} else {
			//log.Println("EnterId:", r.EnterId, "发送心跳")
			r.AckCount++
			if r.AckCount%10 == 0 {
				logger.Info("EnterId:", r.EnterId, "记录Ack包时间")
				r.setAckTime()
			}
		}
		time.Sleep(time.Second * 10)
	}
}

func (r *DouyinRoom) sendAck(logId uint64, iExt string) {
	ackPack := &dyproto.PushFrame{
		LogId:       logId,
		PayloadType: iExt,
	}
	data, _ := proto.Marshal(ackPack)
	if err := r.wsConnect.WriteMessage(websocket.BinaryMessage, data); err != nil {
		logger.Error(err, "enterId:", r.EnterId)
	}
	//log.Println("EnterId:", r.EnterId, "发送Ack")
}

func (r *DouyinRoom) setAckTime() {
	var room model.DouyinRoom
	if err := room.GetByEnterId(r.EnterId); err != nil {
		logger.Error(err, r.EnterId)
	} else {
		if config.SaveToDb {
			if err := room.SetAckTime(); err != nil {
				logger.Error(err, r.EnterId)
			}
		}
	}
}

func degzip(data []byte) ([]byte, error) {
	b := bytes.NewReader(data)
	var out bytes.Buffer
	r, err := gzip.NewReader(b)
	if err != nil {
		return nil, err
	}
	_, err = io.Copy(&out, r)
	if err != nil {
		return nil, err
	}
	return out.Bytes(), nil
}

func (r *DouyinRoom) parseChatMsg(msg []byte) {
	var chatMsg dyproto.ChatMessage
	_ = proto.Unmarshal(msg, &chatMsg)
	//log.Println("[弹幕]", chatMsg)
	//log.Printf("[弹幕] %s : %s\n", chatMsg.User.NickName, chatMsg.Content)
	sendTime := tools.ParseTime(int64(chatMsg.EventTime))
	r.saveToDb("WebcastChatMessage", chatMsg.User.ShortId, chatMsg.User.NickName, chatMsg.Content, 0, "", 0, sendTime, "")
}

func (r *DouyinRoom) parseGiftMsg(msg []byte) {
	var giftMsg dyproto.GiftMessage
	_ = proto.Unmarshal(msg, &giftMsg)

	sendTime := tools.ParseTime(int64(giftMsg.SendTime))
	//createTimeStr := fmt.Sprintf("%d", giftMsg.Common.CreateTime)
	//if len(createTimeStr) == 10 {
	if giftMsg.RepeatEnd == 1 {
		//log.Println("[存储][===giftMsg===]", tools.GetJsonFromStruct(giftMsg))
		//hexString := hex.EncodeToString(msg)
		//log.Println("[===hexString===]", hexString)

		log := fmt.Sprintf("[%d][礼物] %s 送了 %s(%d) * %d   %d  %d %d %d\n", r.EnterId, giftMsg.User.NickName, giftMsg.Gift.Name, giftMsg.GiftId, giftMsg.ComboCount, giftMsg.TotalCount, giftMsg.RepeatCount, giftMsg.GroupCount, giftMsg.FanTicketCount)
		logger.Info(log)
		r.saveToDb(enums.WebcastEnum.WebcastGiftMessage, giftMsg.User.ShortId, giftMsg.User.NickName, "", giftMsg.GiftId, giftMsg.Gift.Name, giftMsg.ComboCount, sendTime, giftMsg.TraceId)
	}
	//if r.EnterId == 780805030682 || r.EnterId == 442325740189 {
	//	logger.Info("[===giftMsg===] [===", giftMsg.TraceId, "===]", tools.GetJsonFromStruct(giftMsg))
	//	hexString := hex.EncodeToString(msg)
	//	logger.Info("[===giftMsg hexString===] [===", giftMsg.TraceId, "===]", hexString)
	//	r.saveToDb("WebcastGiftMessageALL", giftMsg.User.ShortId, giftMsg.User.NickName, fmt.Sprintf("%d", giftMsg.SendTime), giftMsg.GiftId, giftMsg.Gift.Name, giftMsg.ComboCount, sendTime, giftMsg.TraceId)
	//}

}

func (r *DouyinRoom) parseLikeMsg(msg []byte) {
	var likeMsg dyproto.LikeMessage
	_ = proto.Unmarshal(msg, &likeMsg)
	//log.Println("[点赞]", likeMsg)
	//log.Printf("[点赞] %s 点赞 * %d \n", likeMsg.User.NickName, likeMsg.Count)
}

func (r *DouyinRoom) parseEnterMsg(msg []byte) {
	var enterMsg dyproto.MemberMessage
	_ = proto.Unmarshal(msg, &enterMsg)
	//log.Println("[入场]", enterMsg)
	//log.Printf("[入场] %s(%d) 直播间\n", enterMsg.User.NickName, enterMsg.User.Id)
	r.saveToDb("WebcastMemberMessage", enterMsg.User.ShortId, enterMsg.User.NickName, "", 0, "", 0, time.Now(), "")
}

func (r *DouyinRoom) saveToDb(method string, shortId uint64, nickName string, content string, giftId uint64, giftName string, comboCount uint64, sendTime time.Time, traceId string) {
	message := model.DouyinMessage{
		EnterId:    r.EnterId,
		RoomId:     r.RoomId,
		Method:     method,
		ShortId:    uint(shortId),
		NickName:   nickName,
		Content:    content,
		GiftId:     uint(giftId),
		GiftName:   giftName,
		ComboCount: uint(comboCount),
		SendTime:   sendTime,
		TraceId:    traceId,
	}
	if config.SaveToDb {
		if err := message.Save(); err != nil {
			logger.Error(err)
		}
	}
}

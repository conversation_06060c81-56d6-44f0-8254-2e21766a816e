package main

import (
	"center-ai/enums"
	"center-ai/model"
	"center-ai/routes"
	"center-ai/service"
	"center-ai/utils/config"
	"center-ai/utils/logger"
	"center-ai/utils/myredis"
	"fmt"
	"github.com/gin-gonic/gin"
	"golang.org/x/net/websocket"
	"strings"
	"time"
)

//func wsHandler(ws *websocket.Conn) {
//	var message string
//	err := websocket.Message.Receive(ws, &message)
//	if err != nil {
//		fmt.Println("Error receiving message:", err.Error())
//		return
//	}
//	fmt.Println("Received message:", message)
//
//	response := "Hello, client!"
//	err = websocket.Message.Send(ws, response)
//	if err != nil {
//		fmt.Println("Error sending message:", err.Error())
//		return
//	}
//	fmt.Println("Sent message:", response)
//}

func wsHandler(ws *websocket.Conn) {
	var err error

	for {
		var reply string

		// 接收消息
		if err = websocket.Message.Receive(ws, &reply); err != nil {
			fmt.Println("Can't receive", err)
			break
		}

		//fmt.Println("Received back from client: " + reply)

		msg := "Received: " + reply
		//fmt.Println("Sending to client: " + msg)
		fmt.Println(msg)

		// 发送消息
		if strings.Contains(msg, "Hello") {
			if err = websocket.Message.Send(ws, msg); err != nil {
				fmt.Println("Can't send")
				break
			}
		}

	}
}

func main() {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("main奔溃:", e)
		}
	}()

	logger.Info("启动main")
	gin.SetMode(config.AppMode)
	if enums.EnvEnum.GetKey(config.Env) == "" {
		logger.Fatal("运行环境参数值错误：", config.Env)
	}

	model.InitDb()
	myredis.InitRedis()

	if config.Bundle == "douyin.cyuai.aigc" {
		service.DouyinService.LoadGiftPrice()
	}

	if config.Bundle == "sd.cyuai.aigc" {

		go func() {
			ticker := time.NewTicker(1 * time.Second)
			defer ticker.Stop()
			for range ticker.C {
				logger.Info("开始执行SdService业务逻辑")
				service.SdService.Run()
				logger.Info("SdService业务逻辑出错,延迟10秒")
			}
		}()
	}

	if config.RunTimer == true {
		logger.Info("启动RunTimer")
		service.RunTimer()
		logger.Info("启动RunTimer完成")
	}

	//go func() {
	//	http.Handle("/ws", websocket.Handler(wsHandler))
	//	err := http.ListenAndServe(":8080", nil)
	//	//err := http.ListenAndServeTLS(":8080", "cert.pem", "key.pem", nil)
	//	if err != nil {
	//		fmt.Println("Error starting server:", err.Error())
	//		return
	//	} else {
	//		logger.Info("websocket listened:8080")
	//	}
	//}()

	routes.InitRouter()
	logger.Info("main启动完成")
}

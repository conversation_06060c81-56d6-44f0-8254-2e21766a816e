package model

import (
	"errors"
	"gorm.io/gorm"
	"time"
)

type DouyinRoom struct {
	gorm.Model
	EnterId   uint      `json:"enter_id" gorm:"type:bigint;not null;default:0;comment:进入ID(web_rid)"`
	RoomId    string    `json:"room_id" gorm:"type:varchar(250);not null;default:'';comment:房间号"`
	RoomTitle string    `json:"room_title" gorm:"type:varchar(50);not null;default:'';comment:房间标题"`
	NickName  string    `json:"nick_name" gorm:"type:varchar(50);not null;default:'';comment:用户昵称"`
	Avatar    string    `json:"avatar" gorm:"type:varchar(300);not null;default:'';comment:头像"`
	Cover     string    `json:"cover" gorm:"type:varchar(300);not null;default:'';comment:封面"`
	Remark    string    `json:"remark" gorm:"type:varchar(250);not null;default:'';comment:备注"`
	Monitor   int       `json:"monitor" gorm:"type:int;not null;default:0;comment:1监控中"`
	AckTime   time.Time `json:"ack_time" gorm:"type:datetime;default:'1900-01-01';comment:心跳时间"`
}

func (DouyinRoom) TableName() string {
	return "T_DouyinRoom"
}

func (o *DouyinRoom) GetById(id uint) error {
	return db.First(o, id).Error
}

func (o *DouyinRoom) GetByEnterId(enterId uint) error {
	return db.First(o, "enter_id=?", enterId).Error
}

func (o *DouyinRoom) GetByRoomId(roomId string) error {
	return db.First(o, "room_id=?", roomId).Error
}

func (o *DouyinRoom) GetList(dest interface{}, enterId uint, title string, monitor, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o)
	if enterId > 0 {
		tx.Where("enter_id=?", enterId)
	}
	if title != "" {
		tx.Where("room_title like%?%", title)
	}
	if monitor >= 0 {
		tx.Where("monitor=?", monitor)
	}

	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *DouyinRoom) GetAllList(dest interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o)
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

//调用方式
//var role model.Role
//var arr = make([]roleListItem, 0)
//total, err := role.GetList(&arr, req.UserId, req.Mobile, req.Page, req.PageSize)
//if err != nil {
//logger.Error(err)
//errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
//return
//}

func (o *DouyinRoom) Save() error {
	return db.Save(o).Error
}

func (o *DouyinRoom) SetAckTime() error {
	return db.Model(o).Updates(DouyinRoom{AckTime: time.Now()}).Error
}
func (o *DouyinRoom) SetTitle(roomId string, nickName string, title string) error {
	return db.Model(o).Updates(DouyinRoom{RoomId: roomId, NickName: nickName, RoomTitle: title}).Error
}

func (o *DouyinRoom) SetUpscalePath(id uint, level int, path string) error {
	if level == 1 {
		return db.Model(o).Where("id = ?", id).Updates(map[string]interface{}{"path1": path, "scale_at": nil}).Error
	} else if level == 2 {
		return db.Model(o).Where("id = ?", id).Updates(map[string]interface{}{"path2": path, "scale_at": nil}).Error
	} else {
		return errors.New("未找到level对应字段")
	}
}

func (o *DouyinRoom) DelDigital() error {
	return db.Model(o).Omit("version").UpdateColumns(map[string]interface{}{"digital_id": 0, "digital_uuid": ""}).Error
}

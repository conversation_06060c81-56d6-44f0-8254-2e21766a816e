package logger

import (
	"center-ai/enums"
	"center-ai/utils/config"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"time"
)

var errorLogger *zap.SugaredLogger

var levelMap = map[string]zapcore.Level{

	"debug": zapcore.DebugLevel,

	"info": zapcore.InfoLevel,

	"warn": zapcore.WarnLevel,

	"error": zapcore.ErrorLevel,

	"dpanic": zapcore.DPanicLevel,

	"panic": zapcore.PanicLevel,

	"fatal": zapcore.FatalLevel,
}

func getLoggerLevel(lvl string) zapcore.Level {

	if level, ok := levelMap[lvl]; ok {

		return level

	}

	return zapcore.InfoLevel

}

func iSO8601TimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	encodeTimeLayout(t, "2006-01-02 15:04:05.000", enc)
}
func encodeTimeLayout(t time.Time, layout string, enc zapcore.PrimitiveArrayEncoder) {
	type appendTimeEncoder interface {
		AppendTimeLayout(time.Time, string)
	}

	if enc, ok := enc.(appendTimeEncoder); ok {
		enc.AppendTimeLayout(t, layout)
		return
	}
	enc.AppendString(t.Format(layout))
}

func init() {

	fileName := "./log/zap.log"

	level := getLoggerLevel("info")

	syncWriter := zapcore.AddSync(&lumberjack.Logger{

		Filename: fileName, //日志文件存放目录，如果文件夹不存在会自动创建

		MaxSize: 1, //1 << 30=1G //文件大小限制,单位MB

		MaxBackups: 5, //最大保留日志文件数量

		MaxAge: 30, //日志文件保留天数

		LocalTime: true,

		Compress: false,
	})

	encoderConfig := zap.NewProductionEncoderConfig() //NewJSONEncoder()输出json格式，NewConsoleEncoder()输出普通文本格式

	encoderConfig.EncodeTime = iSO8601TimeEncoder //zapcore.ISO8601TimeEncoder

	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	encoder := zapcore.NewConsoleEncoder(encoderConfig)

	if config.Env == enums.EnvEnum.DEV {
		consoleOutput := zapcore.Lock(os.Stdout) // 用 Lock 将 os.Stdout 包装起来
		fileCore := zapcore.NewCore(encoder, zapcore.NewMultiWriteSyncer(consoleOutput, syncWriter), zapcore.DebugLevel)
		logger := zap.New(fileCore, zap.AddCaller(), zap.AddCallerSkip(1))
		errorLogger = logger.Sugar()

	} else {
		fileCore := zapcore.NewCore(encoder, syncWriter, zap.NewAtomicLevelAt(level))
		logger := zap.New(fileCore, zap.AddCaller(), zap.AddCallerSkip(1)) //AddCaller()为显示文件名和行号
		errorLogger = logger.Sugar()
	}
}

func Debug(args ...interface{}) {

	errorLogger.Debug(args...)

}

func Debugf(template string, args ...interface{}) {

	errorLogger.Debugf(template, args...)

}

func Info(args ...interface{}) {

	errorLogger.Info(args...)

}

func Infof(template string, args ...interface{}) {

	errorLogger.Infof(template, args...)

}

func Warn(args ...interface{}) {

	errorLogger.Warn(args...)

}

func Warnf(template string, args ...interface{}) {

	errorLogger.Warnf(template, args...)

}

func Error(args ...interface{}) {

	errorLogger.Error(args...)

}

func Errorf(template string, args ...interface{}) {

	errorLogger.Errorf(template, args...)

}

func DPanic(args ...interface{}) {

	errorLogger.DPanic(args...)

}

func DPanicf(template string, args ...interface{}) {

	errorLogger.DPanicf(template, args...)

}

func Panic(args ...interface{}) {

	errorLogger.Panic(args...)

}

func Panicf(template string, args ...interface{}) {

	errorLogger.Panicf(template, args...)

}

func Fatal(args ...interface{}) {
	errorLogger.Fatal(args...)

}

func Fatalf(template string, args ...interface{}) {

	errorLogger.Fatalf(template, args...)

}

package main

import (
	"fmt"
	"github.com/sacOO7/gowebsocket"
	"log"
	"net/http"
	"os"
	"os/signal"
)

func main23() {
	url := "wss://webcast5-ws-web-lf.douyin.com/webcast/im/push/v2/?app_name=douyin_web&version_code=180800&webcast_sdk_version=1.0.8&update_version_code=1.0.8&compress=gzip&device_platform=web&cookie_enabled=true&screen_width=1792&screen_height=1120&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0%20(Macintosh;%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/*********%20Safari/537.36&browser_online=true&tz_name=Asia/Shanghai&cursor=r-1_d-1_u-1_h-1_t-1693297154806&internal_ext=internal_src:dim|wss_push_room_id:7272653376834276131|wss_push_did:7262176106497050112|dim_log_id:20230829161914F79202C3B1A684040BA5|first_req_ms:1693297154735|fetch_time:1693297154806|seq:1|wss_info:0-1693297154806-0-0|wrds_kvs:WebcastRoomStatsMessage-1693297150895261672_WebcastRoomStreamAdaptationMessage-1693297152457151976_LotteryInfoSyncData-1693297135462819026_WebcastRoomRankMessage-1693297144992754183&host=https://live.douyin.com&aid=6383&live_id=1&did_rule=3&endpoint=live_pc&support_wrds=1&user_unique_id=&im_path=/webcast/im/fetch/&identity=audience&room_id=7272653376834276131&heartbeatDuration=0&signature=RgyCTiUgP8h8Nt+6"
	exit := make(chan struct{}, 1)
	socket := gowebsocket.New(url)
	socket.OnConnected = func(socket gowebsocket.Socket) {
		log.Println("Connected to server")
	}
	socket.OnTextMessage = func(message string, socket gowebsocket.Socket) {
		log.Println("Recieved message: " + message)
	}
	socket.OnDisconnected = func(err error, socket gowebsocket.Socket) {
		exit <- struct{}{}
		return
	}
	socket.Connect()
	var msg string
	for {
		select {
		case <-exit:
			return
		default:
			if msg != "exit" {
				fmt.Scanln(&msg)
				socket.SendText(msg)
			}
		}
	}
}

func main26() {
	url := "wss://webcast5-ws-web-lf.douyin.com/webcast/im/push/v2/?app_name=douyin_web&version_code=180800&webcast_sdk_version=1.0.8&update_version_code=1.0.8&compress=gzip&device_platform=web&cookie_enabled=true&screen_width=1792&screen_height=1120&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0%20(Macintosh;%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/*********%20Safari/537.36&browser_online=true&tz_name=Asia/Shanghai&cursor=r-1_d-1_u-1_h-1_t-1693297154806&internal_ext=internal_src:dim|wss_push_room_id:7272653376834276131|wss_push_did:7262176106497050112|dim_log_id:20230829161914F79202C3B1A684040BA5|first_req_ms:1693297154735|fetch_time:1693297154806|seq:1|wss_info:0-1693297154806-0-0|wrds_kvs:WebcastRoomStatsMessage-1693297150895261672_WebcastRoomStreamAdaptationMessage-1693297152457151976_LotteryInfoSyncData-1693297135462819026_WebcastRoomRankMessage-1693297144992754183&host=https://live.douyin.com&aid=6383&live_id=1&did_rule=3&endpoint=live_pc&support_wrds=1&user_unique_id=&im_path=/webcast/im/fetch/&identity=audience&room_id=7272653376834276131&heartbeatDuration=0&signature=RgyCTiUgP8h8Nt+6"

	socket := gowebsocket.New(url)

	socket.OnConnected = func(socket gowebsocket.Socket) {
		fmt.Println("Connected to server")
	}

	socket.OnConnectError = func(err error, socket gowebsocket.Socket) {
		fmt.Println("Received connect error ", err)
	}

	socket.OnTextMessage = func(message string, socket gowebsocket.Socket) {
		fmt.Println("Received message: " + message)
	}

	socket.OnDisconnected = func(err error, socket gowebsocket.Socket) {
		fmt.Println("Disconnected from server ")
	}

	socket.Connect()
}

func main31() {
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt)

	wssUrl := "wss://webcast5-ws-web-lq.douyin.com/webcast/im/push/v2/?app_name=douyin_web&version_code=180800&webcast_sdk_version=1.0.8&update_version_code=1.0.8&compress=gzip&device_platform=web&cookie_enabled=true&screen_width=1792&screen_height=1120&browser_language=zh-CN&browser_platform=MacIntel&browser_name=Mozilla&browser_version=5.0%20(Macintosh;%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/*********%20Safari/537.36&browser_online=true&tz_name=Asia/Shanghai&cursor=t-1693368193220_r-1_d-1_u-1_h-1&internal_ext=internal_src:dim|wss_push_room_id:7272953108366510885|wss_push_did:7262176106497050112|dim_log_id:202308301203131295AA4A006FD0003EBA|first_req_ms:1693368193156|fetch_time:1693368193220|seq:1|wss_info:0-1693368193220-0-0|wrds_kvs:LotteryInfoSyncData-1693368192303866656_WebcastRoomStreamAdaptationMessage-1693368184864468212_WebcastRoomStatsMessage-1693368188929625124_WebcastRoomRankMessage-1693368182914496210&host=https://live.douyin.com&aid=6383&live_id=1&did_rule=3&endpoint=live_pc&support_wrds=1&user_unique_id=&im_path=/webcast/im/fetch/&identity=audience&room_id=7272953108366510885&heartbeatDuration=0&signature=RpvsHe6gj0fl2AML"
	ttwid := "1%7CPlF7QTQgFw_ZsJh9z8uJbQfuxk5-bHIcqRgFEI0C_zA%7C1690857158%7C5154e8723b5cb069ef48fd5c0d4cec1726007d53c1d8192ed6a854c5cfab1287"

	socket := gowebsocket.New(wssUrl)
	//socket.ConnectionOptions = gowebsocket.ConnectionOptions{
	//	//Proxy: gowebsocket.BuildProxy("http://example.com"),
	//	UseSSL:         true,
	//	UseCompression: true,
	//	Subprotocols:   []string{"chat", "superchat"},
	//}

	socket.RequestHeader.Set("Accept-Encoding", "gzip, deflate, sdch")
	socket.RequestHeader.Set("Accept-Language", "en-US,en;q=0.8")
	socket.RequestHeader.Set("Pragma", "no-cache")
	socket.RequestHeader.Set("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.87 Safari/537.36")
	cookie := &http.Cookie{
		Name:  "ttwid",
		Value: ttwid,
	}
	socket.RequestHeader.Set("Cookie", cookie.String())

	socket.OnConnectError = func(err error, socket gowebsocket.Socket) {
		log.Fatal("Recieved connect error ", err)
	}
	socket.OnConnected = func(socket gowebsocket.Socket) {
		log.Println("Connected to server")
	}
	socket.OnTextMessage = func(message string, socket gowebsocket.Socket) {
		log.Println("Recieved message  " + message)
	}
	socket.OnPingReceived = func(data string, socket gowebsocket.Socket) {
		log.Println("Recieved ping " + data)
	}
	socket.OnDisconnected = func(err error, socket gowebsocket.Socket) {
		log.Println("Disconnected from server ")
		return
	}
	socket.Connect()

	i := 0
	for i < 10 {
		socket.SendText("This is my sample test message")
		i++
	}

	for {
		select {
		case <-interrupt:
			log.Println("interrupt")
			socket.Close()
			return
		}
	}
}

package v1

import (
	"center-ai/enums"
	"center-ai/middleware"
	"center-ai/model"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/myredis"
	"center-ai/utils/tools"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"net/http"
	"strconv"
	"time"
)

type UserInfoResp struct {
	UserId         uint   `json:"user_id"`
	Username       string `json:"username"`
	AvatarUrl      string `json:"avatar_url"`
	Mobile         string `json:"mobile"`
	InvitationCode string `json:"invitation_code"`
	Coin           int    `json:"coin"`
	MemberType     int    `json:"member_type"`
	MemberExpires  string `json:"member_expires"`
}

type userApi_ struct {
}

type resetUsernameReq struct {
	Username string `json:"username"`
}

type resetMobileReq struct {
	Mobile  string `json:"mobile"`
	SmsCode string `json:"sms_code"`
}

type resetPasswordReq struct {
	Mobile   string `json:"mobile"`
	Password string `json:"password"`
	SmsCode  string `json:"sms_code"`
}

func (obj userApi_) GetUserInfo(c *gin.Context) {
	var code int
	var msg string

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	var user model.User
	if err := user.GetByID(claims.UserId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	avatarUrl := ""
	//if user.Avatar != "" {
	//	avatarUrl = config.DiffusionDomain + service.ImgService.GetSmallImagePath(user.Avatar)
	//}

	mobile := user.Mobile

	resp := UserInfoResp{
		UserId:    user.ID,
		Username:  user.Username,
		AvatarUrl: avatarUrl,
		Mobile:    tools.FormatMobileStar(mobile),
	}

	result := make(map[string]interface{})
	result["user"] = resp

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj userApi_) ChangeUsername(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	var oReq resetUsernameReq

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if len(oReq.Username) < 2 {
		errmsg.Abort(c, errmsg.FAIL, "用户名格式不正确")
		return
	}

	userId := claims.UserId
	var user model.User
	if err := user.GetByID(userId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	if exist, err := user.ExistsUsername(oReq.Username); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "验证用户名失败")
		return
	} else {
		if exist {
			errmsg.Abort(c, errmsg.FAIL, "该用户名已经存在")
			return
		} else {
			if er := user.SetUsername(oReq.Username); er != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "更新用户名失败")
			} else {
				result := make(map[string]interface{})
				result["username"] = oReq.Username
				c.JSON(http.StatusOK, gin.H{
					"code":   code,
					"msg":    "用户名更改成功",
					"result": result,
				})
			}
		}
	}

}

func (obj userApi_) ChangeMobile(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	var oReq resetMobileReq

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if len(oReq.Mobile) != 11 {
		errmsg.Abort(c, errmsg.FAIL, "手机号码格式不正确")
		return
	}
	if len(oReq.SmsCode) < 4 {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	userId := claims.UserId
	var user model.User
	if err := user.GetByID(userId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	if user.Mobile == oReq.Mobile {
		errmsg.Abort(c, errmsg.FAIL, "手机号码未变更")
		return
	}

	redisKey := enums.RedisKeyEnum.SmsModifyPassword + oReq.Mobile
	testCount := myredis.Get(redisKey + ":testcount")
	if testCount == "" {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请10分钟后重试")
		return
	}
	iTestCount, _ := strconv.Atoi(testCount)
	if iTestCount > 10 {
		errmsg.Abort(c, errmsg.FAIL, "验证码尝试次数过多，请10分钟后重试")
		return
	}
	iTestCount = iTestCount + 1
	if err := myredis.Set(redisKey+":testcount", strconv.Itoa(iTestCount), time.Minute*10); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "设置短信验证码尝试参数失败")
		return
	}

	v := myredis.Get(redisKey)
	if v == "" {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请重试")
		return
	}
	if v != oReq.SmsCode {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	if exist, err := user.ExistsMobile(oReq.Mobile); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "查询手机号码出错")
		return
	} else {
		if exist {
			errmsg.Abort(c, errmsg.FAIL, "手机号码已经存在")
			return
		} else {
			if er := user.SetMobile(oReq.Mobile); er != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "更新手机号码失败")
			} else {
				result := make(map[string]interface{})
				result["mobile"] = oReq.Mobile
				c.JSON(http.StatusOK, gin.H{
					"code":   code,
					"msg":    "号码绑定成功",
					"result": result,
				})
				if err2 := myredis.Del(enums.RedisKeyEnum.SmsModifyPassword + user.Mobile); err2 != nil {
					logger.Error(err2)
				}
			}
		}
	}

}

func (obj userApi_) ChangePasswordByMobile(c *gin.Context) {
	var oReq resetPasswordReq

	err := c.ShouldBindBodyWith(&oReq, binding.JSON)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "获取数据失败")
		return
	}

	if oReq.Mobile == "" {
		errmsg.Abort(c, errmsg.FAIL, "请输入手机号码")
		return
	}
	var user model.User
	if err := user.GetByMobile(oReq.Mobile); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询数据失败")
		return
	}
	if user.ID == 0 {
		errmsg.Abort(c, errmsg.FAIL, "数据不存在")
		return
	}

	claims, _ := middleware.GetMyClaims(user.ID, "", "")

	c.Set("claims", &claims)
	obj.ChangePassword(c)
}

func (obj userApi_) ChangePassword(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	var oReq resetPasswordReq
	err := c.ShouldBindBodyWith(&oReq, binding.JSON)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if len(oReq.SmsCode) != 4 {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	if len(oReq.Password) < 5 {
		errmsg.Abort(c, errmsg.FAIL, "密码不能小于5位")
		return
	}

	var user model.User
	if err := user.GetByID(claims.UserId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	redisKey := enums.RedisKeyEnum.SmsModifyPassword + user.Mobile
	testCount := myredis.Get(redisKey + ":testcount")
	if testCount == "" {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请10分钟后重试")
		return
	}
	iTestCount, _ := strconv.Atoi(testCount)
	if iTestCount > 10 {
		errmsg.Abort(c, errmsg.FAIL, "验证码尝试次数过多，请10分钟后重试")
		return
	}
	iTestCount = iTestCount + 1
	if err := myredis.Set(redisKey+":testcount", strconv.Itoa(iTestCount), time.Minute*10); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "设置短信验证码尝试参数失败")
		return
	}

	v := myredis.Get(redisKey)
	if len(v) == 0 {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请重试")
		return
	}
	if v != oReq.SmsCode {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	user.Password = oReq.Password
	if err := user.SetPassword(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "修改密码失败")
		return
	}

	//result := make(map[string]interface{})
	//result["username"] = oReq.Username

	c.JSON(http.StatusOK, gin.H{
		"code": code,
		"msg":  "密码修改成功",
	})

}

var UserApi userApi_

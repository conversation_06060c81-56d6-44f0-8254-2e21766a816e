package enums

type classRoomEnum_ struct {
	Unknow, Makeing, Auditing, AuditReject, AuditPass int
}

var ClassRoomEnum classRoomEnum_

func (obj classRoomEnum_) StuUserStatusName(v int) string {
	if val, ok := StuUserStatusName[v]; ok {
		//存在
		return val
	}
	return ""
}

func (obj classRoomEnum_) StuUserStatusExplain(v int) string {
	if val, ok := StuUserStatusName[v]; ok {
		//存在
		return val
	}
	return ""
}

var StuUserStatusName = map[int]string{
	0: "无效",
	1: "有效",
	2: "暂停",
}

var StuUserStatusExplain = map[int]string{
	0: "学员不可见",
	1: "学员可见，可以正常使用",
	2: "学员可见，但是不能使用",
}

func (obj classRoomEnum_) StatusName(v int) string {
	if val, ok := StatusName[v]; ok {
		//存在
		return val
	}
	return ""
}

var StatusName = map[int]string{
	0: "未上线",
	1: "已上线",
	2: "暂时不可用",
}

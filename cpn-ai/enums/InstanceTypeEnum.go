package enums

type instanceTypeEnum_ struct {
	Normal, System, Kol, CCM int
}

var InstanceTypeEnum = instanceTypeEnum_{
	Normal: 0, //普通
	System: 1, //系统
	Kol:    3, //镜像制作
	CCM:    9,
}

func (obj instanceTypeEnum_) Name(v int) string {
	if val, ok := InstanceTypeName[v]; ok {
		//存在
		return val
	}
	return ""
}

var InstanceTypeName = map[int]string{
	0: "普通",
	1: "系统",
	3: "镜像制作",

	9: "算力市场",
}

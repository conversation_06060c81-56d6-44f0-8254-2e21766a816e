package enums

type businessTypeEnum_ struct {
	PodInstance, PodService, ImageStore, CloudStore,
	VerifyWithdrawAccount, Withdraw int
}

var BusinessTypeEnum = businessTypeEnum_{
	PodInstance: 1, //Instance表
	PodService:  2, //Instance
	ImageStore:  3, //算云镜像存储
	CloudStore:  4, //算云云存储

	VerifyWithdrawAccount: 5, //验证提现账户
	Withdraw:              6, //提现
}

func (obj businessTypeEnum_) Name(v int) string {
	if val, ok := BusinessTypeNameEnum[v]; ok {
		//存在
		return val
	}

	return ""
}

var BusinessTypeNameEnum = map[int]string{
	1: "算力费用",
	2: "Pod服务费",
	3: "算云镜像存储",
	4: "算云云存储",

	5: "验证提现账户",
	6: "提现",
}

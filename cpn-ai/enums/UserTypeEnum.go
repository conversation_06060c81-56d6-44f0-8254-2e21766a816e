package enums

type userTypeEnum_ struct {
	Unknow, <PERSON><PERSON><PERSON>, System, Other, Kol int
}

var UserTypeEnum = userTypeEnum_{
	Unknow:  0, //未知
	Suanyun: 1, //
	System:  2, //
	Other:   3, //
	Kol:     4, //认证Kol
}

func (obj userTypeEnum_) Name(v int) string {
	if val, ok := UserTypeNameEnum[v]; ok {
		//存在
		return val
	}
	return ""
}

var UserTypeNameEnum = map[int]string{
	0: "未知",
	1: "算云",
	2: "系统",
	3: "第三方",
	4: "Kol",
}

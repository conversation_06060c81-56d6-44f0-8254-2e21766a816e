package enums

type imageTypeEnum_ struct {
	Base, Public, Private, PrivateInstance, CCM int
}

var ImageTypeEnum = imageTypeEnum_{
	Base:            1, //基础镜像
	Public:          2, //Kol镜像
	Private:         3, //个人镜像
	PrivateInstance: 4, //个人实例镜像
	CCM:             9, //算力市场
}

func (obj imageTypeEnum_) Name(v int) string {
	if val, ok := ImageTypeName[v]; ok {
		//存在
		return val
	}
	return ""
}

var ImageTypeName = map[int]string{
	1: "基础镜像",
	//2: "晨羽POD",
	2: "应用商店",
	3: "个人镜像",
	4: "个人实例镜像",
	9: "算力市场",
}

package enums

type shutdownTypeEnum_ struct {
	ShutdownDestroy, ShutdownSaveDestroy, ShutdownKeep int
}

var ShutdownTypeEnum = shutdownTypeEnum_{
	ShutdownDestroy:     1, //已关机/直接销毁并停止计费
	ShutdownSaveDestroy: 2, //已关机/存储后销毁并停止计费
	ShutdownKeep:        3, //已关机/保留Gpu继续计费
}

func (obj shutdownTypeEnum_) Name(v int) string {
	if val, ok := InstanceStatusName[v]; ok {
		//存在
		return val
	}
	return ""
}

var ShutdownTypeName = map[int]string{
	1: "直接销毁并停止计费",
	2: "存储后销毁并停止计费",
	3: "保留Gpu继续计费",
}

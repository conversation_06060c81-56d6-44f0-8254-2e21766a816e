package enums

import "reflect"

type payChannelEnum_ struct {
	AliPay, WechatPay, EnterpriseNetPay, AppleIap, OtherTransfer, BackRecharge, PersonalNetPay string
}

var PayChannelEnum = payChannelEnum_{
	AliPay:           "0",
	WechatPay:        "1",
	EnterpriseNetPay: "2",
	AppleIap:         "3",
	OtherTransfer:    "8",
	BackRecharge:     "9",
	PersonalNetPay:   "10",
}

var PayChannelNameEnum = payChannelEnum_{
	AliPay:           "支付宝",
	WechatPay:        "微信支付",
	AppleIap:         "IOS iap支付",
	EnterpriseNetPay: "企业网银",
	OtherTransfer:    "其他账户划账",
	BackRecharge:     "后台充值",
	PersonalNetPay:   "个人网银",
}

func PayChannelName(i string) string {
	if i == PayChannelEnum.AliPay {
		return PayChannelNameEnum.AliPay
	} else if i == PayChannelEnum.WechatPay {
		return PayChannelNameEnum.WechatPay
	} else if i == PayChannelEnum.AppleIap {
		return PayChannelNameEnum.AppleIap
	} else if i == PayChannelEnum.EnterpriseNetPay {
		return PayChannelNameEnum.EnterpriseNetPay
	} else if i == PayChannelEnum.OtherTransfer {
		return PayChannelNameEnum.OtherTransfer
	} else if i == PayChannelEnum.BackRecharge {
		return PayChannelNameEnum.BackRecharge
	} else if i == PayChannelEnum.PersonalNetPay {
		return PayChannelNameEnum.PersonalNetPay
	}
	return ""
}

func (c payChannelEnum_) GetKey(value string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if vo.Field(i).Interface().(string) == value {
			return typeVo.Field(i).Name
		}
	}
	return ""
}

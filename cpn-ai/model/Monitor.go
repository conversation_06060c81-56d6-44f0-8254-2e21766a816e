package model

import (
	"gorm.io/gorm"
	"time"
)

type Monitor struct {
	gorm.Model
	KolData     string    `json:"kol_data" gorm:"type:json;comment:Kol统计数据"`
	UsageData   string    `json:"usage_data" gorm:"type:json;comment:按量用户统计数据"`
	InsiderData string    `json:"insider_data" gorm:"type:json;comment:按量内部用户统计数据"`
	PodData     string    `json:"pod_data" gorm:"type:json;comment:Pod统计数据"`
	GpuData     string    `json:"gpu_data" gorm:"type:json;comment:显卡数据"`
	MonitorTime time.Time `json:"monitor_time" gorm:"type:datetime;default:'1900-01-01';comment:监测时间"`
	MonitorAt   int64     `json:"monitor_at" gorm:"type:bigint;not null;default:0;comment:监测时间;unique"`
}

func (Monitor) TableName() string {
	return "T_Monitor"
}

func (o *Monitor) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *Monitor) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["monitor_at"]; ok {
			tx.Where("monitor_at=?", queryParm["monitor_at"])
		}
		if _, ok := queryParm["start_at"]; ok {
			tx.Where("monitor_at>=?", queryParm["start_at"])
		}
		if _, ok := queryParm["end_at"]; ok {
			tx.Where("monitor_at<?", queryParm["end_at"])
		}
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("monitor_at asc").Limit(pageSize).Offset((page - 1) * pageSize)
	tx.Scan(dest)
	return total, tx.Error
}

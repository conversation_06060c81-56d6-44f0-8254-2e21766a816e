package model

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/structs"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
)

type transactions_ struct {
}

func (transactions *transactions_) ManageAddAmount(balance *AmountBalance) error {
	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if err := balance.New(tx); err != nil {
			return err
		}
		return nil
	})
	return nil
}

func (transactions *transactions_) SettleStore(userId uint, businessType int, settleTime time.Time, size decimal.Decimal) error {
	lockKey := enums.RedisKeyEnum.LockKey + "user_" + utils.Uint2String(userId)
	if common.RedisLock(lockKey, 1, 0) {
		defer common.RedisUnLock(lockKey)
		return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
			var settleStore SettleStore
			if err := settleStore.New(tx, userId, businessType, settleTime, size); err != nil {
				logger.Error(err)
				return err
			} else {
				return nil
			}
		})
	} else {
		err := errors.New("等待超时")
		logger.Error(err)
		return err
	}
}

func (transactions *transactions_) BuyCard(cards []Card) error {
	if len(cards) == 0 {
		return errors.New("cards is empty")
	}
	card := cards[0]
	var coupon Coupon
	if err := coupon.GetById(card.CouponId); err != nil {
		logger.Error(err)
		return err
	}
	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if err := card.Create(tx, cards); err != nil {
			logger.Error(err)
			return err
		}
		if err := coupon.UpSoldCount(tx, len(cards)); err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
	return nil
}

func (transactions *transactions_) BuyCardSuccess(tx *gorm.DB, buyUserId uint, orderNo string, couponCode string, quantity int, autoBind int) error {
	var coupon Coupon
	if err := coupon.GetByCouponCode(couponCode); err != nil {
		msg := "优惠卷查询失败"
		logger.Error(msg, err, couponCode)
		return err
	}
	cardPrefix := coupon.CardPrefix
	digits := 16 - len(cardPrefix)

	cards := make([]Card, 0)
	for i := 0; i < quantity; i++ {
		cardNoLast := utils.GenerateRandomNumberStr(digits)
		cardNo := cardPrefix + cardNoLast
		if len(cardNo) != 16 {
			msg := "生成卡号失败"
			logger.Error(msg, "couponId:", coupon.ID)
			return errors.New("生成卡号失败")
		}
		card := Card{
			Uuid:        utils.GetUUID(),
			CouponId:    coupon.ID,
			CardNo:      cardNo,
			Title:       coupon.Title,
			ValidDays:   coupon.ValidDays,
			ExpireDate:  coupon.ExpireDate,
			BuyUserId:   buyUserId,
			SalePrice:   coupon.SalePrice,
			FacePrice:   coupon.FacePrice,
			LeaveAmount: coupon.FacePrice,
			PodIds:      coupon.PodIds,
			Pods:        coupon.Pods,
			Status:      1,
			OrderNo:     orderNo,
		}
		if autoBind == 1 {
			expireDate := time.Now().AddDate(0, 0, coupon.ValidDays)
			card.BindUserId = buyUserId
			card.BindTime = time.Now()
			card.ExpireDate = expireDate
		}
		cards = append(cards, card)
	}

	if len(cards) == 0 {
		return errors.New("cards is empty")
	}
	card := cards[0]

	if err := card.Create(tx, cards); err != nil {
		logger.Error(err)
		return err
	}
	if err := coupon.UpSoldCount(tx, len(cards)); err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

//func (transactions *transactions_) SetInstanceStatus2Running() error {
//	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
//		if err := tx.Model(o).Updates(Instance{Status: enums.InstanceStatusEnum.Running}).Error; err != nil {
//			logger.Error(err)
//			return err
//		}
//
//		if err := o.Status(tx); err != nil {
//			return err
//		}
//		return nil
//	})
//	return nil
//}

func (transactions *transactions_) CostAmountLLm(userId uint, cost decimal.Decimal, keyTime string) error {
	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		var balance AmountBalance
		orderNo := fmt.Sprintf("%s_%d_%s", enums.OrderNoPrefixEnum.CostLLM, userId, keyTime)
		if exists, err := balance.ExistsOrderNo(orderNo); err != nil {
			logger.Error(err)
			return err
		} else if exists {
			err = errors.New("该用户已经扣费")
			return err
		}
		if err := balance.GetBalanceObject(orderNo, 0, userId, enums.OrderTypeEnum.CostLLM, cost.Neg(), "大模型消费", fmt.Sprintf("keyTime:%s", keyTime), userId, "用户ID"); err != nil {
			logger.Error(err)
			return err
		}
		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (transactions *transactions_) RechargeSuccess(recharge *Recharge) error {
	logger.Info("支付成功事务逻辑开始", recharge.OutTradeNo)
	if recharge.State != 0 {
		logger.Error(recharge.ID, "state:", recharge.State)
		return errors.New("状态不正确")
	}

	if recharge.Action == enums.RechargeActionEnum.BuyCard {
		var param structs.RechargeCustomParam
		if err := utils.GetStructFromJson(&param, recharge.CustomParam); err != nil || param.Action == "" {
			msg := "参数错误"
			logger.Error(msg, " CustomParam:", recharge.CustomParam)
			return err
		}
		return transactions.RechargeBuySuccess(recharge)
	}

	var product RechargeProduct
	if recharge.ProductId > 0 {
		if err := product.GetById(recharge.ProductId); err != nil {
			logger.Error(err)
			return err
		}
	}

	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')

		balance := AmountBalance{
			UserId:         recharge.UserId,
			OperatorId:     recharge.UserId,
			OccurredAmount: recharge.Amount,
		}
		if recharge.PayChannel == enums.PayChannelEnum.AppleIap {
			err := errors.New("apple 支付逻辑不在这里")
			logger.Error(err)
			return err
		} else if recharge.PayChannel == enums.PayChannelEnum.AliPay {
			if err := recharge.SetPaySuccess(tx, recharge.PayTradeId, recharge.PayTime, recharge.PayCallbackJson); err != nil {
				logger.Error(err)
				return err
			}
		} else if recharge.PayChannel == enums.PayChannelEnum.WechatPay {
			if err := recharge.SetPaySuccess(tx, recharge.PayTradeId, recharge.PayTime, recharge.PayCallbackJson); err != nil {
				logger.Error(err)
				return err
			}
		} else {
			err := errors.New("未写业务逻辑")
			logger.Error(err)
			return err
		}

		if product.ProductCategory == enums.ProductCategoryEnum.Subscription {
			err := errors.New("未写订阅业务逻辑")
			logger.Error(err)
			return err
		} else {
			logger.Info("开始创建流水 充值类型 ", product.ProductCategory, "   ", recharge.OutTradeNo)
			if err := balance.GetBalanceObject(recharge.OutTradeNo, 0, recharge.UserId, enums.OrderTypeEnum.RechargeBuy, recharge.Amount, "算力充值", fmt.Sprintf("充值金额%s", recharge.Amount), recharge.UserId, "User"); err != nil {
				logger.Error(err)
				return err
			}
			if err := balance.New(tx); err != nil {
				logger.Error(err)
				return err
			} else {
				logger.Info("流水创建成功 充值类型 ", product.ProductCategory, "   ", recharge.OutTradeNo)
			}

			if len(recharge.CustomParam) > 10 {
				logger.Info("开始处理CustomParam ", recharge.OutTradeNo)
				//m := utils.GetMapFromJson(recharge.CustomParam)
				//action := 0
				//if v, ok := m["action"]; ok {
				//	action = int(v.(float64))
				//}
				//if v, ok := m["face_img_md5"]; ok && action == enums.RechargeActionEnum.BuyDigital {
				//
				//} else {
				//	logger.Error("face_img_md5 字段不存在", recharge.CustomParam)
				//}
			}
		}
		return nil
	})
	return nil
}

func (transactions *transactions_) RechargeBuySuccess(recharge *Recharge) error {
	logger.Info("支付成功事务逻辑开始", recharge.OutTradeNo)
	if recharge.State != 0 {
		logger.Error(recharge.ID, "state:", recharge.State)
		return errors.New("状态不正确")
	}

	var param structs.RechargeCustomParam
	if err := utils.GetStructFromJson(&param, recharge.CustomParam); err != nil || param.Action == "" {
		msg := "参数错误"
		logger.Error(msg, " CustomParam:", recharge.CustomParam)
		return err
	}

	//show := ""
	//remark := ""
	if param.Action == enums.RechargeActionEnum.BuyCard {
		//show = "购买算力卡"
		//remark = "算力优惠卷:" + param.CouponCode
		var coupon Coupon
		if err := coupon.GetByCouponCode(param.CouponCode); err != nil {
			msg := "优惠卷查询失败"
			logger.Error(msg, err, param.CouponCode)
			return err
		}
	} else {
		err := errors.New("未匹配到支付成功业务")
		logger.Error(err, "param.Action:", param.Action)
		return err
	}

	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')

		//balance := AmountBalance{
		//	UserId:         recharge.UserId,
		//	OperatorId:     recharge.UserId,
		//	OccurredAmount: recharge.Amount,
		//}
		if recharge.PayChannel == enums.PayChannelEnum.AppleIap {
			err := errors.New("apple 支付逻辑不在这里")
			logger.Error(err)
			return err
		} else if recharge.PayChannel == enums.PayChannelEnum.AliPay {
			if err := recharge.SetPaySuccess(tx, recharge.PayTradeId, recharge.PayTime, recharge.PayCallbackJson); err != nil {
				logger.Error(err)
				return err
			}
		} else if recharge.PayChannel == enums.PayChannelEnum.WechatPay {
			if err := recharge.SetPaySuccess(tx, recharge.PayTradeId, recharge.PayTime, recharge.PayCallbackJson); err != nil {
				logger.Error(err)
				return err
			}
		} else {
			err := errors.New("未写业务逻辑")
			logger.Error(err)
			return err
		}

		{
			//logger.Info("开始创建流水 充值类型 ", "   recharge.OutTradeNo:", recharge.OutTradeNo)
			//if err := balance.GetBalanceObject(recharge.OutTradeNo, 0, recharge.UserId, enums.OrderTypeEnum.RechargeBuy, recharge.Amount, show, remark, recharge.UserId, "User"); err != nil {
			//	logger.Error(err)
			//	return err
			//}
			//if err := balance.New(tx); err != nil {
			//	logger.Error(err)
			//	return err
			//} else {
			//	logger.Info("流水创建成功 充值类型 ", "   recharge.OutTradeNo:", recharge.OutTradeNo)
			//}
			if param.Action == enums.RechargeActionEnum.BuyCard {
				if err := transactions.BuyCardSuccess(tx, recharge.UserId, recharge.OutTradeNo, param.CouponCode, param.Quantity, param.AutoBind); err != nil {
					logger.Error(err)
					return err
				}
			} else {
				err := errors.New("未知的支付类型")
				logger.Error(err, param.Action)
				return err
			}
		}
		return nil
	})

}

var Transactions transactions_

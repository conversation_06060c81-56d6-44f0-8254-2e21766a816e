package model

import (
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
)

type GpuModel struct {
	gorm.Model
	Uuid       string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID"`
	Title      string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:显卡标题"`
	GpuName    string `json:"gpu_name" gorm:"type:varchar(50);not null;default:'';comment:显卡名称"`
	Desc       string `json:"desc" gorm:"type:varchar(1500);not null;default:'';comment:说明"`
	MemoryG    int    `json:"memory_g" gorm:"type:int;not null;default:0;comment:显存(单位G)"`
	MemoryM    int    `json:"memory_m" gorm:"type:int;not null;default:0;comment:显存(单位M)"`
	OrderIndex int    `json:"order_index" gorm:"type:int;not null;default:0;comment:排序越小越前面"`
	Price      string `json:"price" gorm:"type:varchar(200);not null;default:'';comment:单价"`
	Remark     string `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	Status     int    `json:"status" gorm:"type:int;not null;default:0;comment:状态"`
}

//Price {"hour":1.66,"day":38.65,"week":245.43,"month":847.80}

func (GpuModel) TableName() string {
	return "T_GpuModel"
}

func (o *GpuModel) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *GpuModel) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *GpuModel) GetByGpuName(gpuName string) error {
	return DB.First(o, "gpu_name=?", gpuName).Error
}

func (o *GpuModel) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}

func (o *GpuModel) List(dest interface{}, id uint, gpuName string, status int, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	} else {
		if gpuName != "" {
			tx.Where("gpu_name=?", gpuName)
		}
		if status >= 0 {
			tx.Where("status=?", status)
		}
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("CASE WHEN order_index = 0 THEN 10000 ELSE order_index END, order_index asc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *GpuModel) CalculateAmount(chargingType int, num int, gups int) decimal.Decimal {

	var m map[string]interface{}
	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(o.Price), &m)
	if err != nil {
		logger.Error(err, o.Price)
		return decimal.Zero
	}
	amount := decimal.Zero
	if chargingType == enums.ChargingTypeEnum.Usage { //num是秒数
		if v, ok := m["hour"]; ok {
			hourPrice := decimal.NewFromFloat(v.(float64))

			duration := time.Second * time.Duration(num)
			hours := int64(duration.Hours())
			remainingSeconds := int(duration.Seconds()) % 3600
			if hours > 0 {
				amount = hourPrice.Mul(decimal.NewFromInt(hours))
			}
			if remainingSeconds > 0 {
				tmp := hourPrice.Mul(decimal.NewFromInt(int64(remainingSeconds))).Div(decimal.NewFromInt(3600))
				amount = amount.Add(tmp)
			}
		}
	}
	if chargingType == enums.ChargingTypeEnum.Day {
		if v, ok := m["day"]; ok {
			tmp := decimal.NewFromFloat(v.(float64))
			amount = tmp.Mul(decimal.NewFromInt(int64(num)))
		}
	} else if chargingType == enums.ChargingTypeEnum.Week {
		if v, ok := m["week"]; ok {
			tmp := decimal.NewFromFloat(v.(float64))
			amount = tmp.Mul(decimal.NewFromInt(int64(num)))
		}
	} else if chargingType == enums.ChargingTypeEnum.Month {
		if v, ok := m["month"]; ok {
			tmp := decimal.NewFromFloat(v.(float64))
			amount = tmp.Mul(decimal.NewFromInt(int64(num)))
		}
	}
	amount = amount.Mul(decimal.NewFromInt(int64(gups)))
	return amount
	//amount1 := o.DecimalTruncateRound(amount)
	//logger.Info("CalculateAmount：", amount.String(), "      ", amount1.String())
	//return amount1
}

func (o *GpuModel) UnitPrice(chargingType int) decimal.Decimal {

	var m map[string]interface{}
	// 反序列化 JSON 字符串
	err := json.Unmarshal([]byte(o.Price), &m)
	if err != nil {
		logger.Error(err, o.Price)
		return decimal.Zero
	}
	amount := decimal.Zero
	if chargingType == enums.ChargingTypeEnum.Usage { //num是秒数
		if v, ok := m["hour"]; ok {
			amount = decimal.NewFromFloat(v.(float64))
		}
	}
	if chargingType == enums.ChargingTypeEnum.Day {
		if v, ok := m["day"]; ok {
			amount = decimal.NewFromFloat(v.(float64))

		}
	} else if chargingType == enums.ChargingTypeEnum.Week {
		if v, ok := m["week"]; ok {
			amount = decimal.NewFromFloat(v.(float64))
		}
	} else if chargingType == enums.ChargingTypeEnum.Month {
		if v, ok := m["month"]; ok {
			amount = decimal.NewFromFloat(v.(float64))
		}
	}
	return amount
}

func (o *GpuModel) CalculateCostedAmount(chargingType int, startTime time.Time, settleTime time.Time, gpus int) decimal.Decimal {
	txt := ""
	defer logger.Info("CalculateUseAmount:", chargingType, " ", startTime, " ", txt)
	startTime = startTime.Truncate(time.Second)
	settleTime = settleTime.Truncate(time.Second)
	var m map[string]interface{}
	// 反序列化 JSON 字符串
	if err := json.Unmarshal([]byte(o.Price), &m); err != nil {
		logger.Error(err, o.Price)
		return decimal.Zero
	}
	if settleTime.Before(startTime) {
		err := errors.New("时间参数错误")
		logger.Error(err, " ", startTime, "  ", settleTime)
	}

	duration := settleTime.Sub(startTime)

	// 计算天数
	days := int(duration.Hours() / 24)

	// 计算余下的秒数
	seconds := int(duration.Seconds()) % (24 * 60 * 60)
	amount := decimal.Zero
	if seconds > 0 { //num是秒数
		if v, ok := m["hour"]; ok {
			tmp := decimal.NewFromFloat(v.(float64))
			amount = tmp.Mul(decimal.NewFromInt(int64(seconds))).Div(decimal.NewFromInt(3600))
			txt += fmt.Sprintf("%d秒：%s", seconds, amount.String())
		} else {
			logger.Error("hour 价格不存在")
			return decimal.Zero
		}
	}

	dayPrice := decimal.Zero
	weekPrice := decimal.Zero
	monthPrice := decimal.Zero
	if v, ok := m["day"]; ok {
		dayPrice = decimal.NewFromFloat(v.(float64))
	} else {
		logger.Error("day 价格不存在")
		return decimal.Zero
	}

	if v, ok := m["week"]; ok {
		weekPrice = decimal.NewFromFloat(v.(float64))
	} else {
		logger.Error("week 价格不存在")
		return decimal.Zero
	}

	if v, ok := m["month"]; ok {
		monthPrice = decimal.NewFromFloat(v.(float64))
	} else {
		logger.Error("month 价格不存在")
		return decimal.Zero
	}

	if chargingType == enums.ChargingTypeEnum.Day {
		dayAmount := dayPrice.Mul(decimal.NewFromInt(int64(days)))
		txt = fmt.Sprintf("%d天：%s", days, dayAmount.String()) + "," + txt
		amount = amount.Add(dayAmount)

		//return amount
	} else if chargingType == enums.ChargingTypeEnum.Week {
		weeks := days / 7
		leaveDays := days % 7
		daysAmount := dayPrice.Mul(decimal.NewFromInt(int64(leaveDays)))
		txt = fmt.Sprintf("%d天：%s", leaveDays, daysAmount.String()) + "," + txt
		amount = amount.Add(daysAmount)

		weeksAmount := weekPrice.Mul(decimal.NewFromInt(int64(weeks)))
		txt = fmt.Sprintf("%d周：%s", weeks, weeksAmount.String()) + "," + txt
		amount = amount.Add(weeksAmount)
		//return amount
	} else if chargingType == enums.ChargingTypeEnum.Month && monthPrice.GreaterThanOrEqual(decimal.Zero) {
		weeks := days / 7
		leaveDays := days % 7
		daysAmount := dayPrice.Mul(decimal.NewFromInt(int64(leaveDays)))
		txt = fmt.Sprintf("%d天：%s", leaveDays, daysAmount.String()) + "," + txt
		amount = amount.Add(daysAmount)

		weeksAmount := weekPrice.Mul(decimal.NewFromInt(int64(weeks)))
		txt = fmt.Sprintf("%d周：%s", weeks, weeksAmount.String()) + "," + txt
		amount = amount.Add(weeksAmount)
		//return amount
	}
	amount = amount.Mul(decimal.NewFromInt(int64(gpus)))
	return amount
	//amount1 := o.DecimalTruncateRound(amount)
	//logger.Info("CalculateAmount：", amount.String(), "      ", amount1.String())
	//return amount1
}

func (o *GpuModel) DecimalTruncateRound(amount decimal.Decimal) decimal.Decimal { //保留两位小数，只要有余数都入位
	amount1 := amount.Truncate(2)
	if amount.Sub(amount1).GreaterThan(decimal.Zero) {
		amount1 = amount1.Add(decimal.NewFromFloat(0.01))
	}
	return amount1
}

package model

import (
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"strings"
)

type ClassRoom struct {
	gorm.Model
	Uuid         string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID;uniqueIndex"`
	UserId       uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	Title        string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	Des          string `json:"des" gorm:"type:varchar(1500);not null;default:'';comment:简介"`
	Markdown     string `json:"markdown" gorm:"type:text;comment:详情页markdown格式"`
	Cover        string `json:"cover" gorm:"type:varchar(100);not null;default:'';comment:封面地址"`
	Logo         string `json:"logo" gorm:"type:varchar(100);not null;default:'';comment:logo地址"`
	PodIds       string `json:"pod_ids" gorm:"type:varchar(500);not null;default:'';comment:相关的PodIDs"`
	Remark       string `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	OnlineCount  int    `json:"online_count" gorm:"type:int;not null;default:0;comment:在线人数"`
	AuditStatus  int    `json:"audit_status" gorm:"type:int;not null;default:0;comment:审核状态"` //
	AuditContent string `json:"audit_content" gorm:"type:json;comment:审核内容"`
	Status       int    `json:"status" gorm:"type:int;not null;default:0;comment:状态 0未上线 1已上线 2暂时不可用"`
}

func (ClassRoom) TableName() string {
	return "T_ClassRoom"
}

func (o *ClassRoom) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *ClassRoom) Delete() error {
	return DB.Debug().Unscoped().Delete(o).Error
}

func (o *ClassRoom) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *ClassRoom) GetByIdFromCache(id uint) error {

	key := fmt.Sprintf("model_class_room_%d", id)
	if data, err := cache.Get(key); err == nil {
		if err := json.Unmarshal(data, o); err == nil {
			return nil
		} else {
			logger.Error(key, " err:", err)
		}
	}

	if err := DB.First(o, id).Error; err != nil {
		return err
	} else {
		// 将结果缓存
		if data, err := json.Marshal(*o); err == nil {
			if err := cache.Set(key, data); err == nil {
				return nil
			}
		}
		return nil
	}
}

func (o *ClassRoom) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}

func (o *ClassRoom) GetsByUuids(dest interface{}, uuids string) error {
	inAry := make([]string, 0)
	if uuids != "" {
		ary := strings.Split(uuids, "|")
		for _, val := range ary {
			if val != "" {
				inAry = append(inAry, val)
			}
		}
	}
	if len(inAry) == 0 {
		return nil
	}
	tx := DB.Debug().Model(o).Where("uuid in (?)", inAry)
	tx.Scan(dest)
	return tx.Error
}

func (o *ClassRoom) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}

		if _, ok := queryParm["class_room_ids"]; ok {
			tx.Where("id in (?)", queryParm["class_room_ids"].([]uint))
		}

		if _, ok := queryParm["pod_uuid"]; ok {
			tx.Where("pod_uuid=?", queryParm["pod_uuid"])
		}

		if _, ok := queryParm["status"]; ok {
			tx.Where(fmt.Sprintf("status %s", queryParm["status"].(string)))
		}
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	tx.Scan(dest)
	return total, tx.Error
}

func (o *ClassRoom) SetLogo(path string) error {
	return DB.Model(o).Updates(ClassRoom{Logo: path}).Error
}
func (o *ClassRoom) SetCover(path string) error {
	return DB.Model(o).Updates(ClassRoom{Cover: path}).Error
}
func (o *ClassRoom) SetMarkdown(txt string) error {
	return DB.Model(o).Updates(ClassRoom{Markdown: txt}).Error
}

func (o *ClassRoom) SetAuditStatus(status int) error {
	return DB.Model(o).Updates(ClassRoom{AuditStatus: status}).Error
}

func (o *ClassRoom) SetStatus(status int) error {
	return DB.Model(o).Update("status", status).Error
}

func (o *ClassRoom) SetPodIds(podIds string) error {
	oldPodIds := o.PodIds
	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		oldAryPodId := strings.Split(oldPodIds, "|")
		for _, val := range oldAryPodId {
			if oldPodId := utils.String2Uint(val); oldPodId > 0 {
				if !strings.Contains(podIds, fmt.Sprintf("|%d|", oldPodId)) {
					//pod 移除
					var pod Pod
					if err := tx.First(&pod, oldPodId).Error; err != nil {
						return err
					} else {
						if err := pod.RemoveClassRoomId(tx, o.ID); err != nil {
							return err
						}
					}
				}
			}
		}

		aryPodId := strings.Split(podIds, "|")
		for _, val := range aryPodId {
			if podId := utils.String2Uint(val); podId > 0 {
				var pod Pod
				if err := tx.First(&pod, podId).Error; err != nil {
					return err
				} else {
					if err := pod.AddClassRoomId(tx, o.ID); err != nil {
						return err
					}
				}
			}
		}
		return tx.Model(o).Update("pod_ids", podIds).Error
	})
}

func (o *ClassRoom) SetAuditContent(txt string) error {
	return DB.Model(o).Updates(ClassRoom{AuditContent: txt, AuditStatus: enums.PodAuditStatusEnum.Makeing}).Error
}

func (o *ClassRoom) Updates(m map[string]interface{}) error {
	return DB.Model(o).Debug().Updates(m).Error
}

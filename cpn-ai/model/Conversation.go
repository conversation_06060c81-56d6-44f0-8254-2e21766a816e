package model

import (
	"cpn-ai/common/logger"
	"errors"
	"gorm.io/gorm"
	"strings"
)

//会话

type Conversation struct {
	gorm.Model
	Uuid    string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串Id"`
	UserId  uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	TokenId uint   `json:"token_id" gorm:"type:bigint;not null;default:0;comment:TokenID"`
	Title   string `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	Mapping string `json:"mapping" gorm:"type:json;comment:对话内容"`
}

func (Conversation) TableName() string {
	return "T_Conversation"
}

func (o *Conversation) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *Conversation) Delete() error {
	return DB.Delete(o).Error
}

func (o *Conversation) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Conversation) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}

func (o *Conversation) List(dest interface{}, uuid string, userId uint, tokenId uint, kw string, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o).Where("user_id=?", userId).Order("id desc")
	if uuid != "" {
		tx.Where("uuid=?", uuid)
	} else {
		tx.Select("uuid, title, created_at,updated_at") //排除 mapping这个大字段
	}
	if tokenId >= 0 {
		tx.Where("token_id=?", tokenId)
	}
	if len(kw) > 0 {
		tx.Where("title like ?", "%"+kw+"%")
	}
	if err := tx.Count(&total).Error; err != nil {
		return 0, err
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Conversation) SetTitle(title string) error {
	return DB.Model(o).Updates(Conversation{Title: title}).Error
}

func (o *Conversation) AppendMapping(jsonObject map[string]interface{}) error {

	var fields []interface{}
	for key, value := range jsonObject {
		fields = append(fields, key, value)

		// 判断值的类型是否是字符串
		switch v := value.(type) {
		case string:
			// 如果是字符串类型，并且以"base64:"开头，记录错误日志
			if strings.HasPrefix(v, "base64:") {
				logger.Error(v, "val 错误", jsonObject)
				return errors.New("数据转换出错,不保存")
			}
			if strings.HasPrefix(value.(string), "base64:") { //base64:type15:OWJmNzEwZTVhMmNiNGU5NGI0NTRhMzNlZTY3ODRlZTY=
				logger.Error(value.(string), "val 错误", jsonObject)
				return errors.New("数据转换出错,不保存")
			}
		default:
			// 处理其他类型的值，你可以根据实际需求添加相应的逻辑
			logger.Warn("未处理的值类型", jsonObject)
		}

	}

	// Prepare SQL query with placeholder for JSON string
	sql := `UPDATE T_Conversation SET mapping = JSON_ARRAY_APPEND(mapping, '$', JSON_OBJECT(?)) WHERE id = ?`

	// Execute the SQL query
	result := DB.Exec(sql, fields, o.ID)

	// Check for errors
	if result.Error != nil {
		// Handle the error
		return result.Error
	}

	return nil
}

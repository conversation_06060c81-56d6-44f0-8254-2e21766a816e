package model

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// Invoice 发票信息表
type Invoice struct {
	gorm.Model
	UserId      uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	Amount      decimal.Decimal `json:"amount" gorm:"type:decimal(16,2);not null;default:0;comment:发票金额"`
	CompanyName string          `json:"company_name" gorm:"type:varchar(255);not null;default:'';comment:发票抬头公司名称"`
	TaxId       string          `json:"tax_id" gorm:"type:varchar(100);not null;default:'';comment:发票税号"`
	Email       string          `json:"email" gorm:"type:varchar(100);not null;default:'';comment:接收发票的邮箱"`
	Address     string          `json:"address" gorm:"type:varchar(255);default:'';comment:公司地址"`
	Phone       string          `json:"phone" gorm:"type:varchar(20);default:'';comment:联系电话"`
	BankName    string          `json:"bank_name" gorm:"type:varchar(100);default:'';comment:开户银行"`
	BankAccount string          `json:"bank_account" gorm:"type:varchar(50);default:'';comment:银行账号"`
	Remark      string          `json:"remark" gorm:"type:varchar(255);default:'';comment:备注信息"`
	Status      int             `json:"status" gorm:"type:tinyint;not null;default:0;comment:状态 1:待处理 2:已开票 3:已拒绝"`
	IssueTime   *time.Time      `json:"issue_time" gorm:"comment:开票时间"`
	OperatorId  uint            `json:"operator_id" gorm:"type:bigint;not null;default:0;comment:处理人ID"`
	InvoiceType int             `json:"invoice_type" gorm:"type:tinyint;not null;default:0;comment:发票类型 0:增值税普通发票 1:增值税专用发票"`
	InvoiceUrl  string          `json:"invoice_url" gorm:"type:varchar(255);default:'';comment:发票附件地址"`
}

func (Invoice) TableName() string {
	return "T_Invoice"
}

// GetById 根据ID获取发票信息
func (o *Invoice) GetById(id uint) error {
	err := DB.First(o, id).Error
	return err
}

// GetByOutTradeNo 根据订单号获取发票信息
func (o *Invoice) GetByOutTradeNo(outTradeNo string) error {
	err := DB.Debug().First(o, "out_trade_no = ?", outTradeNo).Error
	return err
}

// ListByUserId 获取用户的发票申请列表
func (o *Invoice) ListByUserId(dest interface{}, userId uint, status int, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o).Where("user_id = ?", userId)

	if status > 0 {
		tx.Where("status = ?", status)
	}

	if err := tx.Count(&total).Error; err != nil {
		return 0, err
	}

	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

// ListForAdmin 管理员查询发票申请列表
func (o *Invoice) ListForAdmin(dest interface{}, status int, kw string, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)

	if status > 0 {
		tx.Where("status = ?", status)
	}

	if len(kw) > 0 {
		tx.Where("company_name LIKE ? OR tax_id LIKE ? OR out_trade_no LIKE ?",
			"%"+kw+"%", "%"+kw+"%", "%"+kw+"%")
	}

	if err := tx.Count(&total).Error; err != nil {
		return 0, err
	}

	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

// Save 保存发票信息
func (o *Invoice) Save() error {
	return DB.Debug().Save(o).Error
}

// UpdateStatus 更新发票状态
func (o *Invoice) UpdateStatus(status int, operatorId uint, invoiceUrl string, remark string) error {
	now := time.Now()
	o.Status = status
	o.OperatorId = operatorId
	o.IssueTime = &now
	o.InvoiceUrl = invoiceUrl
	o.Remark = remark
	return DB.Debug().Save(o).Error
}

// CheckInvoiceExists 检查订单是否已申请过发票
func (o *Invoice) CheckInvoiceExists(outTradeNo string) (bool, error) {
	var count int64
	err := DB.Debug().Model(o).Where("out_trade_no = ?", outTradeNo).Count(&count).Error
	return count > 0, err
}

package model

import (
	"encoding/json"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"sort"
	"strconv"
	"time"
)

type StatDay struct {
	gorm.Model
	TotalRegisterUsers int `json:"total_register_users" gorm:"type:integer;not null;default:0;comment:总注册用户数"`
	TotalCertUsers     int `json:"total_cert_users" gorm:"type:integer;not null;default:0;comment:总认证用户数"`
	TotalMaleUsers     int `json:"total_male_users" gorm:"type:integer;not null;default:0;comment:总男性用户数"`
	TotalFemaleUsers   int `json:"total_female_users" gorm:"type:integer;not null;default:0;comment:总女性用户数"`
	//TotalAgeUsers             string          `json:"total_age_users" gorm:"type:json;comment:60 70 80 90 00 10 "`
	TotalCertStudentUsers     int             `json:"total_cert_student_users" gorm:"type:integer;not null;default:0;comment:总学生认证用户数"`
	TotalCertCompanyUsers     int             `json:"total_cert_company_users" gorm:"type:integer;not null;default:0;comment:总企业认证用户数"`
	TotalRechargeCount        int             `json:"total_recharge_count" gorm:"type:integer;not null;default:0;comment:总充值次数"`
	TotalRechargeUsers        int             `json:"total_recharge_users" gorm:"type:integer;not null;default:0;comment:总充值用户数"`
	TotalRechargeAmount       decimal.Decimal `json:"total_recharge_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:总充值金额"`
	TotalRechargeMaxAmount    decimal.Decimal `json:"total_recharge_max_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:最高充值金额"`
	TotalRechargeRemainUsers  int             `json:"total_recharge_remain_users" gorm:"type:integer;not null;default:0;comment:总余额充值用户数"`
	TotalRechargeRemainAmount decimal.Decimal `json:"total_recharge_remain_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:总余额充值金额"`
	TotalBuyCardUsers         int             `json:"total_buy_card_users" gorm:"type:integer;not null;default:0;comment:总购卡用户数"`
	TotalBuyCardAmount        decimal.Decimal `json:"total_buy_card_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:总购卡金额"`
	TotalCostAmount           decimal.Decimal `json:"total_cost_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:总消耗金额"`
	TotalCostRemainAmount     decimal.Decimal `json:"total_cost_remain_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:余额总消耗金额"`
	TotalCostCardAmount       decimal.Decimal `json:"total_cost_card_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:算力卡总消耗金额"`
	DayRegisterUsers          int             `json:"day_register_users" gorm:"type:integer;not null;default:0;comment:当日注册用户数"`
	DayCertUsers              int             `json:"day_cert_users" gorm:"type:integer;not null;default:0;comment:当日认证用户数"`
	DayRechargeCount          int             `json:"day_recharge_count" gorm:"type:integer;not null;default:0;comment:当日充值次数"`
	DayRechargeUsers          int             `json:"day_recharge_users" gorm:"type:integer;not null;default:0;comment:当日充值用户数"`
	DayRechargeRegisterUsers  int             `json:"day_recharge_register_users" gorm:"type:integer;not null;default:0;comment:当日注册并充值用户数"`
	DayRechargeFirstUsers     int             `json:"day_recharge_first_users" gorm:"type:integer;not null;default:0;comment:当日首次充值用户数"`
	DayRechargeRepeatUsers    int             `json:"day_recharge_repeat_users" gorm:"type:integer;not null;default:0;comment:当日复购充值用户数"`
	DayRechargeAmount         decimal.Decimal `json:"day_recharge_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:当日充值金额"`
	DayRechargeMaxAmount      decimal.Decimal `json:"day_recharge_max_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:当日最高充值金额"`
	DayRechargeRemainUsers    int             `json:"day_recharge_remain_users" gorm:"type:integer;not null;default:0;comment:当日余额充值用户数"`
	DayRechargeRemainAmount   decimal.Decimal `json:"day_recharge_remain_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:当日余额充值金额"`
	DayBuyCardUsers           int             `json:"day_buy_card_users" gorm:"type:integer;not null;default:0;comment:当日购卡用户数"`
	DayBuyCardAmount          decimal.Decimal `json:"day_buy_card_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:当日购卡金额"`
	DayCostAmount             decimal.Decimal `json:"day_cost_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:当日消耗金额"`
	DayCostRemainAmount       decimal.Decimal `json:"day_cost_remain_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:余额当日消耗金额"`
	DayCostCardAmount         decimal.Decimal `json:"day_cost_card_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:算力卡当日消耗金额"`
	DayCostInstAmount         decimal.Decimal `json:"day_cost_inst_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:当日算力消耗金额"`
	DayCostPodAmount          decimal.Decimal `json:"day_cost_pod_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:当日镜像使用消耗金额"`
	DayCostImageStore         decimal.Decimal `json:"day_cost_image_store" gorm:"type:decimal(16, 8);not null;default:0;comment:当日镜像存储消耗金额"`
	DayCostCloudStore         decimal.Decimal `json:"day_cost_cloud_store" gorm:"type:decimal(16, 8);not null;default:0;comment:当日云存储消耗金额"`
	DayCostOtherAmount        decimal.Decimal `json:"day_cost_other_amount" gorm:"type:decimal(16, 8);not null;default:0;comment:当日其他消耗金额"`
	DayonRegisterUsers        decimal.Decimal `json:"dayon_register_users" gorm:"type:decimal(16, 8);not null;default:0;comment:注册用户日环比"`
	DayonRechargeUsers        decimal.Decimal `json:"dayon_recharge_users" gorm:"type:decimal(16, 8);not null;default:0;comment:充值用户日环比"`
	DayPodUsageIds            int             `json:"day_pod_usage_ids" gorm:"type:integer;not null;default:0;comment:单日使用的不重复Pod数量"`
	DayPodUsageUsers          int             `json:"day_pod_usage_users" gorm:"type:integer;not null;default:0;comment:单日Pod的使用人数"`
	DayPodUsageCount          int             `json:"day_pod_usage_count" gorm:"type:integer;not null;default:0;comment:单日Pod的使用次数"`
	DayPodUsageDuration       uint            `json:"day_pod_usage_duration" gorm:"type:bigint;not null;default:0;comment:使用时长(以秒为单位)"`
	DayPodUsageData           string          `json:"day_pod_usage_data" gorm:"type:json;comment:单日Pod使用数据"`
	StatAt                    time.Time       `json:"stat_at" gorm:"type:datetime;default:'1900-01-01';comment:统计时间"`
	StatDay                   int64           `json:"stat_day" gorm:"type:bigint;not null;default:0;comment:统计日期;unique"`
	Status                    int             `json:"status" gorm:"type:tinyint;not null;default:0;comment:状态 0统计中 1结束统计"`
}

func (StatDay) TableName() string {
	return "T_StatDay"
}

func (o *StatDay) Save() error {
	if o.DayPodUsageData == "" {
		o.DayPodUsageData = "{}"
	}
	return DB.Debug().Save(o).Error
}

func (o *StatDay) GetByStatDay(statDay int64) error {
	err := DB.First(o, "stat_day=?", statDay).Error
	return err
}

func (o *StatDay) GetFirstNeedStat() error {
	err := DB.Debug().Order("stat_day asc").Limit(1).Where("status = 0").First(o).Error
	if err == gorm.ErrRecordNotFound {
		return o.GetLastStatDay()
	}
	return err
}

func (o *StatDay) GetLastStatDay() error {
	err := DB.Debug().Order("stat_day desc").Limit(1).First(o).Error
	return err
}

func (o *StatDay) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["stat_start"]; ok {
			tx.Where("stat_day>=?", queryParm["stat_start"])
		}
		if _, ok := queryParm["stat_end"]; ok {
			tx.Where("stat_day<=?", queryParm["stat_end"])
		}
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	order := "stat_day desc"
	if val, ok := queryParm["order"]; ok {
		if val.(string) == "StatDayAsc" {
			order = "stat_day asc"
		}
	}
	tx.Order(order).Limit(pageSize).Offset((page - 1) * pageSize)
	tx.Scan(dest)
	return total, tx.Error
}

func (o *StatDay) Updates(m map[string]interface{}) error {
	return DB.Model(o).Debug().Updates(m).Error
}

func (o *StatDay) StatTotalRegisterUsers(statAt time.Time) (int, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&User{}).
		Select("IFNULL(count(id),0) as total_count").
		Where("created_at<?", statEnd).
		Scan(&totalCount)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatTotalCertUsers(statAt time.Time) (int, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&Certify{}).
		Select("IFNULL(count(distinct user_id),0) as total_count").
		Where("status=1 and cert_time<?", statEnd).
		Scan(&totalCount)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatTotalRechargeCount(statAt time.Time) (int, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(count(user_id),0) as total_count").
		Where("state =1 and pay_time<?", statEnd).
		Scan(&totalCount)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatTotalRechargeUsers(statAt time.Time) (int, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(count(distinct user_id),0) as total_count").
		Where("state =1 and pay_time<?", statEnd).
		Scan(&totalCount)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatTotalRechargeAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(sum(amount), 0) as total_amount").
		Where("state = 1 and pay_time<?", statEnd).
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount, nil
}

func (o *StatDay) StatTotalRechargeMaxAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(max(amount), 0) as total_amount").
		Where("state = 1 and pay_time<?", statEnd).
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount, nil
}

func (o *StatDay) StatTotalRechargeRemainUsers(statAt time.Time) (int, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(count(distinct user_id),0) as total_count").
		Where("state =1 and product_category='CON' and pay_time<?", statEnd).
		Scan(&totalCount)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatTotalRechargeRemainAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(sum(amount), 0) as total_amount").
		Where("state = 1 and product_category='CON' and pay_time<?", statEnd).
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount, nil
}

func (o *StatDay) StatTotalBuyCardUsers(statAt time.Time) (int, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(count(distinct user_id),0) as total_count").
		Where("state =1 and action='BuyCard' and pay_time<?", statEnd).
		Scan(&totalCount)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatTotalBuyCardAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(sum(amount), 0) as total_amount").
		Where("state = 1 and action='BuyCard' and pay_time<?", statEnd).
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount, nil
}

func (o *StatDay) StatTotalCostAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&AmountBalance{}).
		Select("IFNULL(sum(occurred_amount), 0) as total_amount").
		Where("occurred_amount <0 and created_at<?", statEnd).
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount.Neg(), nil
}

func (o *StatDay) StatTotalCostRemainAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&AmountBalance{}).
		Select("IFNULL(sum(occurred_amount), 0) as total_amount").
		Where("card_id=0 and occurred_amount <0 and created_at<?", statEnd).
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount.Neg(), nil
}

func (o *StatDay) StatTotalCostCardAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&AmountBalance{}).
		Select("IFNULL(sum(occurred_amount), 0) as total_amount").
		Where("card_id>0 and occurred_amount <0 and created_at<?", statEnd).
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount.Neg(), nil
}

func (o *StatDay) StatDayRegisterUsers(statAt time.Time) (int, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&User{}).
		Select("IFNULL(count(id),0) as total_count").
		Where("created_at>=? and created_at<?", statStart, statEnd).
		Scan(&totalCount)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatDayCertUsers(statAt time.Time) (int, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&Certify{}).
		Select("IFNULL(count(distinct user_id),0) as total_count").
		Where("status=1 and created_at>=? and created_at<?", statStart, statEnd).
		Scan(&totalCount)
	if tx.Error != nil {
		return 0, tx.Error
	}

	return totalCount, nil
}

func (o *StatDay) StatDayRechargeCount(statAt time.Time) (int, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(count(user_id),0) as total_count").
		Where("pay_time>=? and pay_time<?", statStart, statEnd).
		Where("state =1").
		Scan(&totalCount)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatDayRechargeUsers(statAt time.Time) (int, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(count(distinct user_id),0) as total_count").
		Where("pay_time>=? and pay_time<?", statStart, statEnd).
		Where("state =1").
		Scan(&totalCount)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatDayRechargeList(statAt time.Time, dest interface{}) error {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	tx := DB.Debug().Model(&Recharge{})
	tx.Where("pay_time>=? and pay_time<? and state=1", statStart, statEnd)
	tx.Scan(dest)
	return tx.Error
}

func (o *StatDay) StatDayRechargeRegisterUsers(statAt time.Time) (int, error) {
	type item struct {
		UserId  uint      `json:"user_id"`
		PayTime time.Time `json:"pay_time"`
	}

	ary := make([]item, 0)

	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&Recharge{}).
		Select("user_id,pay_time").
		Where("pay_time>=? and pay_time<?", statStart, statEnd).
		Where("state =1").
		Scan(&ary)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatDayRechargeFirstUsers(statAt time.Time) (int, error) {
	type item struct {
		UserId  uint      `json:"user_id"`
		PayTime time.Time `json:"pay_time"`
	}

	ary := make([]item, 0)

	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&Recharge{}).
		Select("user_id,pay_time").
		Where("pay_time>=? and pay_time<?", statStart, statEnd).
		Where("state =1").
		Scan(&ary)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatDayRechargeAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(sum(amount), 0) as total_amount").
		Where("pay_time>=? and pay_time<?", statStart, statEnd).
		Where("state = 1").
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount, nil
}

func (o *StatDay) StatDayRechargeMaxAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(max(amount), 0) as total_amount").
		Where("pay_time>=? and pay_time<?", statStart, statEnd).
		Where("state = 1").
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount, nil
}

func (o *StatDay) StatDayRechargeRemainUsers(statAt time.Time) (int, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(count(distinct user_id),0) as total_count").
		Where("pay_time>=? and pay_time<?", statStart, statEnd).
		Where("state =1 and product_category='CON'").
		Scan(&totalCount)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatDayRechargeRemainAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(sum(amount), 0) as total_amount").
		Where("pay_time>=? and pay_time<?", statStart, statEnd).
		Where("state = 1 and product_category='CON'").
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount, nil
}

func (o *StatDay) StatDayBuyCardUsers(statAt time.Time) (int, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalCount int
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(count(distinct user_id),0) as total_count").
		Where("pay_time>=? and pay_time<?", statStart, statEnd).
		Where("state =1 and action='BuyCard'").
		Scan(&totalCount)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return totalCount, nil
}

func (o *StatDay) StatDayBuyCardAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&Recharge{}).
		Select("IFNULL(sum(amount), 0) as total_amount").
		Where("pay_time>=? and pay_time<?", statStart, statEnd).
		Where("state = 1 and action='BuyCard'").
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount, nil
}

func (o *StatDay) StatDayCostAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&AmountBalance{}).
		Select("IFNULL(sum(occurred_amount), 0) as total_amount").
		Where("created_at>=? and created_at<?", statStart, statEnd).
		Where("occurred_amount <0").
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount.Neg(), nil
}

func (o *StatDay) StatDayCostRemainAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&AmountBalance{}).
		Select("IFNULL(sum(occurred_amount), 0) as total_amount").
		Where("created_at>=? and created_at<?", statStart, statEnd).
		Where("card_id=0 and occurred_amount <0").
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount.Neg(), nil
}

func (o *StatDay) StatDayCostCardAmount(statAt time.Time) (decimal.Decimal, error) {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(&AmountBalance{}).
		Select("IFNULL(sum(occurred_amount), 0) as total_amount").
		Where("created_at>=? and created_at<?", statStart, statEnd).
		Where("card_id>0 and occurred_amount <0").
		Scan(&totalAmount)
	if tx.Error != nil {
		return decimal.Zero, tx.Error
	}
	return totalAmount.Neg(), nil
}

func (o *StatDay) StatDayCostDetailAmount(dest interface{}, statAt time.Time) error {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	tx := DB.Debug().Model(&AmountBalance{}).
		Select("order_type,-(IFNULL(sum(occurred_amount), 0)) as total_amount").
		Where("created_at>=? and created_at<?", statStart, statEnd).
		Where("occurred_amount <0").
		Group("order_type").
		Scan(dest)
	return tx.Error
}

func (o *StatDay) StatDayInstRecordList(lastStartupTime time.Time, limit int, statAt time.Time, dest interface{}) error {
	statStart := time.Date(statAt.Year(), statAt.Month(), statAt.Day(), 0, 0, 0, 0, time.Now().Location())
	statEnd := statStart.AddDate(0, 0, 1)
	tx := DB.Debug().Model(&InstRecord{})
	tx.Where("startup_time>?", lastStartupTime)
	tx.Where("startup_time<?", statEnd)
	tx.Where("shutdown_time<='1949-10-01' or shutdown_time>=?", statStart)
	//还没关的 当前时间段之后关的  当前时间段关的
	tx.Order("startup_time asc").Limit(limit)
	tx.Scan(dest)
	return tx.Error
}

// GetPodUsageAndImageInfo 获取最近7天的pod使用数据和对应的镜像信息
func (o *StatDay) GetPodUsageAndImageInfo() ([]map[string]interface{}, error) {
	// 查询最近7天的day_pod_usage_data数据
	var statDays []StatDay
	err := DB.Debug().Model(&StatDay{}).
		Where("created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)").
		Order("created_at DESC").
		Select("day_pod_usage_data").
		Find(&statDays).Error
	if err != nil {
		return nil, err
	}

	// 合并所有天的pod使用数据，保留最新的数据
	mergedPodUsageData := make(map[string]interface{})
	for _, day := range statDays {
		if day.DayPodUsageData == "" || day.DayPodUsageData == "{}" {
			continue
		}

		// 解析当天的JSON数据
		var dailyPodData map[string]interface{}
		if err := json.Unmarshal([]byte(day.DayPodUsageData), &dailyPodData); err != nil {
			continue
		}

		// 将新数据合并到结果中，已存在的pod不会被覆盖（保留最新的）
		for podID, usageData := range dailyPodData {
			if _, exists := mergedPodUsageData[podID]; !exists {
				mergedPodUsageData[podID] = usageData
			}
		}
	}

	// 提取所有pod_id
	podIDs := make([]uint, 0, len(mergedPodUsageData))
	for podIDStr := range mergedPodUsageData {
		podID, err := strconv.ParseUint(podIDStr, 10, 32)
		if err != nil {
			continue
		}
		podIDs = append(podIDs, uint(podID))
	}

	if len(podIDs) == 0 {
		return []map[string]interface{}{}, nil
	}

	// 查询Pod表获取usage_count
	var pods []Pod
	err = DB.Debug().Model(&Pod{}).
		Where("id IN ? AND status = 1", podIDs).
		Order("usage_count DESC").
		Find(&pods).Error
	if err != nil {
		return nil, err
	}

	podUsageCounts := make(map[uint]int)
	for _, pod := range pods {
		podUsageCounts[pod.ID] = pod.UsageCount
	}

	// 查询符合条件的最新版本镜像
	var podImages []PodImage
	err = DB.Debug().Model(&PodImage{}).
		Where("pod_id IN ? AND image_type = 2 AND status = 1 AND deleted_at IS NULL AND hub_status <> 2", podIDs).
		Order("id DESC").
		Find(&podImages).Error
	if err != nil {
		return nil, err
	}

	// 过滤结果，只保留每个pod_id的最新记录
	latestImages := make(map[uint]PodImage)
	for _, img := range podImages {
		if _, exists := latestImages[img.PodId]; !exists {
			latestImages[img.PodId] = img
		}
	}

	// 整合结果
	results := make([]map[string]interface{}, 0)
	for _, img := range latestImages {
		podID := img.PodId
		if podUsageCounts[podID] == 0 {
			continue
		}

		podIDStr := strconv.FormatUint(uint64(podID), 10)
		usageData, exists := mergedPodUsageData[podIDStr]
		if !exists {
			continue
		}

		result := map[string]interface{}{
			"pod_id":      podID,
			"pod_uuid":    img.PodUuid,
			"sha256":      img.Sha256,
			"image_tag":   img.ImageTag,
			"usage_data":  usageData,
			"image_size":  img.Size,
			"usage_count": podUsageCounts[podID],
			"hub_url":     "10.20.103.240/chenyu/public/" + img.PodUuid + ":" + img.ImageTag,
		}
		results = append(results, result)
	}

	// 按 usage_count 排序
	sort.Slice(results, func(i, j int) bool {
		return results[i]["usage_count"].(int) > results[j]["usage_count"].(int)
	})

	return results, nil
}

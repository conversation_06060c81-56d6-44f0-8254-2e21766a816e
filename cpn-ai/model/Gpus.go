package model

import (
	"cpn-ai/enums"
	"gorm.io/gorm"
	"time"
)

type Gpus struct {
	gorm.Model
	Index         int       `json:"index" gorm:"type:int;not null;default:0;comment:显卡序号"`
	Uuid          string    `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID"`
	GpuModelId    uint      `json:"gpu_model_id" gorm:"type:bigint;not null;default:0;comment:显卡型号ID"`
	VirtualId     uint      `json:"virtual_id" gorm:"type:bigint;not null;default:0;comment:虚拟机ID"`
	HostPort      string    `json:"host_port" gorm:"type:varchar(50);not null;default:'';comment:所属虚拟机"`
	FreeGpus      int       `json:"free_gpus" gorm:"type:int;not null;default:0;comment:所属主机空余显卡数量"`
	GpuName       string    `json:"gpu_name" gorm:"type:varchar(50);not null;default:'';comment:显卡名称"`
	MemoryG       int       `json:"memory_g" gorm:"type:int;not null;default:0;comment:显存(单位G)"`
	MemoryM       int       `json:"memory_m" gorm:"type:int;not null;default:0;comment:显存(单位M)"`
	Remark        string    `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	LastCheckTime time.Time `json:"last_check_time" gorm:"type:datetime;default:'1900-01-01';comment:最后核对时间"`
	Status        int       `json:"status" gorm:"type:int;not null;default:0;comment:状态"`
	InstanceUuid  string    `json:"instance_uuid" gorm:"type:varchar(50);not null;default:'';comment:实例字符串ID"`
	StartupMark   string    `json:"startup_mark" gorm:"type:varchar(50);not null;default:'';comment:启动标识字符串ID"`
	LockedAt      time.Time `json:"locked_at" gorm:"type:datetime;default:'1900-01-01';comment:锁定时间"`
}

func (Gpus) TableName() string {
	return "T_Gpus"
}

func (o *Gpus) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *Gpus) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Gpus) GetByUuid(uuid string) error {
	if err := DB.First(o, "uuid=?", uuid).Error; err != nil {
		return err
	}
	if o.ID == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

func (o *Gpus) SetStatus(status int) error {
	return DB.Model(o).Updates(Gpus{Status: status}).Error
}

func (o *Gpus) ListByVirtual(dest interface{}, virtualId uint) error {
	tx := DB.Debug().Model(o)
	tx.Where("virtual_id=?", virtualId).Scan(dest)
	return tx.Error
}

func (o *Gpus) List(dest interface{}, id uint, virtualId uint, status int, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	}
	if virtualId > 0 {
		tx.Where("virtual_id=?", virtualId)
	}
	if status >= 0 {
		tx.Where("status=?", status)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id asc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Gpus) ListPick(dest interface{}, region int, gpuModelId uint, needGpus int) error {
	tx := DB.Debug().Model(o)

	if region > 0 {
		tx.Where("region=?", region)
	}
	if gpuModelId > 0 {
		tx.Where("gpu_model_id=?", gpuModelId)
	}
	tx.Where("free_gpus>=?", needGpus)
	tx.Where("status=?", enums.GpuStatusEnum.Free)
	tx.Order("free_gpus asc,virtual_id asc").Limit(100).Scan(dest)
	return tx.Error
}

func (o *Gpus) LockGpus(instanceUuid string, startupMark string, ids []uint) error {
	//ids := []int{1, 2, 3}
	// 构建 SQL 查询
	tx := DB.Debug().Model(o).Where("id IN ?", ids).Updates(Gpus{InstanceUuid: instanceUuid, StartupMark: startupMark, Status: enums.GpuStatusEnum.Locked, LockedAt: time.Now()})
	return tx.Error
}

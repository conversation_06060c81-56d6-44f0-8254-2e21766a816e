package model

import "github.com/shopspring/decimal"

type UserEnv struct {
	Ip       string `json:"ip"`
	Plat     string `json:"plat" gorm:"type:varchar(50);not null;default:'';comment:来源平台"`
	CId      string `json:"c_id" gorm:"type:varchar(50);not null;default:'';comment:App推送ID"`
	Nation   string `json:"nation" gorm:"type:varchar(50);not null;default:'';comment:国家"`
	Province string `json:"province" gorm:"type:varchar(50);not null;default:'';comment:省份"`
	City     string `json:"city" gorm:"type:varchar(50);not null;default:'';comment:城市"`
	District string `json:"district" gorm:"type:varchar(50);not null;default:'';comment:区"`
	Address  string `json:"address" gorm:"type:varchar(100);not null;default:'';comment:地址"`

	Channel string          `json:"channel" gorm:"type:varchar(50);not null;default:'';comment:渠道"`
	Lng     decimal.Decimal `json:"lng"`
	Lat     decimal.Decimal `json:"lat"`

	AppVersion     string `json:"appVersion"`
	AppVersionCode int    `json:"appVersionCode"`
	AppWgtVersion  string `json:"appWgtVersion"`
	Brand          string `json:"brand"`
	BrowserName    string `json:"browserName"`
	BrowserVersion string `json:"browserVersion"`
	DeviceBrand    string `json:"deviceBrand"`
	DeviceId       string `json:"deviceId"`
	DeviceModel    string `json:"deviceModel"`
	DeviceType     string `json:"deviceType"`
	Model          string `json:"model"`
	Oaid           string `json:"oaid"`
	OsName         string `json:"osName"`
	OsVersion      string `json:"osVersion"`
	Platform       string `json:"platform"`
	RomName        string `json:"romName"`
	RomVersion     string `json:"romVersion"`
	System         string `json:"system"`
	Ua             string `json:"ua"`
	UniPlatform    string `json:"uniPlatform"`
}

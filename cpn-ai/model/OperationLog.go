package model

import (
	"gorm.io/gorm"
)

type OperationLog struct {
	gorm.Model
	OrigWhere        int    `json:"orig_where" gorm:"type:int;not null;default:0;comment:哪里来的数据"`
	OrigId           uint   `json:"orig_id" gorm:"type:bigint;not null;default:0;comment:来源记录ID"`
	UdId             string `json:"ud_id" gorm:"type:varchar(50);not null;default:'';comment:设备标识"`
	Ip               string `json:"ip" gorm:"type:varchar(50);not null;default:'';comment:客户端IP"`
	OperatorUserId   uint   `json:"operator_user_id" gorm:"type:bigint;not null;default:0;comment:操作员用户ID"`
	OperatorUserType string `json:"operator_user_type" gorm:"type:varchar(50);not null;default:'';comment:操作员用户类型 后台管理员manager 用户user"`
	LogType          int    `json:"log_type" gorm:"type:int;not null;default:0;comment:日志类型"`
	LogJson          string `json:"log_json" gorm:"type:json;';comment:日志内容"`
	UserEnv          string `json:"user_env" gorm:"type:json;comment:用户系统信息"`
}

func (OperationLog) TableName() string {
	return "T_OperationLog"
}

func (o *OperationLog) GetByID(id uint) error {
	err := DB.First(o, id).Error
	return err
}

func (o *OperationLog) GetList(dest interface{}, logType int, origWhere int, origId uint, page int, pageSize int) (int64, error) {

	queryParm := map[string]interface{}{
		"log_type":   logType,
		"orig_where": origWhere,
		"orig_id":    origId,
	}
	return o.List(dest, queryParm, page, pageSize)
}

func (o *OperationLog) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["log_type"]; ok {
			tx.Where("log_type=?", queryParm["log_type"])
		}
		if _, ok := queryParm["orig_where"]; ok {
			tx.Where("orig_where=?", queryParm["orig_where"])
		}
		if _, ok := queryParm["orig_id"]; ok {
			tx.Where("orig_id=?", queryParm["orig_id"])
		}
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	tx.Scan(dest)
	return total, tx.Error
}

func (o *OperationLog) Save() error {
	return DB.Save(o).Error
}

func (o *OperationLog) UpdateLogJson(logJson string) error { //Update 方法只会更新指定的单个列。在这种情况下，它只会更新 status 这一列，而不会修改其他列的值。这种方式通常用于只更新单一字段
	return DB.Model(o).Update("log_json", logJson).Error
}

package model

import (
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type RewardPodUsage struct {
	gorm.Model
	Nanoid           string          `json:"nanoid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID;uniqueIndex"`
	OrderType        int             `json:"order_type" gorm:"type:integer;not null;default:0;comment:业务类型"`
	UserId           uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	RewardAmount     decimal.Decimal `json:"reward_amount" gorm:"type:decimal(16,2);not null;default:0;comment:奖励金额"`
	CostAmount       decimal.Decimal `json:"cost_amount" gorm:"type:decimal(16,2);not null;default:0;comment:被邀请人总有效计费消费金额"`
	RewardMonth      int             `json:"reward_month" gorm:"type:int;not null;default:0;comment:奖励月份（202401）"`
	InvitationReward string          `json:"invitation_reward" gorm:"type:json;comment:当前奖励规则"`
	OrderNo          string          `json:"order_no" gorm:"type:varchar(50);not null;default:'';comment:业务单号;"`
	Remark           string          `json:"remark" gorm:"type:varchar(500);not null;default:'';comment:内部备注"`
	State            int             `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0统计中 1结束统计"`
}

func (RewardPodUsage) TableName() string {
	return "T_RewardPodUsage"
}

func (o *RewardPodUsage) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *RewardPodUsage) StateToLock() error {
	return DB.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if o.State != 0 {
			return errors.New("状态不正确")
		}

		if o.RewardAmount.LessThanOrEqual(decimal.Zero) {
			result := tx.Model(o).Updates(map[string]interface{}{"state": 1})
			if err := result.Error; err != nil {
				logger.Error(err)
				return err
			}

			if result.RowsAffected != 1 {
				err := errors.New(fmt.Sprintf("RewardRecord更新记录不唯一，当前更新记录数为%d", result.RowsAffected))
				logger.Error(err)
				return err
			}
			return nil
		}
		orderNo, err1 := OrderNo.NewByOrderType(enums.OrderTypeEnum.Reward, 0)
		if err1 != nil {
			logger.Error(err1)
			return err1
		}

		show := fmt.Sprintf("%d邀请佣金", o.RewardMonth)
		var balance RewardBalance
		if err := balance.GetNewObject(orderNo, o.UserId, enums.OrderTypeEnum.RewardPodUsage, o.RewardAmount, show, "", "", o.UserId, ""); err != nil {
			logger.Error(err)
			return err
		}
		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}

		result := tx.Model(o).Updates(map[string]interface{}{"state": 1, "order_no": orderNo})
		if err := result.Error; err != nil {
			logger.Error(err)
			return err
		}

		if result.RowsAffected != 1 {
			err := errors.New(fmt.Sprintf("RewardRecord更新记录不唯一，当前更新记录数为%d", result.RowsAffected))
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (o *RewardPodUsage) GetById(id uint) error {
	err := DB.First(o, id).Error
	return err
}

func (o *RewardPodUsage) GetByRewardMonth(orderType int, userId uint, rewardMonth int) error {
	err := DB.First(o, "order_type=? and user_id=? and reward_month=?", orderType, userId, rewardMonth).Error
	return err
}

func (o *RewardPodUsage) GetByOutTradeNo(outTradeNo string) error {
	err := DB.Debug().First(o, "out_trade_no = ?", outTradeNo).Error
	return err
}

func (o *RewardPodUsage) List(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {
	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["order_type"]; ok {
			tx.Where("order_type=?", queryParm["order_type"])
		}
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["reward_month"]; ok {
			tx.Where("reward_month=?", queryParm["reward_month"])
		}
		if _, ok := queryParm["status"]; ok {
			tx.Where("status=?", queryParm["status"])
		}
		if page == 1 {
			if err := tx.Count(&total).Error; err != nil {
				return 0, err
			}
		}
		tx.Order("reward_month desc, id desc").Limit(pageSize).Offset((page - 1) * pageSize)
	}
	tx.Scan(dest)
	return total, tx.Error
}

func (o *RewardPodUsage) StatReward(userId uint, rewardMonth int) (decimal.Decimal, error) {
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(o).
		Select("IFNULL(sum(reward_amount),0) as total_amount")
	if userId > 0 {
		tx.Where("user_id = ?", userId)
	}
	if rewardMonth > 0 {
		tx.Where("reward_month = ?", rewardMonth)
	}
	tx.Scan(&totalAmount)
	return totalAmount, tx.Error
}

func (o *RewardPodUsage) StatingReward(userId uint) (decimal.Decimal, error) {
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(o).
		Select("IFNULL(sum(reward_amount),0) as total_amount").
		Where("user_id = ?", userId).
		Where("state=0").
		Scan(&totalAmount)
	return totalAmount, tx.Error
}

func (o *RewardPodUsage) StatedReward(userId uint) (decimal.Decimal, error) {
	var totalAmount decimal.Decimal
	tx := DB.Debug().Model(o).
		Select("IFNULL(sum(reward_amount),0) as total_amount").
		Where("user_id = ?", userId).
		Where("state>0").
		Scan(&totalAmount)
	return totalAmount, tx.Error
}

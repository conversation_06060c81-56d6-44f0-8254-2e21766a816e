package model

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/structs"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"sort"
	"time"
)

type SettleStore struct {
	gorm.Model
	UserId       uint            `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	BusinessType int             `json:"business_type" gorm:"type:int;not null;default:0;comment:业务类型"`
	BusinessId   uint            `json:"business_id" gorm:"type:bigint;not null;default:0;comment:业务ID"`
	SettleTime   time.Time       `json:"settle_time" gorm:"type:datetime;default:'1900-01-01';comment:结算时间"` //整点
	SettleInfo   string          `json:"settle_info" gorm:"type:json;comment:结算信息"`
	Amount       decimal.Decimal `json:"amount" gorm:"type:decimal(16, 8);not null;default:0;comment:结算金额"`
	OrderNo      string          `json:"order_no" gorm:"type:varchar(50);not null;default:'';comment:扣费订单编号"`
}

func (SettleStore) TableName() string {
	return "T_SettleStore"
}

func (o *SettleStore) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *SettleStore) Exists(userId uint, businessType int, settleTime time.Time) (bool, error) {
	var count int64
	err := DB.Model(o).Where("user_id=? and business_type=? and settle_time=?", userId, businessType, settleTime.Truncate(time.Hour)).Count(&count).Error
	if err != nil {
		return false, err
	}
	if count > 0 {
		return true, nil
	} else {
		return false, nil
	}
}

func (o *SettleStore) New(tx *gorm.DB, userId uint, businessType int, settleTime time.Time, size decimal.Decimal) error {
	settleTime = settleTime.Truncate(time.Hour)
	pre := fmt.Sprintf("存储结算 userId:%d businessType:%d settleTime:%s size:%s ", userId, businessType, settleTime, size.String())
	var count int64
	if err := DB.Model(o).Where("user_id=? and business_type=? and settle_time=?", userId, businessType, settleTime.Truncate(time.Hour)).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		logger.Error("记录已存在 ", fmt.Sprintf("user_id=%d and business_type=%d and settle_time=%s", userId, businessType, settleTime.Truncate(time.Hour)))
		return errors.New("记录已存在")
	}

	orderType := 0
	unitGbPrice := decimal.Zero
	freeSize := decimal.Zero
	if businessType == enums.BusinessTypeEnum.ImageStore {
		unitGbPrice = common.ImageStoreHourPrice
		freeSize = common.ImageStoreFreeSize
		orderType = enums.OrderTypeEnum.ImageStore
	} else if businessType == enums.BusinessTypeEnum.CloudStore {
		unitGbPrice = common.CloudStoreHourPrice
		freeSize = common.CloudStoreFreeSize
		orderType = enums.OrderTypeEnum.CloudStore
	} else {
		err := errors.New("业务类型不正确")
		logger.Error(err, " businessType:", businessType)
		return err
	}

	d1024 := decimal.NewFromInt(1024)
	unitBytePrice := unitGbPrice.Div(d1024).Div(d1024).Div(d1024)
	settleInfo := structs.StoreSettleInfo{
		UnitGbPrice:   unitGbPrice,
		UnitBytePrice: unitBytePrice,
		Size:          size,
		FreeSize:      freeSize,
	}
	amount := size.Sub(freeSize).Mul(unitBytePrice)
	settleStore := SettleStore{
		UserId:       userId,
		BusinessType: businessType,
		SettleTime:   settleTime,
		Amount:       amount,
		SettleInfo:   utils.GetJsonFromStruct(settleInfo),
	}

	if tmpOrderNo, err := OrderNo.NewByOrderType(orderType, int64(0)); err != nil {
		logger.Error(err)
		return err
	} else {
		if tmpOrderNo == "" {
			er := errors.New("orderNo 为空")
			logger.Error(er)
			return er
		}
		settleStore.OrderNo = tmpOrderNo
	}

	if err := tx.Debug().Save(&settleStore).Error; err != nil {
		logger.Error(err)
		return err
	}

	show := fmt.Sprintf("%s", enums.BusinessTypeEnum.Name(businessType))
	remark := fmt.Sprintf("结算时间%s", settleTime)

	if userId <= 0 {
		//从余额扣费
		var balance AmountBalance
		if err := balance.GetBalanceObject(settleStore.OrderNo, 0, userId, orderType, settleStore.Amount.Neg(), show, remark, userId, "用户ID"); err != nil {
			logger.Error(err)
			return err
		}
		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}
	} else {
		//从算力卡扣费
		cards := make([]structs.RankCard, 0) //从算力卡扣费
		var card Card
		if _, err := card.ListForSettle(&cards, userId, 0); err != nil {
			msg := "查询用户可用算力卡失败"
			logger.Error(pre, msg, err)
		} else {
			logger.Info(pre, fmt.Sprintf("检索到%d张算力卡", len(cards)))
			for i := 0; i < len(cards); i++ {
				cards[i].RankScore = cards[i].ExpireDate.Unix()
				if cards[i].PodIds == "" {
					cards[i].RankScore = cards[i].ExpireDate.AddDate(1, 0, 0).Unix()
				}
			}
			sort.Slice(cards, func(i, j int) bool {
				return cards[i].RankScore < cards[j].RankScore
			})
		}

		leaveSettleAmount := settleStore.Amount
		logger.Info(pre, fmt.Sprintf("需要扣款的金额%s", leaveSettleAmount.String()))
		for _, tmpCard := range cards {
			if tmpCard.Status != 1 {
				logger.Info(pre, fmt.Sprintf("CardId：%d,状态不为1 status:%d", tmpCard.ID, tmpCard.Status))
				continue
			}
			if tmpCard.BindUserId != userId {
				logger.Info(pre, fmt.Sprintf("CardId：%d,用户不匹配 bindUserId:%d  ", tmpCard.ID, tmpCard.BuyUserId))
				continue
			}
			if tmpCard.LeaveAmount.LessThanOrEqual(decimal.Zero) {
				logger.Info(pre, fmt.Sprintf("CardId：%d,卡内可用额度为0 leaveAmount:%s", tmpCard.ID, tmpCard.LeaveAmount.String()))
				continue
			}

			costAmount := leaveSettleAmount
			if tmpCard.LeaveAmount.LessThan(leaveSettleAmount) {
				costAmount = tmpCard.LeaveAmount
			}
			leaveSettleAmount = leaveSettleAmount.Sub(costAmount)

			logger.Info(pre, fmt.Sprintf("CardId：%d,开始扣款 costAmount:%s    settleAmount：%s", tmpCard.ID, costAmount.String(), leaveSettleAmount.String()))
			var balance AmountBalance
			if err := balance.GetBalanceObject(settleStore.OrderNo, tmpCard.ID, userId, orderType, costAmount.Neg(), show, remark, userId, "用户ID"); err != nil {
				logger.Error(pre, err)
				return err
			}
			if err := balance.New(tx); err != nil {
				logger.Error(pre, err)
				return err
			}
			if leaveSettleAmount.LessThanOrEqual(decimal.Zero) {
				logger.Info(pre, fmt.Sprintf("CardId：%d,结算金额已经全部完成  settleAmount：%s", tmpCard.ID, leaveSettleAmount.String()))
				break
			}

		}
		if leaveSettleAmount.GreaterThan(decimal.Zero) {
			logger.Info(pre, fmt.Sprintf("从账户余额中扣款  settleAmount：%s", leaveSettleAmount.String()))
			var balance AmountBalance
			if err := balance.GetBalanceObject(settleStore.OrderNo, 0, userId, orderType, leaveSettleAmount.Neg(), show, remark, userId, "用户ID"); err != nil {
				logger.Error(pre, err)
				return err
			}
			if err := balance.New(tx); err != nil {
				logger.Error(pre, err)
				return err
			}
		}
	}

	return nil
}

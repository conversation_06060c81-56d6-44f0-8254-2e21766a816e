package model

import (
	"cpn-ai/common/logger"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

type SocialLink struct {
	Name string `json:"name"`
	Icon string `json:"icon"`
	Link string `json:"link"`
}

type SocialLinks []SocialLink

func (a *SocialLinks) Scan(v any) (err error) {
	var b []byte
	switch x := v.(type) {
	case nil:
	case string:
		b = []byte(x)
	case []byte:
		b = x
	default:
		return errors.New("value cannot cast to string or []byte")
	}
	if len(b) == 0 {
		*a = SocialLinks{}
	} else {
		err = json.Unmarshal(b, a)
	}
	return
}

func (a SocialLinks) Value() (driver.Value, error) {
	return json.Marshal(a)
}

type Teacher struct {
	gorm.Model
	Uuid     string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串id;uniqueIndex"`
	InstitId uint   `json:"instit_id" gorm:"type:bigint;not null;default:0;comment:机构id;index"`
	Name     string `json:"name" gorm:"type:varchar(50);not null;default:'';comment:名称"`
	Intro    string `json:"intro" gorm:"type:varchar(1500);not null;default:'';comment:简介"`
	Avatar   string `json:"avatar" gorm:"type:varchar(100);not null;default:'';comment:头像图片"`

	Contact       string `json:"contact" gorm:"type:varchar(100);not null;default:'';comment:联系方式：二维码图片"`
	ContactDetail string `json:"contact_detail" gorm:"type:text;comment:联系方式详情"`

	Position string      `json:"position" gorm:"type:varchar(50);not null;default:'';comment:职位"`
	Video    string      `json:"video" gorm:"type:varchar(100);not null;default:'';comment:介绍视频"`
	Links    SocialLinks `json:"links" gorm:"type:text;comment:社交链接"`

	Detail   string       `json:"detail" gorm:"type:text;comment:详情"`
	Status   InstitStatus `json:"status" gorm:"type:integer;not null;default:1;comment:状态：1不展示，2展示"`
	Priority int          `json:"priority" gorm:"type:integer;not null;default:0;comment:排序优先级"`
}

func (Teacher) TableName() string {
	return "T_Teacher"
}

func (o *Teacher) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *Teacher) Delete() error {
	return DB.Debug().Unscoped().Delete(o).Error
}

func (o *Teacher) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Teacher) GetByIdFromCache(id uint) error {
	key := fmt.Sprintf("model_teacher_%d", id)
	if data, err := cache.Get(key); err == nil {
		if err := json.Unmarshal(data, o); err == nil {
			return nil
		} else {
			logger.Error(key, " err:", err)
		}
	}

	if err := DB.First(o, id).Error; err != nil {
		return err
	} else {
		if data, err := json.Marshal(*o); err == nil {
			if err := cache.Set(key, data); err == nil {
				return nil
			}
		}
		return nil
	}
}

func (o *Teacher) GetByUuid(uuid string) error {
	return DB.First(o, "uuid=?", uuid).Error
}

func (o *Teacher) List(dest any, queryParams M, descCol string, page, pageSize int) (total int64, _ error) {
	tx := DB.Debug().Model(o)
	if _, ok := queryParams["id"]; ok {
		tx.Where("id=?", queryParams["id"])
	} else {
		if _, ok := queryParams["instit_id"]; ok {
			tx.Where("instit_id=?", queryParams["instit_id"])
		}

		if _, ok := queryParams["status"]; ok {
			tx.Where("status=?", queryParams["status"])
		}
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}

	orderBy := "id desc"
	if descCol == "priority" {
		orderBy = "priority desc, " + orderBy
	}

	tx.Order(orderBy).Limit(pageSize).Offset((page - 1) * pageSize)
	tx.Scan(dest)
	return total, tx.Error
}

func (o *Teacher) SetName(s string) error {
	return DB.Model(o).Updates(M{"name": s}).Error
}
func (o *Teacher) SetIntro(s string) error {
	return DB.Model(o).Updates(M{"intro": s}).Error
}
func (o *Teacher) SetAvatar(s string) error {
	return DB.Model(o).Updates(M{"avatar": s}).Error
}
func (o *Teacher) SetVideo(s string) error {
	return DB.Model(o).Updates(M{"video": s}).Error
}
func (o *Teacher) SetLinks(a SocialLinks) error {
	return DB.Model(o).Updates(M{"links": a}).Error
}
func (o *Teacher) SetContact(s string) error {
	return DB.Model(o).Updates(M{"contact": s}).Error
}
func (o *Teacher) SetContactDetail(s string) error {
	return DB.Model(o).Updates(M{"contact_detail": s}).Error
}
func (o *Teacher) SetDetail(s string) error {
	return DB.Model(o).Updates(M{"detail": s}).Error
}
func (o *Teacher) SetStatus(i InstitStatus) error {
	return DB.Model(o).Updates(M{"status": i}).Error
}
func (o *Teacher) SetPriority(i int) error {
	return DB.Model(o).Updates(M{"priority": i}).Error
}

func (o *Teacher) Create() error {
	return DB.Model(o).Debug().Create(o).Error
}
func (o *Teacher) Updates(m M) error {
	return DB.Model(o).Debug().Updates(m).Error
}

package model

import (
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"errors"
	"time"

	"gorm.io/gorm"
)

type PodImageHubStatusEnum int

const (
	PodImageHubStatusNeedCheck PodImageHubStatusEnum = 0
	PodImageHubStatusExist                           = 1
	PodImageHubStatusNotExist                        = 2
	PodImageHubStatusError                           = 3
)

func PodImageHubStatusEnumName(i PodImageHubStatusEnum) string {
	switch i {
	case PodImageHubStatusNeedCheck:
		return "未核对"
	case PodImageHubStatusExist:
		return "存在"
	case PodImageHubStatusNotExist:
		return "不存在"
	case PodImageHubStatusError:
		return "未知错误"
	}
	return ""
}

type PodImage struct {
	gorm.Model
	Uuid              string    `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID"`
	ParentId          uint      `json:"parent_id" gorm:"type:bigint;not null;default:0;comment:上级镜像ID;index"`
	UserId            uint      `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID;index"`
	ImageType         int       `json:"image_type" gorm:"type:int;not null;default:0;comment:镜像类型 1基础 2Kol 3私人 4私人实例 9算力市场"`
	StorageMode       int       `json:"storage_mode" gorm:"type:int;not null;default:0;comment:存储模式 1镜像仓库 2镜像磁盘仓库 3个人磁盘存储"`
	InstanceId        uint      `json:"instance_id" gorm:"type:bigint;not null;default:0;comment:来源实例ID"`
	Title             string    `json:"title" gorm:"type:varchar(50);not null;default:'';comment:标题"`
	PodId             uint      `json:"pod_id" gorm:"type:bigint;not null;default:0;comment:PodID;index"`
	PodUuid           string    `json:"pod_uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID"`
	ImageName         string    `json:"image_name" gorm:"type:varchar(500);not null;default:'';comment:镜像名称"`
	ImageTag          string    `json:"image_tag" gorm:"type:varchar(500);not null;default:'';comment:镜像版本"`
	ImageLabelIds     string    `json:"image_label_ids" gorm:"type:varchar(1500);not null;default:'';comment:镜像标签IDs"`
	ImageMeta         string    `json:"image_meta" gorm:"type:text;comment:镜像元数据"`
	ShaId             string    `json:"sha_id" gorm:"type:varchar(100);not null;default:'';comment:对应仓库的镜像ID"`
	Sha256            string    `json:"sha256" gorm:"type:varchar(100);not null;default:'';comment:镜像哈希值;index"`
	Size              float64   `json:"size" gorm:"type:float;not null;default:0;comment:镜像大小(字节)"`
	LayerCount        int       `json:"layer_count" gorm:"type:integer;not null;default:0;comment:镜像层数(最多127层)"`
	Reason            string    `json:"reason" gorm:"type:varchar(50);not null;default:'';comment:保存出错原因"`
	Remark            string    `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注"`
	Share             string    `json:"share" gorm:"type:varchar(150);not null;default:'';comment:分享用户"`
	DockerFile        string    `json:"docker_file" gorm:"type:text;comment:DockerFile编译文件"`
	CommitStartTime   time.Time `json:"commit_start_time" gorm:"type:datetime;default:'1900-01-01';comment:提交保存的时间"`
	CommitStartupMark string    `json:"commit_startup_mark" gorm:"type:varchar(50);not null;default:'';comment:提交的启动标记"`
	CommitVirtualId   uint      `json:"commit_virtual_id" gorm:"type:bigint;not null;default:0;comment:提交的虚拟机ID"`
	LastSaveTime      time.Time `json:"last_save_time" gorm:"type:datetime;default:'1900-01-01';comment:最后保存成功的时间"`
	LastUseTime       time.Time `json:"last_use_time" gorm:"type:datetime;default:'1900-01-01';comment:最后使用的时间"`
	AuditStatus       int       `json:"audit_status" gorm:"type:tinyint;not null;default:0;comment:审核状态"` //
	Status            int       `json:"status" gorm:"type:tinyint;not null;default:0;comment:状态"`
	DestroyAt         time.Time `json:"destroy_at" gorm:"type:datetime;default:'1900-01-01';comment:销毁时间"`
	HubStatus         int       `json:"hub_status" gorm:"type:tinyint;not null;default:0;comment:仓库核对状态 0未核对 1存在 2不存在 3未知错误"` //
	HubAt             time.Time `json:"hub_at" gorm:"type:datetime;default:'1900-01-01';comment:仓库核对时间"`
}

//Price {"hour":1.66,"day":38.65,"week":245.43,"month":847.80}

func (PodImage) TableName() string {
	return "T_PodImage"
}

func (o *PodImage) Save() error {
	return DB.Debug().Save(o).Error
}

func (o *PodImage) Delete() error {
	return DB.Debug().Delete(o).Error
}

func (o *PodImage) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *PodImage) GetByUuid(uuid string) error {
	if err := DB.First(o, "uuid=?", uuid).Error; err != nil {
		return err
	} else if o.ID == 0 {
		return gorm.ErrRecordNotFound
	} else {
		return nil
	}
}

func (o *PodImage) GetBySha256(sha256 string) error {
	return DB.First(o, "sha256=?", sha256).Error
}

func (o *PodImage) GetPrivateInstanceImage(instanceId uint) error {
	return DB.First(o, "instance_id=? and image_type=? and status<9", instanceId, enums.ImageTypeEnum.PrivateInstance).Error
}

func (o *PodImage) GetPrivatePodImage(userId uint, podId uint, imageTag string) error {
	//First修复
	//return DB.First(o, "user_id=? and image_type=? and pod_id=? and image_tag=? and storage_mode=? and status<9", userId, enums.ImageTypeEnum.Private, podId, imageTag, enums.ImageStorageModeEnum.Registry).Order("id desc").Error
	return DB.Where("user_id=? and image_type=? and pod_id=? and image_tag=? and storage_mode=? and status<9", userId, enums.ImageTypeEnum.Private, podId, imageTag, enums.ImageStorageModeEnum.Registry).Order("id desc").First(o).Error
}

func (o *PodImage) GetUserPrivateImageByTitle(userId uint, title string) error {
	return DB.First(o, "user_id=? and image_type=? and title=?", userId, enums.ImageTypeEnum.Private, title).Error
}

func (o *PodImage) GetByPodIdAndTag(podId uint, imageTag string) error {
	return DB.First(o, "pod_id=? and image_tag=?", podId, imageTag).Error
}

func (o *PodImage) GetLastTagByPodId(podId uint) error {
	//First修复
	//return DB.First(o, "pod_id=? and audit_status=?", podId, enums.PodAuditStatusEnum.AuditPass).Order("id desc").Error
	return DB.Where("pod_id=? and audit_status=?", podId, enums.PodAuditStatusEnum.AuditPass).Order("id desc").First(o).Error
}

func (o *PodImage) GetByImageTypeAndTag(imageType int, imageName string, imageTag string) error {
	return DB.First(o, "image_type=? and image_name=? and image_tag=?", imageType, imageName, imageTag).Error
}

func (o *PodImage) GetByImageTag(imageName string, imageTag string) error {
	return DB.First(o, "image_name=? and image_tag=?", imageName, imageTag).Error
}

func (o *PodImage) ListByLastAutoId(dest interface{}, lastAutoId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Where("id>? ", lastAutoId)
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *PodImage) List(dest interface{}, id uint, userId uint, imageType int, podId uint, imageName string, status int, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if id > 0 {
		tx.Where("id=?", id)
	} else {
		if userId > 0 {
			tx.Where("user_id=?", userId)
		}
		if podId > 0 {
			tx.Where("pod_id=?", podId)
		}
		if imageName != "" {
			tx.Where("image_name=?", imageName)
		}
		if imageType > 0 {
			tx.Where("image_type=?", imageType)
		}
		if status >= 0 {
			tx.Where("status=?", status)
		}
		tx.Where("status<?", 9)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *PodImage) ListPro(dest interface{}, queryParm map[string]interface{}, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)
	if _, okk := queryParm["id"]; okk {
		tx.Where("id=?", queryParm["id"])
	} else {
		if _, ok := queryParm["ids"]; ok {
			tx.Where("id in (?)", queryParm["ids"])
		}
		if _, ok := queryParm["user_id"]; ok {
			tx.Where("user_id=?", queryParm["user_id"])
		}
		if _, ok := queryParm["pod_id"]; ok {
			tx.Where("pod_id=?", queryParm["pod_id"])
		}
		if _, ok := queryParm["pod_uuid"]; ok {
			tx.Where("pod_uuid=?", queryParm["pod_uuid"])
		}
		if _, ok := queryParm["image_name"]; ok {
			tx.Where("image_name=?", queryParm["image_name"])
		}
		if _, ok := queryParm["image_tag"]; ok {
			tx.Where("image_tag=?", queryParm["image_tag"])
		}
		if _, ok := queryParm["storage_mode"]; ok {
			tx.Where("storage_mode=?", queryParm["storage_mode"])
		}
		if _, ok := queryParm["image_type"]; ok {
			tx.Where("image_type=?", queryParm["image_type"])
		}
		if _, ok := queryParm["share"]; ok {
			ary := queryParm["share"].([]string)
			if len(ary) == 1 {
				kw := "%|" + ary[0] + "|%"
				tx.Where("share like ?", kw)
			} else if len(ary) == 2 {
				kw := "%|" + ary[0] + "|%"
				kw2 := "%|" + ary[1] + "|%"
				tx.Where("share like ? or share like ?", kw, kw2)
			}
		}
		//if _, ok := queryParm["status"]; ok {
		//	tx.Where("status=?", queryParm["status"])
		//}
		if _, ok := queryParm["audit_status"]; ok {
			tx.Where("audit_status=?", queryParm["audit_status"])
		}
		if _, ok := queryParm["hub_status"]; ok {
			tx.Where("hub_status=?", queryParm["hub_status"])
		}
		if v, ok := queryParm["status"]; ok {
			if v.(int) == 10 {

			} else {
				tx.Where("status=?", queryParm["status"])
			}
		} else {
			tx.Where("status<?", 9)
		}
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	if _, ok := queryParm["order"]; ok {
		tx.Order(queryParm["order"])
	} else {
		tx.Order("id desc")
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize)
	tx.Scan(dest)
	return total, tx.Error
}

func (o *PodImage) ListForCheckPushInProgress(dest interface{}, lastAutoId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Where("id>? and status<9 and audit_status=?", lastAutoId, enums.ImageAuditStatusEnum.Pushing)
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *PodImage) ListForCheckPushFail(dest interface{}, lastAutoId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Where("id>? and status=1 and audit_status=? and commit_start_time>=?", lastAutoId, enums.ImageAuditStatusEnum.PushFail, time.Now().Add(time.Hour*-48))
	tx.Order("id asc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *PodImage) ListForPrivateImage(dest interface{}, userId uint, lastAutoId uint, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Where("id>? and user_id=? and image_type=3", lastAutoId, userId)
	tx.Order("id desc").Limit(pageSize).Scan(dest)
	return tx.Error
}

func (o *PodImage) StatsSize(userId uint, imageType int) (float64, error) {
	var totalSize float64
	tx := DB.Debug().Model(o).
		Select("IFNULL(sum(size),0) as total_size")
	if userId > 0 {
		tx.Where("user_id = ?", userId)
	}
	if imageType > 0 {
		tx.Where("image_type = ?", imageType)
	}
	//if auditStatus > 0 {
	tx.Where("audit_status in(2,4)")
	//}
	tx.Scan(&totalSize)
	return totalSize, tx.Error
}

func (o *PodImage) StatStore(dest interface{}, userId uint, page int, pageSize int) error {
	tx := DB.Debug().Model(o)
	tx.Select("user_id, sum(size) as sum_size")
	if userId > 0 {
		tx.Where("user_id=? AND image_type = 3 AND audit_status = 2 AND storage_mode<3 AND status < 9", userId)
	} else {
		tx.Where("image_type = 3 AND audit_status = 2 AND storage_mode<3 AND status < 9")
	}

	tx.Group("user_id")
	tx.Order("user_id")
	tx.Limit(pageSize).Offset((page - 1) * pageSize)

	tx.Scan(dest)
	return tx.Error
}

func (o *PodImage) SetTitle(title string) error {
	//return DB.Model(o).Updates(PodImage{Title: title}).Error
	return DB.Model(o).Update("title", title).Error
}
func (o *PodImage) SetShare(share string) error {
	return DB.Model(o).Update("share", share).Error
}

func (o *PodImage) SetRemark(remark string) error {
	//return DB.Model(o).Updates(PodImage{Remark: remark}).Error
	return DB.Model(o).Update("remark", remark).Error
}

func (o *PodImage) SetSha256(sha256 string) error {
	//return DB.Model(o).Updates(PodImage{Sha256: sha256}).Error
	return DB.Model(o).Update("sha256", sha256).Error
}

func (o *PodImage) SetDelete() error {
	//return DB.Model(o).Updates(PodImage{Status: 9}).Error
	//return DB.Model(o).Update("status", 9).Error
	return DB.Model(o).Updates(map[string]interface{}{"status": 9, "destroy_at": time.Now()}).Error
}

func (o *PodImage) SetStatus(status int) error {
	return DB.Model(o).Update("status", status).Error
}

func (o *PodImage) SetLayerCount(layerCount int) error {
	return DB.Model(o).Update("layer_count", layerCount).Error
}

func (o *PodImage) SetImageLabelIds(labelIds string) error {
	return DB.Model(o).Update("image_label_ids", labelIds).Error
}

func (o *PodImage) SetImageDockerFile(dockerFile string) error {
	return DB.Model(o).Update("docker_file", dockerFile).Error
}

func (o *PodImage) SetHubStatus(hubStatus PodImageHubStatusEnum) error {
	return DB.Model(o).Updates(map[string]interface{}{"hub_status": hubStatus, "hub_at": time.Now()}).Error
}

func (o *PodImage) SetHubSuccess(size uint, digestSha256 string) error {
	if o.AuditStatus == enums.ImageAuditStatusEnum.PushFail {
		return DB.Model(o).Updates(map[string]interface{}{"audit_status": enums.ImageAuditStatusEnum.PushSuccess, "size": size, "sha256": digestSha256}).Error
	} else {
		return errors.New("不是上传失败状态，不更新状态")
	}
}

func (o *PodImage) SetAuditStatus(auditStatus int) error {
	if auditStatus == enums.ImageAuditStatusEnum.Pushing {
		return DB.Model(o).Updates(PodImage{AuditStatus: auditStatus, CommitStartTime: time.Now()}).Error
	} else if auditStatus == enums.ImageAuditStatusEnum.CommintSuccess {
		return DB.Model(o).Updates(PodImage{AuditStatus: auditStatus, CommitStartTime: time.Now()}).Error
	} else {
		return DB.Model(o).Update("audit_status", auditStatus).Error
	}
}

func (o *PodImage) ResumeAuditStatus() error {
	if o.AuditStatus != enums.ImageAuditStatusEnum.PushFail {
		return errors.New("不支持该状态")
	}
	return DB.Model(o).Updates(PodImage{AuditStatus: enums.ImageAuditStatusEnum.PushSuccess}).Error
}

func (o *PodImage) SetAuditStatusToPushing(startupMark string, virtualId uint, instanceId uint) error {
	return DB.Model(o).Updates(PodImage{AuditStatus: enums.ImageAuditStatusEnum.Pushing, CommitStartupMark: startupMark, CommitVirtualId: virtualId, InstanceId: instanceId, CommitStartTime: time.Now()}).Error
}

func (o *PodImage) SetAuditStatusWithReason(auditStatus int, reason string) error {
	if auditStatus == enums.ImageAuditStatusEnum.Pushing {
		return DB.Model(o).Updates(PodImage{AuditStatus: auditStatus, CommitStartTime: time.Now()}).Error
	} else if auditStatus == enums.ImageAuditStatusEnum.CommintSuccess {
		return DB.Model(o).Updates(PodImage{AuditStatus: auditStatus, CommitStartTime: time.Now()}).Error
	} else {
		return DB.Model(o).Update("audit_status", auditStatus).Update("reason", reason).Error
	}
}

func (o *PodImage) SetAuditPass() error {
	return DB.Model(o).Updates(PodImage{AuditStatus: enums.ImageAuditStatusEnum.AuditPass, Status: 1}).Error
}

func (o *PodImage) SetLastSaveTime() error {
	return DB.Model(o).Updates(PodImage{LastSaveTime: time.Now(), AuditStatus: enums.ImageAuditStatusEnum.PushSuccess}).Error
}

func (o *PodImage) SetLastUseTime() error {
	return DB.Model(o).Updates(PodImage{LastUseTime: time.Now()}).Error
}

func (o *PodImage) Transfer(podId uint, targetUserId uint, targetAuthorName string) error {
	return DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Debug().Model(&PodImage{}).Where("image_type=? and pod_id=?", enums.ImageTypeEnum.Public, podId).Updates(PodImage{UserId: targetUserId}).Error; err != nil {
			logger.Error(err)
			return err
		}
		if err := tx.Debug().Model(&Pod{}).Where("id=?", podId).Updates(Pod{UserId: targetUserId, AuthorName: targetAuthorName}).Error; err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (o *PodImage) Updates(m map[string]interface{}) error {
	return DB.Model(o).Debug().Updates(m).Error
}

package model

import (
	"cpn-ai/common/utils"
	"errors"
	"gorm.io/gorm"
	"strings"
	"time"
)

type Virtual struct {
	gorm.Model
	Uuid        string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:字符串ID"`
	Region      int    `json:"region" gorm:"type:integer;not null;default:0;comment:所在区域节点"`
	NodeId      uint   `json:"node_id" gorm:"type:bigint;not null;default:0;comment:节点ID"`
	Host        string `json:"host" gorm:"type:varchar(50);not null;default:'';comment:IP"`
	Port        int    `json:"port" gorm:"type:integer;not null;default:0;comment:端口"`
	SshUser     string `json:"ssh_user" gorm:"type:varchar(50);not null;default:'';comment:账号"`
	SshPassword string `json:"ssh_password" gorm:"type:varchar(50);not null;default:'';comment:密码"`
	GpuModelId  uint   `json:"gpu_model_id" gorm:"type:bigint;not null;default:0;comment:显卡型号ID"`
	//Gpus          string    `json:"gpus" gorm:"type:varchar(1500);not null;default:'';comment:显卡信息"`
	//GpuName       string    `json:"gpu_name" gorm:"type:varchar(50);not null;default:'';comment:显卡名称"`
	//MemoryG       int       `json:"memory_g" gorm:"type:integer;not null;default:0;comment:显存(单位G)"`
	//MemoryM       int       `json:"memory_m" gorm:"type:integer;not null;default:0;comment:显存(单位M)"`
	FreeGpus      int     `json:"free_gpus" gorm:"type:integer;not null;default:0;comment:当前空余显卡数量"`
	TotalGpus     int     `json:"total_gpus" gorm:"type:integer;not null;default:0;comment:总显卡数量"`
	TotalInstance int     `json:"total_instance" gorm:"type:integer;not null;default:0;comment:实例数量"`
	TotalCpus     int     `json:"total_cpus" gorm:"type:integer;not null;default:0;comment:总cpu数量"`
	ContainerCpus float64 `json:"container_cpus" gorm:"type:float;not null;default:0;comment:容器分配cpu数量"`
	TotalMem      int     `json:"total_mem" gorm:"type:integer;not null;default:0;comment:总内存"`
	ContainerMem  string  `json:"container_mem" gorm:"type:varchar(50);not null;default:'';comment:容器分配内存"`
	//ImageIds      string    `json:"image_ids" gorm:"type:varchar(6000);not null;default:'';comment:本机上的镜像Id用|拼接"`
	ImageIds      string    `json:"image_ids" gorm:"type:text;comment:本机上的镜像Id用|拼接"`
	LastCheckTime time.Time `json:"last_check_time" gorm:"type:datetime;default:'1900-01-01';comment:最后核对时间"`
	DockerAt      int64     `json:"docker_at" gorm:"type:bigint;not null;default:0;comment:启动docker时间戳,0为没有在启动"`
	TimeoutAt     int64     `json:"timeout_at" gorm:"type:bigint;not null;default:0;comment:ssh链接失败的时间点 0为链接正常"`
	InitedAt      int64     `json:"inited_at" gorm:"type:bigint;not null;default:0;comment:初始化完成载入的时间点 0为本次程序启动未初始化载入"`
	LastInitTime  time.Time `json:"last_init_time" gorm:"type:datetime;default:'1900-01-01';comment:最后一次成功初始化的时间"`
	PodIds        string    `json:"pod_ids" gorm:"type:varchar(1500);not null;default:'';comment:定向使用PodIDs"`
	Remark        string    `json:"remark" gorm:"type:varchar(500);not null;default:'';comment:内部备注"`
	Status        int       `json:"status" gorm:"type:tinyint;not null;default:0;comment:状态"`
	SysInfo       string    `json:"sys_info" gorm:"type:json;';comment:系统信息"`
}

//磁盘空间 MAC地址 Cpu信息 内存信息

func (Virtual) TableName() string {
	return "T_Virtual"
}

func (o *Virtual) Save() error {
	if o.ID == 0 && o.SysInfo == "" {
		o.SysInfo = "{}"
	}
	return DB.Debug().Save(o).Error
}

func (o *Virtual) GetById(id uint) error {
	return DB.First(o, id).Error
}

func (o *Virtual) Delete() error {
	return DB.Debug().Delete(o).Error
}

func (o *Virtual) GetByHost(host string) error {
	return DB.First(o, "host=?", host).Error
}

func (o *Virtual) GetByHostPort(hostPort string) error {
	ary := strings.Split(hostPort, ":")
	if len(ary) != 2 {
		return errors.New("hostPort参数错误")
	}
	host := ary[0]
	port := utils.String2Int(ary[1])
	if host == "" || port == 0 {
		return errors.New("hostPort参数错误")
	}
	if err := DB.First(o, "host=? and port=?", host, port).Error; err != nil {
		return err
	} else {
		if o.ID == 0 {
			return gorm.ErrRecordNotFound
		}
	}
	return nil
}

func (o *Virtual) ListPick(dest interface{}, virtualId uint, region int, gpuModelId uint, needGpus int) error {
	tx := DB.Debug().Model(o)

	if virtualId > 0 {
		tx.Where("id=?", region)
	} else {
		if region > 0 {
			tx.Where("region=?", region)
		}
		if gpuModelId > 0 {
			tx.Where("gpu_model_id=?", gpuModelId)
		}
		tx.Where("free_gpus>=?", needGpus)
		tx.Where("timeout_at=?", 0)
		tx.Where("inited_at>?", 0)
		tx.Where("status=?", 1)

	}

	tx.Order("free_gpus asc").Limit(100).Scan(dest)
	return tx.Error
}

func (o *Virtual) List(dest interface{}, nodeId uint, status int, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)

	if nodeId > 0 {
		tx.Where("node_id=?", nodeId)
	}
	if status >= 0 {
		tx.Where("status=?", status)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Virtual) ListForKol(dest interface{}, page int, pageSize int) (int64, error) {

	var total int64
	tx := DB.Debug().Model(o)

	tx.Where("status in (1,2) and free_gpus>=0")
	{
		kw := "%" + "|kol|" + "%"
		tx.Where("pod_ids like ?", kw)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Virtual) ListStats(dest interface{}, virtualIds []uint, page int, pageSize int) (int64, error) {
	fiveMinutesAgo := time.Now().Add(-15 * time.Minute)
	var total int64
	tx := DB.Debug().Model(o)
	tx.Where("status=1 and timeout_at = ? and last_check_time >= ?", 0, fiveMinutesAgo)
	if virtualIds != nil && len(virtualIds) > 0 {
		tx.Where("id in ?", virtualIds)
	}
	if page == 1 {
		if err := tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Virtual) Stats(dest interface{}, virtualIds []uint) error {
	fiveMinutesAgo := time.Now().Add(-15 * time.Minute)
	tx := DB.Debug().Model(o).
		Select("gpu_model_id, sum(total_gpus) as total_gpus, sum(free_gpus) as free_gpus").
		Where("status=1 and timeout_at = ? and last_check_time >= ?", 0, fiveMinutesAgo)
	if virtualIds != nil && len(virtualIds) > 0 {
		tx.Where("id in ?", virtualIds)
	}
	tx.Group("gpu_model_id").Scan(dest)
	return tx.Error
}

func (o *Virtual) StatsTotal(dest interface{}) error {
	//fiveMinutesAgo := time.Now().Add(-6 * time.Minute)
	tx := DB.Debug().Model(o).
		Select("gpu_model_id, sum(total_gpus) as total_gpus, sum(free_gpus) as free_gpus").
		//Where("timeout_at = ? and last_check_time >= ?", 0, fiveMinutesAgo).
		Group("gpu_model_id").
		Scan(dest)
	return tx.Error
}

func (o *Virtual) SetFreeGpus(freeGpus int) error {
	if freeGpus == 0 {
		return DB.Model(o).Updates(map[string]interface{}{"free_gpus": freeGpus, "last_check_time": time.Now()}).Error
	} else {
		return DB.Model(o).Updates(Virtual{FreeGpus: freeGpus, LastCheckTime: time.Now()}).Error
	}
}

func (o *Virtual) SetStatus(status int) error {
	return DB.Model(o).Updates(map[string]interface{}{"status": status}).Error
}

func (o *Virtual) SetStatusAndRemark(status int, remark string) error {
	return DB.Model(o).Updates(map[string]interface{}{"status": status, "remark": remark}).Error
}

func (o *Virtual) SetGpuModelAndRemark(gpuModelId uint, remark string) error {
	return DB.Model(o).Updates(map[string]interface{}{"gpu_model_id": gpuModelId, "remark": remark}).Error
}

func (o *Virtual) SetNodeAndRemark(nodeId uint, remark string) error {
	return DB.Model(o).Updates(map[string]interface{}{"node_id": nodeId, "remark": remark}).Error
}

func (o *Virtual) SetRemark(remark string) error {
	return DB.Model(o).Updates(map[string]interface{}{"remark": remark}).Error
}

func (o *Virtual) SetPodIds(podIds string) error {
	return DB.Model(o).Updates(map[string]interface{}{"pod_ids": podIds}).Error
}

func (o *Virtual) SetOnlyFreeGpus(freeGpus int) error {
	return DB.Model(o).Update("free_gpus", freeGpus).Error
}

func (o *Virtual) SetImageIds(imageIds string) error {
	return DB.Model(o).Update("image_ids", imageIds).Error
}

func (o *Virtual) SetDockerAt(unix int64) error {
	return DB.Model(o).Update("docker_at", unix).Error
}

func (o *Virtual) SetTimeoutAt(unix int64) error {
	return DB.Model(o).Update("timeout_at", unix).Error
}

func (o *Virtual) ResetInitedAt() error {
	return DB.Model(&Virtual{}).Debug().Where("id>0").Update("inited_at", 0).Error
}

func (o *Virtual) Updates(m map[string]interface{}) error {
	return DB.Model(o).Debug().Updates(m).Error
}

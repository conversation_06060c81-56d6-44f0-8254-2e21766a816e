package router

import (
	"cpn-ai/controller"
	"cpn-ai/controller/kol"
	"cpn-ai/controller/manage"
	"cpn-ai/controller/master"
	"cpn-ai/controller/master_node"
	"cpn-ai/controller/notify/alipay"
	"cpn-ai/controller/notify/wechatpay"
	v1 "cpn-ai/controller/v1"
	"cpn-ai/middleware"
	"encoding/gob"

	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
)

func SetApiRouter(router *gin.Engine) {
	// 使用 Cookie 作为会话存储
	gob.Register(&middleware.MyClaims{})
	//store := cookie.NewStore([]byte("secret"))
	//router.Use(sessions.Sessions("mysession", store))

	//router.Use(middleware.CORS())

	router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, AccessToken, X-CSRF-Token, Authorization, Token")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204) // OPTIONS 请求不需要进入后续处理，直接返回
			return
		}

		c.Next()
	})

	apiRouter := router.Group("/api")
	//apiRouter.Use(gzip.Gzip(gzip.DefaultCompression))
	apiRouter.Use(gzip.Gzip(gzip.DefaultCompression, gzip.WithExcludedPaths([]string{
		"/api/filedown", // 排除整个文件下载路径
	})))

	apiRouter.GET("live", controller.LiveApi.Live)
	//apiRouter.Use(middleware.GlobalAPIRateLimit())
	{
		apiRouter.POST("project/publish", controller.ProjectApi.Publish)
		apiRouter.POST("project/deploy", controller.ProjectApi.Deploy)

		nodeAuth := apiRouter.Group("/")
		nodeAuth.Use(middleware.NodeAuth())
		{
			//apiRouter.POST("master/docker/detail", master_node.DockerApi)

			nodeAuth.POST("master/info/startup_live", master.InfoApi.StartupLive)
			nodeAuth.POST("master/info/instance_live", master.InfoApi.InstanceLive)
			nodeAuth.POST("master/report/docker_commit", master.ReportApi.DockerCommit)
			nodeAuth.POST("master/report/docker_push", master.ReportApi.DockerPush)
			nodeAuth.POST("master/report/virtual_local_images", master.ReportApi.VirtualLocalImages)
			nodeAuth.POST("master/report/virtual_local_new_images", master.ReportApi.VirtualLocalNewImages)
			nodeAuth.POST("master/report/virtual_inspect_image", master.ReportApi.VirtualInspectImage)
			nodeAuth.POST("master/report/virtual_info", master.ReportApi.VirtualInfo)
			nodeAuth.POST("master/report/virtual_timeout", master.ReportApi.VirtualTimeout)
			nodeAuth.POST("master/report/node_info", master.ReportApi.NodeInfo)
			nodeAuth.POST("master/report/startup_success", master.ReportApi.StartupSuccess)
			nodeAuth.POST("master/report/startup_fail", master.ReportApi.StartupFail)

			//nodeAuth.POST("master/node/instance_live", master_node.NodeApi.InstanceLive)
			nodeAuth.POST("master/node/report_live", master_node.NodeApi.ReportLive)
			nodeAuth.POST("master/node/report_startup_success", master_node.NodeApi.ReportStartupSuccess)

			nodeAuth.POST("master/node/gpu_models", master_node.NodeApi.GpuModels)
			nodeAuth.POST("master/node/virtuals", master_node.NodeApi.Virtuals)
			nodeAuth.POST("master/node/pods", master_node.NodeApi.Pods)
			nodeAuth.POST("master/node/pod_images", master_node.NodeApi.PodImages)
			nodeAuth.POST("master/virtual/report", master_node.VirtualApi.Report) //虚拟机
			nodeAuth.POST("master/virtual/report_timeout", master_node.VirtualApi.ReportTimeout)
		}

		apiRouter.POST("wechatpay/notify", wechatpay.Notify)
		apiRouter.POST("alipay/notify", alipay.Notify)

		apiRouter.GET("sys/weburl2nginx", controller.SysApi.InstanceWebUrl2Nginx)
		apiRouter.GET("sys/weburl", controller.SysApi.InstanceWebUrl)
		apiRouter.GET("sys/lastusetime", controller.SysApi.InstanceLastUseTime)

		apiRouter.POST("gpu/models", controller.GpuApi.Models)
		apiRouter.POST("pod/access", controller.PodApi.Access)
		apiRouter.POST("pod/gpu_instances", controller.PodApi.GpuInstances)
		apiRouter.POST("pod/llm_models", controller.PodApi.LlmModels)
		apiRouter.POST("pod/instances", controller.PodApi.PodInstances)
		apiRouter.POST("pod/labels", controller.LabelApi.Select)
		apiRouter.POST("notice/list", controller.NoticeApi.List)

		apiRouter.POST("pod/stat_pull", manage.StatApi.StatPullPods)

		apiRouter.POST("recharge/products", controller.RechargeApi.GetProductList)

		apiRouter.POST("/virtual/info", controller.VirtualApi.Info)
		//apiRouter.POST("/virtual/info_redis", controller.VirtualApi.InfoFromRedis)
		//apiRouter.POST("/virtual/run_docker", controller.VirtualApi.RunDocker)
		//apiRouter.POST("/virtual/run_llmpod", controller.VirtualApi.RunLLMPod)
		//apiRouter.POST("/virtual/rm_docker", controller.VirtualApi.RmDocker)
		apiRouter.POST("/virtual/restart_frpc", controller.VirtualApi.RestartFrpc)

		apiRouter.POST("/fastchat/hello", controller.FastchatApi.Hello)

		apiRouter.GET("/event", v1.HelloApi.Event)
		apiRouter.GET("/relay_event", v1.HelloApi.RelayEvent)
		apiRouter.GET("/hello", v1.HelloApi.Hello)
		apiRouter.GET("/status", controller.GetStatus)
		apiRouter.GET("/notice", controller.GetNotice)
		apiRouter.GET("/about", controller.GetAbout)
		apiRouter.GET("/home_page_content", controller.GetHomePageContent)
		apiRouter.GET("/verification", middleware.CriticalRateLimit(), middleware.TurnstileCheck(), controller.SendEmailVerification)
		apiRouter.GET("/reset_password", middleware.CriticalRateLimit(), middleware.TurnstileCheck(), controller.SendPasswordResetEmail)
		apiRouter.POST("/user/reset", middleware.CriticalRateLimit(), controller.ResetPassword)
		//apiRouter.GET("/oauth/github", middleware.CriticalRateLimit(), controller.GitHubOAuth)
		//apiRouter.GET("/oauth/state", middleware.CriticalRateLimit(), controller.GenerateOAuthCode)
		//apiRouter.GET("/oauth/wechat", middleware.CriticalRateLimit(), controller.WeChatAuth)
		//apiRouter.GET("/oauth/wechat/bind", middleware.CriticalRateLimit(), middleware.UserAuth(), controller.WeChatBind)
		//apiRouter.GET("/oauth/email/bind", middleware.CriticalRateLimit(), middleware.UserAuth(), controller.EmailBind)

		systemAuth := apiRouter.Group("/")
		systemAuth.Use(middleware.SysAuth())
		{
			systemAuth.GET("sys/lastchecktime", controller.SysApi.InstanceLastCheckTime)
			systemAuth.GET("sys/instanceinfo", controller.SysApi.InstanceInfo)
		}

		apiRouter.GET("site/conf", controller.SiteApi.Conf)
		apiRouter.GET("site/time", controller.SiteApi.Time)
		userAuth := apiRouter.Group("/")
		userAuth.Use(middleware.UserAuth())
		{
			userAuth.POST("amount/balance", controller.AmountApi.GetBalance)
			userAuth.POST("recharge/launch", controller.RechargeApi.Launch)
			userAuth.POST("recharge/launch_buy", controller.RechargeApi.LaunchBuy)
			userAuth.POST("recharge/balance", controller.RechargeApi.GetBalance)
			userAuth.POST("recharge/apply", controller.InvoiceApi.ApplyInvoice)
			userAuth.POST("recharge/query", controller.RechargeApi.QueryByOutTradeNo)

			userAuth.POST("/identity/qrcode", controller.IdentityApi.QrCode)
			userAuth.POST("/identity/query", controller.IdentityApi.Query)

			userAuth.POST("/identity/send_edu_email", controller.IdentityApi.SendEduEmail)
			userAuth.POST("/identity/verify_edu_email", controller.IdentityApi.VerifyEduEmail)

			userAuth.POST("/identity/student_certify", controller.IdentityApi.StudentCertify)
			userAuth.POST("/identity/student_info", controller.IdentityApi.StudentInfo)

			userAuth.POST("/identity/company_certify", controller.IdentityApi.CompanyCertify)
			userAuth.POST("/identity/company_info", controller.IdentityApi.CompanyInfo)

			userAuth.POST("invitation/invitees", controller.InvitationApi.Invitees)
			userAuth.POST("invitation/rewards", controller.InvitationApi.Rewards)
			userAuth.POST("invitation/stats", controller.InvitationApi.Stats)
			userAuth.POST("invitation/set", controller.InvitationApi.SetAction)

			userAuth.POST("withdraw/balance", controller.WithdrawApi.Balance)
			userAuth.POST("withdraw/add_account", controller.WithdrawApi.AddAccount)
			userAuth.POST("withdraw/accounts", controller.WithdrawApi.Accounts)
			userAuth.POST("withdraw/apply", controller.WithdrawApi.Apply)
			userAuth.POST("withdraw/applys", controller.WithdrawApi.Applys)
			userAuth.POST("/withdraw/rewards", controller.WithdrawApi.RewardRecords)
			userAuth.POST("withdraw/set", controller.WithdrawApi.SetAction)

			userAuth.POST("card/buy", controller.CardApi.Buy)
			userAuth.POST("card/bind", controller.CardApi.Bind)
			userAuth.POST("card/list", controller.CardApi.List)
			userAuth.POST("card/valid_count", controller.CardApi.ValidCount)
			userAuth.POST("coupon/list", controller.CouponApi.List)

			userAuth.POST("instance/settle_price", controller.InstanceApi.SettlePrice)
			userAuth.POST("instance/price", controller.InstanceApi.Price)
			userAuth.POST("instance/create", controller.InstanceApi.Create)
			userAuth.POST("instance/hidden", controller.InstanceApi.Hidden)
			userAuth.POST("instance/status", controller.InstanceApi.Status)
			userAuth.POST("instance/monitor", controller.InstanceApi.MointorData)
			userAuth.POST("instance/monitor/metrics", controller.InstanceApi.MointorMetrics)
			userAuth.POST("instance/set_action", controller.InstanceApi.SetAction)

			userAuth.POST("instance/list", controller.InstanceApi.List)
			userAuth.POST("instance/container_info", controller.InstanceApi.ContainerInfo)
			userAuth.POST("instance/container_logs", controller.InstanceApi.ContainerLogs)
			userAuth.POST("instance/startup", controller.InstanceApi.Startup)
			userAuth.POST("instance/startup_abort", controller.InstanceApi.StartupAbort)
			userAuth.POST("instance/shutdown", controller.InstanceApi.Shutdown)
			userAuth.POST("instance/destroy", controller.InstanceApi.Destroy)
			userAuth.POST("instance/shutdown_regular", controller.InstanceApi.SetShutdownRegularTime)
			userAuth.POST("instance/restart", controller.InstanceApi.Restart)
			userAuth.POST("instance/check", controller.InstanceApi.Check)
			userAuth.POST("instance/save_image", controller.InstanceApi.SaveImage)
			userAuth.POST("instance/save_image_abort", controller.InstanceApi.SaveImageAbort)
			userAuth.POST("instance/renewal", controller.InstanceApi.Renewal)
			userAuth.POST("instance/change2usage", controller.InstanceApi.ChangeToUsage)
			userAuth.POST("instance/change2sub", controller.InstanceApi.ChangeToSub)
			userAuth.POST("invoice/list", controller.InvoiceApi.GetInvoiceList)
			userAuth.POST("invoice/apply", controller.InvoiceApi.ApplyInvoice)
			userAuth.POST("invoice/detail", controller.InvoiceApi.GetInvoiceDetail)

			userAuth.POST("tasklog/list", controller.TaskLog.List)

			userAuth.POST("instit/list", controller.InstitApi.List)
			userAuth.POST("teacher/list", controller.TeacherApi.List)
			userAuth.POST("course/list", controller.CourseApi.List)
			userAuth.POST("tutorial/list", controller.TutorialApi.List)
			userAuth.POST("tutorial/view", controller.TutorialApi.View)

			userAuth.POST("class_room/list", controller.ClassRoomApi.List)
			userAuth.POST("kol/class_room/apply", kol.ClassRoomApi.Apply)
			userAuth.POST("kol/class_room/list", kol.ClassRoomApi.List)
			userAuth.POST("kol/class_room/auditing", kol.ClassRoomApi.Auditing)
			userAuth.POST("kol/class_room/audited", kol.ClassRoomApi.Audited)
			userAuth.POST("kol/class_room/set", kol.ClassRoomApi.Set)

			userAuth.POST("kol/class_room/list_pod", kol.ClassRoomApi.ListPod)

			userAuth.POST("kol/class_room/list_stu", kol.ClassRoomApi.ListStu)
			userAuth.POST("kol/class_room/bind_stu", kol.ClassRoomApi.BindStu)
			userAuth.POST("kol/class_room/batch_stu", kol.ClassRoomApi.BatchStu)
			userAuth.POST("kol/class_room/remove_stu", kol.ClassRoomApi.RemoveStu)
			userAuth.POST("kol/class_room/set_stu", kol.ClassRoomApi.SetStu)

			userAuth.POST("kol/pod/apply", kol.PodApi.Apply)
			userAuth.POST("kol/pod/list", kol.PodApi.List)
			userAuth.POST("kol/pod/auditing", kol.PodApi.Auditing)
			userAuth.POST("kol/pod/audited", kol.PodApi.Audited)

			userAuth.POST("kol/pod/catalogues", kol.PodApi.Catalogues)
			userAuth.POST("kol/pod/onsale", kol.PodApi.OnSale)
			userAuth.POST("kol/pod/offsale", kol.PodApi.OffSale)
			userAuth.POST("kol/pod/del", kol.PodApi.Hidden)

			userAuth.POST("kol/image/commit_local", kol.PodImageApi.CommitLocal)
			userAuth.POST("kol/image/commit", kol.PodImageApi.CommitPush)
			userAuth.POST("kol/image/commit_push", kol.PodImageApi.CommitPush)
			userAuth.POST("kol/image/commit_logs", kol.PodImageApi.CommitLogs)
			userAuth.POST("kol/image/commit_abort", kol.PodImageApi.CommitAbort)
			userAuth.POST("kol/image/list", kol.PodImageApi.List)
			userAuth.POST("kol/image/select", kol.PodImageApi.Select)
			userAuth.POST("kol/image/del", kol.PodImageApi.Delete)
			userAuth.POST("kol/image/onsale", kol.PodImageApi.OnSale)
			userAuth.POST("kol/image/offsale", kol.PodImageApi.OffSale)
			userAuth.POST("kol/instance/create", kol.InstanceApi.CreateAndStartup)
			userAuth.POST("kol/instance/logs_docker", kol.InstanceApi.LogsDocker)

			userAuth.POST("pod/favorite", controller.PodApi.Favorite)
			userAuth.POST("pod/static/upload", controller.PodApi.UploadFile)
			userAuth.POST("pod/static/list", controller.PodApi.ListFile)
			userAuth.POST("pod/static/del", controller.PodApi.DeleteFile)
			userAuth.POST("pod/static/markdown", controller.PodApi.Markdown)

			userAuth.POST("pod_image/list", controller.PodImageApi.List)
			userAuth.POST("pod_image/private_pod_image", controller.PodImageApi.PrivatePodImage)
			userAuth.POST("pod_image/squash", controller.PodImageApi.Squash)
			userAuth.POST("pod_image/del", controller.PodImageApi.Delete)
			userAuth.POST("pod_image/resume", controller.PodImageApi.ResumeLastImage)
			userAuth.POST("pod_image/set_title", controller.PodImageApi.SetTitle)
			userAuth.POST("pod_image/set_share", controller.PodImageApi.SetShare)
			userAuth.POST("pod_image/status", controller.PodImageApi.Status)

			userAuth.POST("files", controller.FilesApi.Files)
			userAuth.GET("filedown", controller.FilesApi.Files)

			userAuth.POST("train_files", controller.TrainFilesApi.Files)
			userAuth.GET("train_filedown", controller.TrainFilesApi.Files)
		}
		adminRoute := apiRouter.Group("/")
		adminRoute.Use(middleware.AdminAuth())
		{
			//adminRoute.POST("amount/addquick", controller.AmountApi.AddQuick)
			//adminRoute.POST("/", controller.CreateUser)
			//adminRoute.GET("/", controller.GetAllUsers)
			//adminRoute.GET("/search", controller.SearchUsers)
			//adminRoute.GET("/:id", controller.GetUser)
			//adminRoute.POST("/", controller.CreateUser)
			//adminRoute.POST("/manage", controller.ManageUser)
			//adminRoute.PUT("/", controller.UpdateUser)
			//adminRoute.DELETE("/:id", controller.DeleteUser)
		}

		userRoute := apiRouter.Group("/user")
		{
			userRoute.POST("/get_smscode", middleware.GlobalAPIRateLimit(), controller.SendSms)
			userRoute.POST("/register", middleware.GlobalAPIRateLimit(), middleware.TurnstileCheck(), controller.Register)
			userRoute.POST("/get_token", middleware.GlobalAPIRateLimit(), controller.GetToken)
			userRoute.POST("/login", middleware.GlobalAPIRateLimit(), controller.Login)
			userRoute.POST("/login_sms", middleware.GlobalAPIRateLimit(), controller.LoginSms)
			userRoute.POST("/login_scan", middleware.GlobalAPIRateLimit(), controller.LoginScan)
			userRoute.POST("/login_qr", middleware.GlobalAPIRateLimit(), controller.LoginQr)

			userRoute.POST("/destroy", controller.Destroy)

			selfRoute := userRoute.Group("/")
			selfRoute.Use(middleware.UserAuth())
			{
				selfRoute.POST("/logout", controller.Logout)
				selfRoute.POST("info", controller.UserApi.Info)
				selfRoute.POST("set_info", controller.UserApi.SetUserInfo)
				selfRoute.POST("set_password", controller.UserApi.ChangePasswordByMobile)

			}
		}

		optionRoute := apiRouter.Group("/option")
		optionRoute.Use(middleware.RootAuth())
		{
			//optionRoute.GET("/", controller.GetOptions)
			//optionRoute.PUT("/", controller.UpdateOption)
		}
		channelRoute := apiRouter.Group("/channel")
		channelRoute.Use(middleware.AdminAuth())
		{
			//channelRoute.GET("/", controller.GetAllChannels)
			//channelRoute.GET("/search", controller.SearchChannels)
			//channelRoute.GET("/models", controller.ListModels)
			//channelRoute.GET("/:id", controller.GetChannel)
			//channelRoute.GET("/test", controller.TestAllChannels)
			//channelRoute.GET("/test/:id", controller.TestChannel)
			//channelRoute.GET("/update_balance", controller.UpdateAllChannelsBalance)
			//channelRoute.GET("/update_balance/:id", controller.UpdateChannelBalance)
			//channelRoute.POST("/", controller.AddChannel)
			//channelRoute.PUT("/", controller.UpdateChannel)
			//channelRoute.DELETE("/disabled", controller.DeleteDisabledChannel)
			//channelRoute.DELETE("/:id", controller.DeleteChannel)
		}

		//rechargeRoute := apiRouter.Group("/recharge")
		//rechargeRoute.Use(middleware.UserAuth())
		//{
		//	rechargeRoute.POST("/launch", controller.RechargeApi.Launch)
		//	rechargeRoute.POST("/query", controller.RechargeApi.QueryByOutTradeNo)
		//	rechargeRoute.POST("/balance", controller.RechargeApi.GetBalance)
		//}

		tokenRoute := apiRouter.Group("/token")
		tokenRoute.Use(middleware.UserAuth())
		{
			//tokenRoute.GET("/", controller.GetAllTokens)
			//tokenRoute.GET("/search", controller.SearchTokens)
			//tokenRoute.GET("/:id", controller.GetToken)
			tokenRoute.POST("/add", controller.TokenApi.Add)
			tokenRoute.POST("/modify", controller.TokenApi.Modify)
			tokenRoute.POST("/del", controller.TokenApi.Del)
			tokenRoute.POST("/list", controller.TokenApi.List)
			//tokenRoute.PUT("/", controller.UpdateToken)
			//tokenRoute.DELETE("/:id", controller.DeleteToken)
		}

		manageRoute := apiRouter.Group("/manage")
		manageRoute.Use(middleware.JwtTokenCenter())
		{

			manageRoute.POST("/service_test", manage.ServiceTestApi.NodeTest)

			manageRoute.POST("/sys/ignore_alarm", manage.SysApi.IgnoreAlarm)
			manageRoute.POST("/sys/info", manage.SysApi.Info)
			manageRoute.POST("/sys/stats", manage.SysApi.Stats)

			manageRoute.POST("/stat/stat_day", manage.StatApi.StatDay)
			manageRoute.POST("/stat/stat_days", manage.StatApi.StatDays)

			manageRoute.POST("/coupon/gen_code", manage.CouponApi.GenCode)
			manageRoute.POST("/coupon/gen", manage.CouponApi.Gen)
			manageRoute.POST("/coupon/modify", manage.CouponApi.Gen)
			manageRoute.POST("/coupon/list", manage.CouponApi.List)
			manageRoute.POST("/coupon/onsale", manage.CouponApi.OnSale)
			manageRoute.POST("/coupon/offsale", manage.CouponApi.OffSale)
			manageRoute.POST("/coupon/del", manage.CouponApi.Del)
			manageRoute.POST("/coupon/cards", manage.CouponApi.Cards)
			manageRoute.POST("/coupon/discard", manage.CouponApi.Discard)

			manageRoute.POST("/identity/companys", manage.IdentityApi.Companys)
			manageRoute.POST("/identity/audit_company", manage.IdentityApi.AuditCompany)

			manageRoute.POST("/identity/students", manage.IdentityApi.Students)
			manageRoute.POST("/identity/audit_student", manage.IdentityApi.AuditStudent)

			manageRoute.POST("/node/detail", manage.NodeApi.DetailFromNode)
			manageRoute.POST("/node/running_command", manage.NodeApi.RunningCommandFromNode)
			manageRoute.POST("/node/action", manage.NodeApi.ActionFromNode)
			manageRoute.POST("/node/set", manage.NodeApi.Action)

			manageRoute.POST("/nginx/list", manage.NginxApi.List)
			manageRoute.POST("/nginx/get", manage.NginxApi.Get)
			manageRoute.POST("/nginx/remove_instance", manage.NginxApi.RemoveInstance)

			manageRoute.POST("/node/static", manage.NodeApi.StaticFromNode)
			manageRoute.POST("/node/list", manage.NodeApi.List)

			manageRoute.POST("/virtual/add", manage.VirtualApi.Add)
			manageRoute.POST("/virtual/list", manage.VirtualApi.List)
			manageRoute.POST("/virtual/set", manage.VirtualApi.ActionFromNode)
			manageRoute.POST("/virtual/action", manage.VirtualApi.ActionFromNode)
			manageRoute.POST("/virtual/detail", manage.VirtualApi.DetailFromNode)
			manageRoute.POST("/virtual/container_info", manage.VirtualApi.ContainerInfo)
			manageRoute.POST("/virtual/local_images", manage.VirtualApi.LocalImages)
			manageRoute.POST("/virtual/remove_image", manage.VirtualApi.RemoveImage)
			manageRoute.POST("/virtual/prune_image", manage.VirtualApi.PruneImage)
			manageRoute.POST("/virtual/refresh_image", manage.VirtualApi.RefreshImage)
			manageRoute.POST("/virtual/spell_run_command", manage.VirtualApi.SpellRunCommandFromNode)
			manageRoute.POST("/virtual/pull_image", manage.VirtualApi.PullImage)

			manageRoute.POST("/virtual/set_status", manage.VirtualApi.SetStatus)
			manageRoute.POST("/virtual/set_gpu_model", manage.VirtualApi.SetGpuModel)
			manageRoute.POST("/virtual/set_node", manage.VirtualApi.SetNode)
			manageRoute.POST("/virtual/set_pod_ids", manage.VirtualApi.SetPodIds)
			manageRoute.POST("/virtual/status_log", manage.VirtualApi.StatusLog)
			manageRoute.POST("/virtual/remove_docker", manage.VirtualApi.RemoveDockerFromMemory)

			manageRoute.POST("/virtual/del", manage.VirtualApi.Del)

			//manageRoute.POST("/virtual/reload", controller.VirtualApi.Reload) node节点5分钟会自动拉取一次
			//manageRoute.POST("/gpu/list", controller.GpuApi.ListForManage)
			manageRoute.POST("/gpu_model/list", manage.GpuModelApi.List)
			manageRoute.POST("/amount/balance_check", controller.AmountApi.BalanceCheck)
			manageRoute.POST("/amount/addquick", controller.AmountApi.AddQuick)
			manageRoute.POST("/amount/costquick", controller.AmountApi.CostQuick)
			manageRoute.POST("/amount/batchquick", controller.AmountApi.BatchQuick)

			manageRoute.POST("/instit/list", manage.InstitApi.List)
			manageRoute.POST("/instit/static/upload", manage.InstitStaticApi.UploadFile)
			manageRoute.POST("/instit/static/list", manage.InstitStaticApi.ListFile)
			manageRoute.POST("/instit/static/del", manage.InstitStaticApi.DeleteFile)
			manageRoute.POST("/instit/static/markdown", manage.InstitStaticApi.Markdown)
			manageRoute.POST("/instit/save", manage.InstitApi.Save)
			manageRoute.POST("/instit/update", manage.InstitApi.Update)
			manageRoute.POST("/teacher/list", manage.TeacherApi.List)
			manageRoute.POST("/teacher/save", manage.TeacherApi.Save)
			manageRoute.POST("/teacher/update", manage.TeacherApi.Update)
			manageRoute.POST("/course/list", manage.CourseApi.List)
			manageRoute.POST("/course/save", manage.CourseApi.Save)
			manageRoute.POST("/course/update", manage.CourseApi.Update)
			manageRoute.POST("/tutorial/list", manage.TutorialApi.List)
			manageRoute.POST("/tutorial/save", manage.TutorialApi.Save)
			manageRoute.POST("/tutorial/update", manage.TutorialApi.Update)

			manageRoute.POST("/class_room/list", manage.ClassRoomApi.List)
			manageRoute.POST("/class_room/list_stu", manage.ClassRoomApi.ListStu)

			manageRoute.POST("/pod/list", manage.PodApi.List)
			manageRoute.POST("/pod/save", manage.PodApi.Save)
			manageRoute.POST("/pod/save_simple", manage.PodApi.SaveSimple)
			manageRoute.POST("/pod/audited", kol.PodApi.Audited)
			manageRoute.POST("/pod/transfer", manage.PodApi.Transfer)
			manageRoute.POST("/pod/batch_update_author_name", manage.PodApi.BatchUpdateAuthorName)

			manageRoute.POST("pod/static/upload", controller.PodApi.UploadFile)
			manageRoute.POST("pod/static/list", controller.PodApi.ListFile)
			manageRoute.POST("pod/static/del", controller.PodApi.DeleteFile)
			manageRoute.POST("pod/static/markdown", controller.PodApi.Markdown)

			manageRoute.POST("/label/save", manage.LabelApi.Save)
			manageRoute.POST("/label/list", manage.LabelApi.List)
			manageRoute.POST("/label/select", manage.LabelApi.Select)

			manageRoute.POST("/image_label/save", manage.ImageLabelApi.Save)
			manageRoute.POST("/image_label/list", manage.ImageLabelApi.List)
			manageRoute.POST("/image_label/select", manage.ImageLabelApi.Select)

			manageRoute.POST("/notice/save", manage.NoticeApi.Save)
			manageRoute.POST("/notice/list", manage.NoticeApi.List)

			manageRoute.POST("/material/list", manage.MaterialApi.List)
			manageRoute.POST("/material/add", manage.MaterialApi.Add)
			manageRoute.POST("/material/del", manage.MaterialApi.Del)

			manageRoute.POST("/monitor/list", manage.MonitorApi.List)

			manageRoute.POST("/pod_image/list", manage.PodImageApi.List)
			manageRoute.POST("/pod_image/detail", manage.PodImageApi.Detail)
			manageRoute.POST("/pod_image/commit_logs", manage.PodImageApi.CommitLogs)
			manageRoute.POST("/pod_image/add", manage.PodImageApi.Add)
			manageRoute.POST("/pod_image/onsale", manage.PodImageApi.OnSale)
			manageRoute.POST("/pod_image/offsale", manage.PodImageApi.OffSale)
			manageRoute.POST("/pod_image/save_image_abort", manage.PodImageApi.SaveImageAbort)
			manageRoute.POST("/pod_image/action", manage.PodImageApi.Action)

			manageRoute.POST("/instance/list", manage.InstanceApi.List)
			manageRoute.POST("/instance/stats_running", manage.InstanceApi.StatsRunning)
			manageRoute.POST("/instance/create", manage.InstanceApi.Create)
			manageRoute.POST("/instance/startup", manage.InstanceApi.Startup)
			manageRoute.POST("/instance/startup_abort", manage.InstanceApi.StartupAbort)
			manageRoute.POST("/instance/shutdown", manage.InstanceApi.Shutdown)
			manageRoute.POST("/instance/hand_shutdown", manage.InstanceApi.HandShutdown)
			manageRoute.POST("/instance/logs_docker", manage.InstanceApi.LogsDocker)
			manageRoute.POST("/instance/records", manage.InstRecordApi.List)
			manageRoute.POST("/invoice/list", manage.InvoiceAdminApi.ListInvoices)
			manageRoute.POST("/invoice/apply", manage.InvoiceAdminApi.ProcessInvoice)
			manageRoute.POST("/invoice/detail", manage.InvoiceAdminApi.GetInvoiceDetail)
			manageRoute.POST("/instance/monitor", manage.InstanceApi.MointorData)
			manageRoute.POST("/instance/monitor/metrics", manage.InstanceApi.MointorMetrics)
			manageRoute.POST("/invoice/upload", manage.InvoiceAdminApi.UploadFile)

			manageRoute.POST("withdraw/balance", manage.WithdrawApi.Balance)
			manageRoute.POST("/withdraw/accounts", manage.WithdrawApi.Accounts)
			manageRoute.POST("/withdraw/applys", manage.WithdrawApi.Applys)
			manageRoute.POST("/withdraw/pays", manage.WithdrawApi.Pays)
			manageRoute.POST("/withdraw/rewards", manage.WithdrawApi.RewardRecords)
			manageRoute.POST("/withdraw/set", manage.WithdrawApi.SetAction)

			manageRoute.POST("instance/restart", manage.InstanceApi.Restart)
			manageRoute.POST("instance/save_image", manage.InstanceApi.SaveImage)
			manageRoute.POST("instance/save_image_continue", manage.InstanceApi.SaveImageContinue)
			manageRoute.POST("instance/save_image_create", manage.InstanceApi.SaveImageCreate)

			manageRoute.POST("instance/save_image_abort", manage.InstanceApi.SaveImageAbort)

			manageRoute.POST("/instance/set_nginx", manage.InstanceApi.SetNginx)
			manageRoute.POST("/instance/remove_nginx", manage.InstanceApi.RemoveNginx)
			manageRoute.POST("/instance/batch_reset_nginx", manage.InstanceApi.BatchResetNginx)

			manageRoute.POST("token/remote_list", manage.TokenApi.RemoteList)

			manageRoute.POST("/user/list", manage.UserApi.List)
			manageRoute.POST("/user/insider", manage.UserApi.Insider)
			manageRoute.POST("/user/set_type", manage.UserApi.SetUserType)
			manageRoute.POST("/user/set_remark", manage.UserApi.SetRemark)
			manageRoute.POST("/user/set_info", manage.UserApi.SetUserInfo)
			manageRoute.POST("/user/get_token", manage.UserApi.GetToken)
			manageRoute.POST("/user/destroy", manage.UserApi.Destroy)
			manageRoute.POST("/user/cancel_student", manage.UserApi.CancelStudent)

			manageRoute.POST("/user/action", manage.UserApi.Action)
			manageRoute.POST("/user/warn_send", manage.UserApi.WarnSend)

			manageRoute.POST("/startup_log/list", manage.StartupLog.List)
			manageRoute.POST("/task_log/list", manage.TaskLog.List)

			manageRoute.POST("recharge/list", manage.Recharge.List)
			manageRoute.POST("recharge/handle", manage.Recharge.Handle)
			manageRoute.POST("recharge/query", manage.Recharge.Query)

			manageRoute.POST("amount/balance", manage.AmountApi.Balance)

		}

		podRoute := apiRouter.Group("/pod")
		podRoute.Use(middleware.JwtTokenCenter())
		{
			//tokenRoute.POST("/add", controller.TokenApi.Add)
			//tokenRoute.POST("/modify", controller.TokenApi.Modify)
			//tokenRoute.POST("/del", controller.TokenApi.Del)
			podRoute.POST("/list", controller.PodApi.List)
		}

		virtualRoute := apiRouter.Group("/virtual")
		virtualRoute.Use(middleware.JwtTokenCenter())
		{
			//tokenRoute.POST("/add", controller.TokenApi.Add)
			//tokenRoute.POST("/modify", controller.TokenApi.Modify)
			//tokenRoute.POST("/del", controller.TokenApi.Del)
			virtualRoute.POST("/list", controller.VirtualApi.List)
		}

		redemptionRoute := apiRouter.Group("/redemption")
		redemptionRoute.Use(middleware.AdminAuth())
		{
			//redemptionRoute.GET("/", controller.GetAllRedemptions)
			//redemptionRoute.GET("/search", controller.SearchRedemptions)
			//redemptionRoute.GET("/:id", controller.GetRedemption)
			//redemptionRoute.POST("/", controller.AddRedemption)
			//redemptionRoute.PUT("/", controller.UpdateRedemption)
			//redemptionRoute.DELETE("/:id", controller.DeleteRedemption)
		}
		//logRoute := apiRouter.Group("/log")
		//logRoute.GET("/", middleware.AdminAuth(), controller.GetAllLogs)
		//logRoute.DELETE("/", middleware.AdminAuth(), controller.DeleteHistoryLogs)
		//logRoute.GET("/stat", middleware.AdminAuth(), controller.GetLogsStat)
		//logRoute.GET("/self/stat", middleware.UserAuth(), controller.GetLogsSelfStat)
		//logRoute.GET("/search", middleware.AdminAuth(), controller.SearchAllLogs)
		//logRoute.GET("/self", middleware.UserAuth(), controller.GetUserLogs)
		//logRoute.GET("/self/search", middleware.UserAuth(), controller.SearchUserLogs)
		groupRoute := apiRouter.Group("/group")
		groupRoute.Use(middleware.AdminAuth())
		{
			//groupRoute.GET("/", controller.GetGroups)
		}
	}
	//for _, route := range router.Routes() {
	//	logger.Info(fmt.Sprintf("方法: %s, 路由: %s", route.Method, route.Path))
	//}
}

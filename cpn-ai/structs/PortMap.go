package structs

type PortMap struct {
	Title string `json:"title"`
	//<PERSON>           string `json:"des,omitempty"`
	HostPort      string `json:"host_port"`
	ContainerPort string `json:"container_port"`
	Url           string `json:"url,omitempty"`
}

type StrarupPortMap struct {
	Title    string `json:"title"`
	Url      string `json:"url"`
	InnerUrl string `json:"inner_url"`
	InnerAt  int64  `json:"inner_at"` //检测能访问的时间，0为不能访问
}

type OutStartupPortMap struct {
	Title   string `json:"title"`
	Url     string `json:"url"`
	InnerAt int64  `json:"inner_at"` //检测能访问的时间，0为不能访问
}

package structs

import "cpn-ai/common/jsontime"

type RunningCommand struct {
	LogKey      string            `json:"log_key,omitempty"`
	StartupMark string            `json:"startup_mark,omitempty"`
	PodId       uint              `json:"pod_id,omitempty"`
	ImageId     uint              `json:"image_id,omitempty"`
	VirtualId   uint              `json:"virtual_id,omitempty"`
	VirtualHost string            `json:"virtual_host,omitempty"`
	UuId        string            `json:"uu_id,omitempty"`
	Command     string            `json:"command,omitempty"`
	Msg         string            `json:"msg,omitempty"`
	Progress    string            `json:"progress,omitempty"`
	StartTime   jsontime.JsonTime `json:"start_time"`
}

<!DOCTYPE html>
<html>
<head>
    <title>Server-Sent Events Test</title>
    <script>
        // 创建一个新的 EventSource 对象来连接到事件流端点
        //var eventSource = new EventSource('http://localhost:6001/api/relay_event');
        //var eventSource = new EventSource('http://localhost:6001/v1/conv/answer_stream?conv_uuid=818a5db01f5e4835a1ff922e0d26ad66&question_uuid=d335b40ebd9d4ed880c6acf0946c24f5');

        var eventSource = new EventSource('https://suanyun.cyuai.com/v1/conv/answer_stream?conv_uuid=818a5db01f5e4835a1ff922e0d26ad66&question_uuid=d335b40ebd9d4ed880c6acf0946c24f5');

        // 监听消息事件
        eventSource.onmessage = function(event) {
            // 将接收到的消息打印到控制台或更新 UI 中的元素
            console.log('Received message:', '========',event.data,'======');
            if (event.data=="[DONE]"){
                console.log("关闭链接")
                eventSource.close()
            }
        };

        // 监听事件流出现错误的事件
        eventSource.onerror = function(event) {
            console.error('EventSource failed:', event);
            eventSource.close(); // 关闭连接
        };
    </script>
</head>
<body>
<h1>Server-Sent Events Test</h1>
<p>请在浏览器的控制台查看接收到的事件消息。</p>
</body>
</html>

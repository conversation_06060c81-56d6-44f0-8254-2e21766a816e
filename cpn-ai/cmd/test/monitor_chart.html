<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>实例监控数据图表</title>
    <!-- 引入 ECharts 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
    <style>
        #main {
            width: 100%;
            height: 600px; /* 您可以根据需要调整图表高度 */
        }
        body {
            font-family: sans-serif;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .controls label, .controls textarea, .controls button {
            display: block;
            margin-bottom: 10px;
            width: 95%;
        }
         .controls textarea {
            height: 100px;
            font-family: monospace;
         }
    </style>
</head>
<body>

<h1>实例监控数据</h1>

<div class="controls">
    <label for="apiUrl">API URL:</label>
    <input type="text" id="apiUrl" value="https://online.chenyu.cn/api/instance/monitor">

    <label for="authToken">Authorization Token (Bearer):</label>
    <textarea id="authToken">Cpn eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo0MSwibW9iaWxlIjoiMTgwKioqKjk1MTEiLCJ1c2VybmFtZSI6IiIsImRpc3BsYXlfbmFtZSI6InJvaG9uIiwicm9sZSI6MSwic3RhdHVzIjowLCJhY2Nlc3NfdG9rZW4iOiIwYWFjMDAyNjRhNmM0Y2VjYjFmODE3NzgxMjAxMTljMSIsIm9wZXJhdG9yX2lkIjowLCJleHAiOjMzMjgwNzgxNjM2LCJpc3MiOiJDcG4iLCJuYmYiOjE3NDQ3ODE1MzZ9.GCQfX_cp1HRLoIS5DwsDmEV03VkxQTS9GTpBsAtsbbU</textarea>
    <small><b>安全警告:</b> 直接在此处粘贴令牌存在安全风险。</small>

    <label for="requestBody">Request Body (JSON):</label>
    <textarea id="requestBody">{
    "instance_uuid": "56b12abc549142cabd9b98715bdcd206",
    "start_time": 1745203800000,
    "endTime": 1745210729000,
    "metrics": [
        "DCGM_FI_DEV_FB_USED",
        "container_memory_working_set_bytes"
    ]
}</textarea>

    <button onclick="fetchAndDrawChart()">获取并绘制图表</button>
    <div id="status"></div>
</div>

<!-- 图表容器 -->
<div id="main"></div>

<script type="text/javascript">
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(document.getElementById('main'));
    var statusDiv = document.getElementById('status');

    // --- API 请求配置 ---
    // !! 安全警告: 不要在生产环境中直接暴露 Authorization Token !!
    const defaultApiUrl = 'https://online.chenyu.cn/api/instance/monitor';
    const defaultAuthToken = 'Cpn eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo0MSwibW9iaWxlIjoiMTgwKioqKjk1MTEiLCJ1c2VybmFtZSI6IiIsImRpc3BsYXlfbmFtZSI6InJvaG9uIiwicm9sZSI6MSwic3RhdHVzIjowLCJhY2Nlc3NfdG9rZW4iOiIwYWFjMDAyNjRhNmM0Y2VjYjFmODE3NzgxMjAxMTljMSIsIm9wZXJhdG9yX2lkIjowLCJleHAiOjMzMjgwNzgxNjM2LCJpc3MiOiJDcG4iLCJuYmYiOjE3NDQ3ODE1MzZ9.GCQfX_cp1HRLoIS5DwsDmEV03VkxQTS9GTpBsAtsbbU';
    const defaultRequestBody = {
        "instance_uuid": "56b12abc549142cabd9b98715bdcd206",
        "start_time": 1745203800000, // 示例开始时间戳 (毫秒)
        "endTime": 1745210729000,   // 示例结束时间戳 (毫秒)
        "metrics": [
            "DCGM_FI_DEV_FB_USED",
            "container_cpu_usage_seconds_total",
            "container_cpu_cfs_periods_total",
            "container_cpu_cfs_throttled_periods_total",
            "container_cpu_cfs_throttled_seconds_total",

            "container_memory_usage_bytes",
            "container_memory_max_usage_bytes",
            "container_memory_working_set_bytes",
            "container_memory_failures_total",

            "container_network_receive_bytes_total",
            "container_network_transmit_bytes_total",
            "container_network_receive_packets_total",
            "container_network_transmit_packets_total",
            "container_fs_usage_bytes",
            "container_fs_limit_bytes",
            "container_fs_reads_total",
            "container_fs_writes_total",

            "DCGM_FI_DEV_GPU_UTIL",
            "DCGM_FI_PROF_MEM_CLOCK",
            "DCGM_FI_DEV_MEM_USED",
            "DCGM_FI_DEV_MEM_TOTAL",

            "DCGM_FI_DEV_THERMAL",
            "DCGM_FI_DEV_POWER_USAGE",
            "DCGM_FI_DEV_POWER_LIMIT",
            "DCGM_FI_DEV_FAN_SPEED",

            "DCGM_FI_DEV_ECC_CURRENT",
            "DCGM_FI_DEV_ECC_PENDING",
            "DCGM_FI_DEV_XID_ERRORS"
        ]
    };

    // 从输入框获取配置或使用默认值
    function getConfig() {
        const apiUrl = document.getElementById('apiUrl').value || defaultApiUrl;
        const authToken = document.getElementById('authToken').value || defaultAuthToken;
        let requestBody;
        try {
            requestBody = JSON.parse(document.getElementById('requestBody').value);
        } catch (e) {
            statusDiv.textContent = '错误: 请求体 JSON 格式无效 - ' + e.message;
            statusDiv.style.color = 'red';
            throw new Error('Invalid JSON in request body');
        }
        return { apiUrl, authToken, requestBody };
    }


    // --- ECharts 配置 ---
    function getChartOption(apiData) {
        const seriesData = [];
        const legendData = [];

        if (!apiData || typeof apiData !== 'object') {
             console.error("API 返回的数据格式无效:", apiData);
             statusDiv.textContent = '错误: API 返回的数据格式无效。';
             statusDiv.style.color = 'red';
             return null; // 返回 null 表示配置失败
        }

        for (const metricName in apiData) {
            if (apiData.hasOwnProperty(metricName) && Array.isArray(apiData[metricName])) {
                legendData.push(metricName);
                const processedData = apiData[metricName].map(point => {
                    if (Array.isArray(point) && point.length === 2) {
                        // API 返回的时间戳是秒，ECharts 需要毫秒
                        const timestampMs = parseInt(point[0], 10) * 1000;
                        const value = parseFloat(point[1]);
                        return [timestampMs, value];
                    }
                    console.warn(`指标 ${metricName} 中发现无效数据点:`, point);
                    return null; // 忽略无效点
                }).filter(point => point !== null); // 过滤掉无效点

                seriesData.push({
                    name: metricName,
                    type: 'line',
                    smooth: true, // 让线条平滑
                    showSymbol: false, // 不显示数据点标记
                    data: processedData
                });
            } else {
                 console.warn(`忽略无效的指标数据: ${metricName}`, apiData[metricName]);
            }
        }

         if (seriesData.length === 0) {
            statusDiv.textContent = '警告: 未从 API 数据中解析出有效的图表系列。';
            statusDiv.style.color = 'orange';
            // 仍然返回一个空的 option，避免 ECharts 报错
         }

        return {
            tooltip: {
                trigger: 'axis',
                formatter: function (params) {
                    let result = echarts.format.formatTime('yyyy-MM-dd hh:mm:ss', params[0].value[0]);
                    params.forEach(function (item) {
                        // 处理可能存在的 NaN 或 undefined 值
                        const valueStr = (item.value && typeof item.value[1] !== 'undefined' && !isNaN(item.value[1]))
                                         ? item.value[1].toFixed(2) // 保留两位小数
                                         : 'N/A';
                        result += '<br/>' + item.marker + item.seriesName + ': ' + valueStr;
                    });
                    return result;
                },
                 axisPointer: {
                    animation: false
                }
            },
            legend: {
                data: legendData,
                bottom: 10 // 图例放在底部
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '10%', // 为图例留出空间
                containLabel: true
            },
            xAxis: {
                type: 'time', // X轴为时间类型
                splitLine: {
                    show: false
                }
            },
            yAxis: {
                type: 'value', // Y轴为数值类型
                scale: true, // 允许 Y 轴根据数据缩放
                splitLine: {
                    show: true
                },
                 axisLabel: {
                    formatter: function (value, index) {
                        // 简单的 Y 轴标签格式化，例如转换为 MB/GB 或其他单位
                        if (value > 1e9) return (value / 1e9).toFixed(1) + ' G';
                        if (value > 1e6) return (value / 1e6).toFixed(1) + ' M';
                        if (value > 1e3) return (value / 1e3).toFixed(1) + ' k';
                        return value;
                    }
                }
            },
            dataZoom: [ // 添加数据区域缩放功能
                {
                    type: 'inside', // 内部缩放（鼠标滚轮）
                    start: 0,
                    end: 100
                },
                {
                    start: 0,
                    end: 100,
                    handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                    handleSize: '80%',
                    handleStyle: {
                        color: '#fff',
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.6)',
                        shadowOffsetX: 2,
                        shadowOffsetY: 2
                    },
                    bottom: 40 // 缩放条位置
                }
            ],
            series: seriesData
        };
    }

    // --- 获取数据并绘制图表 ---
    async function fetchAndDrawChart() {
        statusDiv.textContent = '正在获取数据...';
        statusDiv.style.color = 'blue';
        myChart.showLoading(); // 显示加载动画

        let config;
        try {
            config = getConfig();
        } catch (e) {
            myChart.hideLoading();
            return; // 如果配置无效，则停止
        }

        const { apiUrl, authToken, requestBody } = config;

        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': authToken,
                    'Content-Type': 'application/json',
                    'Accept': '*/*'
                    // 可以根据需要添加 User-Agent 等其他 Header
                    // 'User-Agent': 'Apifox/1.0.0 (https://apifox.com)'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API 请求失败: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const responseData = await response.json();

            if (responseData.code !== 0) {
                throw new Error(`API 返回错误: code=${responseData.code}, msg=${responseData.msg}`);
            }

            if (!responseData.result || !responseData.result.data) {
                 throw new Error('API 返回的数据结构不符合预期，缺少 result.data');
            }

            // 获取 ECharts 配置项
            const option = getChartOption(responseData.result.data);

            if(option) {
                // 使用刚指定的配置项和数据显示图表。
                myChart.setOption(option);
                statusDiv.textContent = '图表绘制成功!';
                statusDiv.style.color = 'green';
            } else {
                 // getChartOption 内部已经设置了错误信息
                 // 可以选择清空图表或显示提示
                 myChart.clear();
            }


        } catch (error) {
            console.error('获取或处理数据时出错:', error);
            statusDiv.textContent = '错误: ' + error.message;
            statusDiv.style.color = 'red';
            // 可以选择清空图表
            // myChart.clear();
        } finally {
             myChart.hideLoading(); // 隐藏加载动画
        }
    }

    // 页面加载时可以自动绘制一次，或者等待用户点击按钮
    // fetchAndDrawChart(); // 如果需要页面加载时自动绘制，取消此行注释

</script>

</body>
</html>
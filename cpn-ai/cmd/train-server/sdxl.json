{"bucket_no_upscale": true, "bucket_reso_steps": 64, "cache_latents": true, "cache_latents_to_disk": true, "cache_text_encoder_outputs": true, "caption_extension": ".txt", "clip_skip": 1, "dynamo_backend": "no", "enable_bucket": true, "epoch": 1, "gradient_accumulation_steps": 1, "gradient_checkpointing": true, "huber_c": 0.1, "huber_schedule": "snr", "loss_type": "l2", "lr_scheduler": "constant", "lr_scheduler_args": [], "lr_scheduler_num_cycles": 1, "lr_scheduler_power": 1, "max_bucket_reso": 2048, "max_data_loader_n_workers": 0, "max_grad_norm": 1, "max_timestep": 1000, "max_token_length": 75, "max_train_steps": 1000, "min_bucket_reso": 256, "min_snr_gamma": 7, "mixed_precision": "bf16", "network_alpha": 16, "network_args": ["train_blocks=all"], "network_dim": 16, "network_module": "networks.lora", "network_train_unet_only": true, "no_half_vae": true, "noise_offset": 0.05, "noise_offset_type": "Original", "optimizer_args": ["weight_decay=0.05", "betas=0.9,0.98"], "optimizer_type": "AdamW", "output_name": "sdxl", "prior_loss_weight": 1, "resolution": "1024,1024", "sample_every_n_epochs": 1, "sample_sampler": "euler", "save_every_n_epochs": 1, "save_every_n_steps": 50, "save_model_as": "safetensors", "save_precision": "bf16", "sdpa": true, "seed": 42, "train_batch_size": 1, "unet_lr": 0.0003, "wandb_run_name": "sdxl"}
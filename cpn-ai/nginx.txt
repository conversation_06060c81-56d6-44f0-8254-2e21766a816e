lua_shared_dict my_shared_data 10m;
server
{
    listen 80;
    server_name suanyun.cyuai.com;
    index index.php index.html index.htm default.php default.htm default.html;
    root /www/wwwroot/suanyun.cyuai.com/dist;
    try_files $uri $uri/ @404_handler;  # 使用 @404_handler 处理 404 错误

    #SSL-START SSL相关配置，请勿删除或修改下一行带注释的404规则
    #error_page 404/404.html;
    #SSL-END

    #ERROR-PAGE-START  错误页配置，可以注释、删除或修改
    #error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #PHP-INFO-START  PHP引用配置，可以注释或修改
    include enable-php-00.conf;
    #PHP-INFO-END

    #REWRITE-START URL重写规则引用,修改后将导致面板设置的伪静态规则失效
    include /www/server/panel/vhost/rewrite/suanyun.cyuai.com.conf;
    #REWRITE-END


    location /v1/
    {
        proxy_pass http://127.0.0.1:6001/v1/;
        proxy_set_header Remote_addr $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    location /api/
    {
        proxy_pass http://127.0.0.1:6001/api/;
        proxy_set_header Remote_addr $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    location ^~/conversation {
        root /www/wwwroot/suanyun.cyuai.com;
        index index.html;
        try_files $uri $uri/ /conversation/index.html;
    }

location /setpodmapping {
  set $parm '';
  content_by_lua_block{
          local request_method = ngx.var.request_method
          local headers = ngx.req.get_headers()
          --for key, value in pairs(headers) do
           --   ngx.say(key .. ": " .. value)
          --end
          local token = headers["authorization"] or ""
          if token~="ac4f334sddbfe701dda3783124ef6822a" then
            ngx.say("no permission")
            return
          end
          --ngx.say(token)
          if request_method == "GET" then
                  local args = ngx.req.get_uri_args()
                  local a = args["a"]
                  local k = args["k"]
                  local v = args["v"]

                  local dict = ngx.shared.my_shared_data
                  if a=="list" then
                    -- 获取字典中所有键的列表
                    local keys = dict:get_keys(0)  -- 这里的 0 表示获取所有键
                    -- 遍历所有键并获取对应的值
                    for _, key in ipairs(keys) do
                        local value = dict:get(key)
                        ngx.say(key.."  "..value)
                    end
                  elseif a=="add" then
                    dict:set(k, v)
                    ngx.say(k.." "..v)
                  elseif a=="remove" then
                    dict:delete(k)
                    ngx.say(k.."  "..v)
                  elseif a=="get" then
                    local value = dict:get(k)
                    if value ~= nil then
                        -- 键存在，执行相应逻辑
                        ngx.say(k.."  "..dict:get(k))
                    else
                        -- 键不存在，执行相应逻辑
                        ngx.say("Key does not exist.")
                    end

                  end

          elseif request_method == "POST" then
                  ngx.req.read_body()
                  local arg = ngx.req.get_post_args()["b"] or 0
                  ngx.var.parm = arg
                  ngx.say(arg)
          end
  }
  # return 200 "Captured value: $parm";


}

location @404_handler {
    set_by_lua_block $redirected_url {
        -- 获取 Referer
        local referer = ngx.var.http_referer
        local requestUrl = ngx.var.request_uri
        --ngx.say(referer)
        --ngx.say(requestUrl)
        --return ngx.exit(0)

        --if string.sub(requestUrl,1, 8) == "/console" then
        --    return ngx.exit(ngx.HTTP_OK)  -- 直接返回状态码 200
        --end

        if referer==nil then
          return ngx.exit(ngx.HTTP_NOT_FOUND)
        else
            local instanceUuid = string.match(referer, "/pod/([^/]+)/$")
            --if instanceUuid==nil then
            --  instanceUuid = string.match(referer, "/podend/([^/]+)/$")
            --end
            if instanceUuid ~= nil then
                local dict = ngx.shared.my_shared_data
                local v = dict:get(instanceUuid)
                -- ngx.say(v)
                local firstChar = string.sub(requestUrl, 1, 1)
                if firstChar == '/' then
                    local l = string.len(requestUrl)
                    requestUrl = string.sub(requestUrl, 2, l)
                end
                -- ngx.say(requestUrl)
                local redirectedUrl = v..requestUrl
                -- ngx.say(redirectedUrl)
                return redirectedUrl
                -- local dict = ngx.shared.my_shared_data
                -- dict:set("RedirectedUrl", redirectedUrl)
            else
                return ngx.exit(ngx.HTTP_NOT_FOUND)
            end
        end
    }

    #content_by_lua_block {
    # ngx.say(ngx.var.redirected_url)
    #}
    # 反向代理到重写后的 URI
    proxy_pass $redirected_url;
    proxy_set_header Remote_addr $remote_addr;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}

location ~ ^/pod/([^/]+)$ {
  content_by_lua_block{
        local host = ngx.var.host
        local requestUri = ngx.var.request_uri
        local completeUrl = "https://" .. host .. requestUri .. "/"
        ngx.redirect(completeUrl, ngx.HTTP_MOVED_PERMANENTLY)
  }
}

location ~ ^/pod/([^/]+)/(.*)$ {
    # 将第一个捕获组的内容赋值给变量 $dynamic_part
    # try_files $uri $uri/ @404_handler;  # 使用 @404_handler 处理 404 错误
    set $instance_uuid $1;
    set $dynamic_part $2;
    set_by_lua_block $redirected_url {
        local redirectedUrl=""
        local instanceUuid = ngx.var[1];
        local dynamicPart = ngx.var[2];
        local dict = ngx.shared.my_shared_data
        local v = dict:get(instanceUuid)
        if v ~= nil then
            redirectedUrl = v
        else
            local custom_headers = {
                    ["Authorization"] = "ac4f334sddbfe701dda3783124ef6822a"
                }
            local res = ngx.location.capture("/api/sys/weburl?instance_uuid="..instanceUuid, { method = ngx.HTTP_GET,headers = custom_headers })
            if res.status == 200 then
                local cjson = require("cjson")
                local json_data = cjson.decode(res.body)
                if json_data.code==0 then
                  local webUrl = json_data.result.web_url
                  local dict = ngx.shared.my_shared_data
                  dict:set(instanceUuid, webUrl)
                  redirectedUrl = webUrl
                end
            end
        end
        if redirectedUrl=="" then
            return ngx.exit(ngx.HTTP_NOT_FOUND)
        else
            local firstChar = string.sub(dynamicPart, 1, 1)
            if firstChar == '/' then
                local lastChar = string.sub(redirectedUrl,-1)
                if lastChar=='/' then
                  redirectedUrl = string.sub(redirectedUrl, 1, -2)
                end
            end
        end
        return redirectedUrl
    }

    #return 200 "Captured value: ====$1===$2==== Redirected URL: $redirected_url$dynamic_part";

    # 反向代理到重写后的 URI
    proxy_pass $redirected_url$dynamic_part;
    proxy_set_header Remote_addr $remote_addr;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}



   location /fastchat/ {
        # 设置反向代理
        proxy_pass http://**************:8000/;
        proxy_set_header Remote_addr $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    #解决vue包路径不存在，刷新页面404的问题
    error_page 404 =200 /;
    location /docs/ {
        # 在 docs 目录下启用 error_page 404
        error_page 404 =200 /;
        try_files $uri $uri/ /docs/index.html;
    }
    location /console/ {

      error_page 404 =200 /;
    }

    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE)
    {
        return 404;
    }

    #一键申请SSL证书验证目录相关设置
    location ~ \.well-known{
        allow all;
    }

    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    access_log  /www/wwwlogs/suanyun.cyuai.com.log;
    error_log  /www/wwwlogs/suanyun.cyuai.com.error.log;
}
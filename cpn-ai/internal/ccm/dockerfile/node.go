package dockerfile

import (
	"archive/tar"
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"runtime/debug"
	"slices"
	"strings"
	"sync"
	"time"

	"cpn-ai/internal/ccm"
	"cpn-ai/internal/ccm/json"
	"cpn-ai/internal/ccm/noderpc"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/client"
)

type Build struct {
	Virtual *noderpc.Virtual
	// NodeID        int    `json:"node_id,omitempty"`
	// VirtualID     int    `json:"virtual_id,omitempty"`

	ActionID int    `json:"action_id,omitempty"`
	Key      string `json:"key,omitempty"`
	Tag      string `json:"tag,omitempty"`

	StartScript string `json:"start_script,omitempty"`
	Dockerfile  string `json:"dockerfile,omitempty"`

	Push    bool                 `json:"push,omitempty"`
	NoCache bool                 `json:"no_cache,omitempty"`
	Version types.BuilderVersion `json:"version,omitempty"`

	cf        context.CancelFunc
	StartTime time.Time
	EndTime   time.Time
	Image     Image
	Error     error
}

const (
	N   = 3
	Max = 100
)

var (
	mu     sync.Mutex
	solo   = make(chan struct{}, N+1)
	builds = make([]*Build, 0, 10)
)

func (b *Build) Validate() bool {
	if b == nil || b.cf != nil ||
		b.Key == "" || b.Dockerfile == "" || b.StartScript == "" {
		return false
	}

	//TODO

	return true
}

func (b *Build) enqueue() bool {
	if !b.Validate() {
		return false
	}

	mu.Lock()
	defer mu.Unlock()

	for _, o := range builds {
		if o.Key == b.Key {
			return false
		}
	}

	if len(builds) >= Max {
		return false
	}

	//TODO：priority
	builds = append(builds, b)
	solo <- struct{}{}

	return true
}

func dequeue(key string) bool {
	if key == "" {
		return false
	}

	mu.Lock()
	defer mu.Unlock()

	for i, b := range builds {
		if b.Key == key {
			builds = slices.Delete(builds, i, i+1)
			if b.cf != nil {
				b.cf()
			}
			solo <- struct{}{}
			return true
		}
	}

	return false
}

func (b *Build) body() []byte {
	var buf bytes.Buffer
	w := tar.NewWriter(&buf)

	ccm.MustNoError(w.WriteHeader(&tar.Header{
		Typeflag: tar.TypeReg,
		Name:     "Dockerfile",
		Size:     int64(len(b.Dockerfile)),
		Mode:     0644,
	}))
	_, err := w.Write([]byte(b.Dockerfile))
	ccm.MustNoError(err)

	ccm.MustNoError(w.WriteHeader(&tar.Header{
		Typeflag: tar.TypeReg,
		Name:     "start.sh",
		Size:     int64(len(b.StartScript)),
		Mode:     0644,
	}))
	_, err = w.Write([]byte(b.StartScript))
	ccm.MustNoError(err)

	ccm.MustNoError(w.Flush())
	return buf.Bytes()
}

func (b *Build) dockerBuild(ctx context.Context) {
	defer func() {
		if e := recover(); e != nil {
			if err, ok := e.(error); ok && err != nil {
				b.Error = err
			} else {
				b.Error = fmt.Errorf("%v", e)
			}
			ccm.Errorf("docker build panicked: %s\n%s", b.Key, debug.Stack())
		}
		if err := b.End(); err != nil {
			ccm.Errorf("docker build report end failed: %s, error: %s", b.Key, err)
		}
	}()
	defer dequeue(b.Key)

	dc, virtualRow := noderpc.NewBuildClient(ctx)
	if dc == nil || virtualRow == nil {
		b.Error = fmt.Errorf("no virtual")
		return
	}
	b.Virtual = virtualRow
	defer dc.Close()

	ccm.MustNoError(b.Start())

	options := types.ImageBuildOptions{
		SuppressOutput: true,
		Tags:           []string{b.Tag},
		NoCache:        b.NoCache,
		Version:        b.Version,
	}

	// if b.Push {
	// 	options.Outputs = append(options.Outputs, types.ImageBuildOutput{
	// 		Type: "image",
	// 		Attrs: map[string]string{
	// 			"name": b.Tag,
	// 			"push": "true",
	// 		},
	// 	})
	// }

	res, err := dc.ImageBuild(ctx, bytes.NewReader(b.body()), options)
	if err != nil {
		b.Error = err
		return
	}
	defer res.Body.Close()

	d := json.NewDecoder(res.Body)
	for {
		var result ccm.M
		err = d.Decode(&result)
		if err == io.EOF {
			break
		}
		ccm.MustNoError(err)
		if e, ok := result["error"]; ok {
			b.Error = fmt.Errorf("%v", e)
		} else {
			id, ok := result["id"].(string)
			if ok { //"2"
				if id == "moby.image.id" {
					id, ok = result["aux"].(ccm.M)["ID"].(string)
					id = strings.TrimSpace(id)
					ok = ok && id != ""
					if ok {
						b.Image.ID = id
						ccm.Infof("docker built %s, image: %v", b.Key, id)
					}
				} else {
					ok = id == "moby.buildkit.trace"
				}
			} else { //"1"
				id, ok = result["stream"].(string)
				id = strings.TrimSpace(id)
				ok = ok && strings.HasPrefix(id, "sha256:")
				if ok {
					b.Image.ID = id
					ccm.Infof("docker built %s, image: %v", b.Key, id)
				}
			}
			if !ok {
				ccm.Infof("docker building %s, result: %v", b.Key, result)
			}
		}
	}

	if b.Error != nil {
		return
	} else if b.Image.ID == "" {
		b.Error = errors.New("no image id")
		return
	}
	for i := 0; i < 3; i++ {
		image, _, err := dc.ImageInspectWithRaw(ctx, b.Image.ID)
		if err != nil {
			time.Sleep(time.Second)
			continue
		}
		b.Image.Size = image.Size
		b.Image.Layers = len(image.RootFS.Layers)
		b.Image.Created = image.Created

		history, err := dc.ImageHistory(ctx, image.ID)
		if err != nil {
			time.Sleep(time.Second)
			continue
		}
		b.Image.History = len(history)

		break
	}

	if b.Push {
		for i := 1; i <= 3; i++ {
			if err = b.dockerPush(ctx, dc); err == nil {
				break
			} else {
				ccm.Infof("docker push %d failed: %s, image: %s, error: %v", i, b.Key, b.Image.ID, err)
				time.Sleep(time.Duration(i*3) * time.Second)
			}
		}
		b.Error = err
	}

	b.EndTime = time.Now()
}

func (b *Build) dockerPush(ctx context.Context, dc *client.Client) error {
	rc, err := dc.ImagePush(ctx, b.Tag, image.PushOptions{
		//TODO
		//echo -n '{"username": "admin", "password": "Zeyun1234%^&*"}' | base64
		RegistryAuth: "eyJ1c2VybmFtZSI6ICJhZG1pbiIsICJwYXNzd29yZCI6ICJaZXl1bjEyMzQlXiYqIn0=",
	})
	if err != nil {
		return err
	}
	defer rc.Close()

	d := json.NewDecoder(rc)
	for {
		var result ccm.M
		err = d.Decode(&result)
		if err == io.EOF {
			return nil
		} else if err != nil {
			return err
		}
		if e, ok := result["error"]; ok {
			return fmt.Errorf("%v", e)
		} else {
			s, ok := result["status"].(string)
			if ok {
				if s == "Pushed" {
					ccm.Infof("docker pushed: %s, result: %v", b.Tag, result)
				} else {
					ok = ccm.InSlice(s, "Preparing", "Layer already exists", "Pushing")
				}
			}
			if !ok {
				ccm.Infof("docker pushing: %s, result: %v", b.Tag, result)
			}
		}
	}
}

func init() {
	go dockerBuild()
}

func dockerBuild() {
	const (
		gap     = time.Second
		timeout = 2 * time.Hour
	)

	defer func() {
		if e := recover(); e != nil {
			ccm.Error("docker build exited: %v\n%s", e, debug.Stack())
		}

		time.Sleep(gap)
		go dockerBuild()
	}()

	t := time.NewTicker(timeout)
	defer t.Stop()

	for {
		mu.Lock()
		for i, n := 0, len(builds); i < n && i < N; i++ {
			b := builds[i]
			if b.cf == nil {
				ctx := context.Background()
				ctx, b.cf = context.WithCancel(ctx)
				b.StartTime = time.Now()
				go b.dockerBuild(ctx)
			} else if d := time.Since(b.StartTime); d <= 0 || d >= timeout {
				b.cf()
				builds = slices.Delete(builds, i, i+1)
				i--
				n--
			}
		}
		mu.Unlock()

		select {
		case <-solo:
			for len(solo) > 0 {
				<-solo
			}
		case <-t.C:
		}

		t.Reset(timeout)
		time.Sleep(gap)
	}
}

package noderpc

import (
	"bytes"
	"fmt"
	"net/http"
	"strings"

	"cpn-ai/internal/ccm"
	"cpn-ai/internal/ccm/json"
)

var Client = &http.Client{}

func Do[T any](req *http.Request) (*T, error) {
	res, err := Client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("%s %s status code: %d", req.Method, req.URL, res.StatusCode)
	}

	var result struct {
		Code    int    `json:"code,omitempty"`
		Message string `json:"message,omitempty"`
		Data    *T     `json:"data,omitempty"`
	}

	err = json.NewDecoder(res.Body).Decode(&result)
	if err != nil {
		return nil, err
	}

	if result.Code != 0 {
		return nil, fmt.Errorf("%s %s code: %d, message: %s", req.Method, req.URL, result.Code, result.Message)
	}

	return result.Data, nil
}

func Post[T any](n *Node, u string, body any) (_ *T, err error) {
	defer ccm.Recover2Error(&err)

	b, err := json.Marshal(body, nil)
	ccm.MustNoError(err)

	ccm.Must(strings.HasPrefix(u, "/"))
	req, err := http.NewRequest("POST", n.ApiBaseUrl+u, bytes.NewReader(b))
	ccm.MustNoError(err)

	req.Header.Set("Content-Type", ccm.ContentTypeJSON)
	req.Header.Set("Authorization", n.CreateToken())

	return Do[T](req)
}

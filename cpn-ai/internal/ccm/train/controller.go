package train

import (
	"cpn-ai/internal/ccm"
	"cpn-ai/internal/ccm/action"
	"cpn-ai/internal/ccm/pager"
	"fmt"
	"path"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func Get(cx *gin.Context, userId int, userPath string) {
	row := GetByUuid(cx.Param("uuid"))
	if row == nil || row.UserId != userId {
		ccm.WriteData(cx, nil)
		return
	}

	var actionIds []int
	if row.CaptionActionId > 0 {
		actionIds = append(actionIds, row.CaptionActionId)
	}
	if row.TrainActionId > 0 {
		actionIds = append(actionIds, row.TrainActionId)
	}
	if row.GenerateActionId > 0 {
		actionIds = append(actionIds, row.GenerateActionId)
	}
	actionRows := action.ListByIDs(actionIds)

	ccm.WriteData(cx, row.StatMap(userPath, actionRows))
}

func (row *Train) StatMap(userPath string, actionRows map[int]*action.Action) ccm.M {
	m := BasicKeys.Map(row)

	if row.Name == "" {
		m["name"] = row.CreatedAt.Format("20060102-") + strconv.Itoa(row.ID+850729)
	}
	if row.CaptionActionId > 0 {
		m["caption_action"] = actionRows[row.CaptionActionId]
		m["caption_queue"] = indexCaption(row.CaptionActionId)
	}
	if row.TrainActionId > 0 {
		m["train_action"] = actionRows[row.TrainActionId]
		m["train_queue"] = indexTrain(row.TrainActionId)
	}
	if row.GenerateActionId > 0 {
		actionRow := actionRows[row.GenerateActionId]
		m["generate_action"] = actionRow
		if actionRow != nil && actionRow.Status == action.Running {
			m["generate_url"] = "https://" + row.Uuid + ".hz02.chenyu.cn/"
		}
	}

	count, size := InputStat(path.Join(userPath, "chenyu_train", row.Uuid, "input"))
	m["file_count"] = count
	m["file_size"] = size

	return m
}

func Idle(cx *gin.Context, userId int, userPath string) {
	ccm.WriteData(cx, GetIdle())
}

func List(cx *gin.Context, userId int, userPath string) {
	p := pager.Pull(cx)
	rows := pager.List[*Train](p, ListByUserId(userId))

	var actionIds []int
	for _, row := range rows {
		if row.CaptionActionId > 0 {
			actionIds = append(actionIds, row.CaptionActionId)
		}
		if row.TrainActionId > 0 {
			actionIds = append(actionIds, row.TrainActionId)
		}
		if row.GenerateActionId > 0 {
			actionIds = append(actionIds, row.GenerateActionId)
		}
	}
	actionRows := action.ListByIDs(actionIds)

	p.WriteData(cx, ccm.MapSlice(rows, func(row *Train) (bool, ccm.M) {
		return true, row.StatMap(userPath, actionRows)
	}))
}

func Save(cx *gin.Context, userId int, userPath string) {
	row := new(Train)

	if err := ccm.DecodeJSON(cx, row); err != nil {
		ccm.WriteError(cx, err)
		return
	}

	var old *Train
	if row.Uuid != "" {
		old = GetByUuid(row.Uuid)
		if old == nil || old.UserId != userId {
			ccm.WriteError(cx, fmt.Sprintf("uuid %s not exist", row.Uuid))
			return
		}
	}

	row = &Train{
		Name:          row.Name,
		CaptionConfig: row.CaptionConfig,
		TrainType:     row.TrainType,
		TrainModel:    row.TrainModel,
		TrainRepeat:   row.TrainRepeat,
		TrainEpoch:    row.TrainEpoch,
		TrainSample:   row.TrainSample,
		TrainConfig:   row.TrainConfig,
	}

	if row.TrainConfig == nil {
		row.TrainConfig = make(ccm.M)
	}
	if row.TrainType == "" {
		row.TrainType = "flux"
	}
	if row.TrainRepeat == 0 {
		row.TrainRepeat = 1
	}
	if row.TrainEpoch == 0 {
		row.TrainEpoch = 1
	}

	if len(row.Name) > 100 {
		ccm.WriteError(cx, "name too long")
		return
	}
	if len(row.CaptionConfig.Prompt) > 1024 {
		ccm.WriteError(cx, "caption prompt too long")
		return
	}
	if !(1 <= row.TrainRepeat && row.TrainRepeat <= 50) {
		ccm.WriteError(cx, "train repeat out of range")
		return
	}
	if !(1 <= row.TrainEpoch && row.TrainEpoch <= 50) {
		ccm.WriteError(cx, "train epoch out of range")
		return
	}
	if len(row.TrainSample) > 8192 {
		ccm.WriteError(cx, "train sample too long")
		return
	}

	if _, err := row.TrainDefaultConfig(); err != nil {
		ccm.WriteError(cx, err)
		return
	}

	if old != nil {
		row.ID = old.ID
		row.UserId = old.UserId
		row.Uuid = old.Uuid

		ccm.MustNoError(ccm.DB.Select("name", "caption_config", "train_type", "train_model", "train_repeat", "train_epoch", "train_sample", "train_config").Updates(row).Error)
	} else {
		row.UserId = userId
		row.Uuid = ccm.NewUuid()
		ccm.MustNoError(ccm.DB.Save(row).Error)
	}

	ccm.WriteData(cx, row.Uuid)
}

func Run(cx *gin.Context, userId int, userPath string) {
	row := GetByUuid(cx.Param("uuid"))
	if row == nil || row.UserId != userId {
		ccm.WriteError(cx, fmt.Sprintf("uuid %s not exist", row.Uuid))
		return
	}

	switch op, _ := ccm.First(cx, "action"); op {
	case "cancel caption":
		actionRow := action.GetByID(row.CaptionActionId)
		if actionRow == nil || !actionRow.Status.IsUnfinished() {
			ccm.WriteError(cx, "can't cancel caption")
			return
		}
		if actionRow.Meta == nil {
			actionRow.Meta = make(ccm.M)
		}
		actionRow.Meta["canceled"] = true
		ccm.MustNoError(ccm.DB.Select("meta").Updates(actionRow).Error)

		go row.doCaption(actionRow)

	case "idle caption", "caption":
		actionRow := action.GetByID(row.CaptionActionId)
		if actionRow != nil && actionRow.Status.IsUnfinished() {
			ccm.WriteError(cx, "caption is "+actionRow.Status.String())
			return
		}

		if op == "caption" && GetIdle()[op] == 0 && GetIdle()["idle caption"] == 0 {
			ccm.WriteError(cx, "no card to run, please idle caption")
			return
		}
		actionRow = &action.Action{
			Name:   op,
			Key:    row.Uuid,
			Status: action.Pending,
			Meta:   make(ccm.M),
		}
		ccm.MustNoError(ccm.DB.Save(actionRow).Error)

		queueMutex.Lock()
		captionActionIds = append(captionActionIds, actionRow.ID)
		queueMutex.Unlock()

		result := ccm.DB.Model(TrainModel).
			Where(ccm.Eq("id", row.ID)).Where(ccm.Eq("uuid", row.Uuid)).
			Where(ccm.Eq("caption_action_id", row.CaptionActionId)).
			Updates(ccm.M{
				"caption_count":     gorm.Expr("caption_count + 1"),
				"caption_action_id": actionRow.ID,
			})
		ccm.MustNoError(result.Error)
		if result.RowsAffected != 1 {
			ccm.WriteError(cx, op+" failed")
			return
		}

		go runCaption()

	case "cancel train":
		actionRow := action.GetByID(row.TrainActionId)
		if actionRow == nil || !actionRow.Status.IsUnfinished() {
			ccm.WriteError(cx, "can't cancel train")
			return
		}
		if actionRow.Meta == nil {
			actionRow.Meta = make(ccm.M)
		}
		actionRow.Meta["canceled"] = true
		ccm.MustNoError(ccm.DB.Select("meta").Updates(actionRow).Error)

		go row.doTrain(actionRow)

	case "idle train", "train":
		captionActionRow := action.GetByID(row.CaptionActionId)
		if captionActionRow == nil || captionActionRow.Status != action.Succeeded {
			ccm.WriteError(cx, "caption is not succeeded")
			return
		}

		actionRow := action.GetByID(row.TrainActionId)
		if actionRow != nil && actionRow.Status.IsUnfinished() {
			ccm.WriteError(cx, "train is "+actionRow.Status.String())
			return
		}

		if op == "train" && GetIdle()[op] == 0 && GetIdle()["idle train"] == 0 {
			ccm.WriteError(cx, "no card to run, please idle train")
			return
		}

		actionRow = &action.Action{
			Name:   op,
			Key:    row.Uuid,
			Status: action.Pending,
			Meta:   make(ccm.M),
		}
		ccm.MustNoError(ccm.DB.Save(actionRow).Error)

		queueMutex.Lock()
		trainActionIds = append(trainActionIds, actionRow.ID)
		queueMutex.Unlock()

		result := ccm.DB.Model(TrainModel).
			Where(ccm.Eq("id", row.ID)).Where(ccm.Eq("uuid", row.Uuid)).
			Where(ccm.Eq("train_action_id", row.TrainActionId)).
			Updates(ccm.M{
				"train_count":     gorm.Expr("train_count + 1"),
				"train_action_id": actionRow.ID,
			})
		ccm.MustNoError(result.Error)
		if result.RowsAffected != 1 {
			ccm.WriteError(cx, op+" failed")
			return
		}

		go runTrain()

	case "cancel generate":
		actionRow := action.GetByID(row.GenerateActionId)
		if actionRow == nil || !actionRow.Status.IsUnfinished() {
			ccm.WriteError(cx, "can't cancel generate")
			return
		}
		if actionRow.Meta == nil {
			actionRow.Meta = make(ccm.M)
		}
		actionRow.Meta["canceled"] = true
		ccm.MustNoError(ccm.DB.Select("meta").Updates(actionRow).Error)

		runGenerate()

	case "generate":
		actionRow := action.GetByID(row.GenerateActionId)
		if actionRow != nil && actionRow.Status.IsUnfinished() {
			ccm.WriteError(cx, "generate is "+actionRow.Status.String())
			return
		}

		trainActionRow := action.GetByID(row.TrainActionId)
		if trainActionRow == nil || trainActionRow.Status != action.Succeeded {
			ccm.WriteError(cx, "train is not succeeded")
			return
		}

		if GetIdle()[op] == 0 {
			ccm.WriteError(cx, "no card to generate")
			return
		}

		actionRow = &action.Action{
			Name:   op,
			Key:    row.Uuid,
			Status: action.Pending,
			Meta:   make(ccm.M),
		}
		ccm.MustNoError(ccm.DB.Save(actionRow).Error)

		result := ccm.DB.Model(TrainModel).
			Where(ccm.Eq("id", row.ID)).Where(ccm.Eq("uuid", row.Uuid)).
			Where(ccm.Eq("generate_action_id", row.GenerateActionId)).
			Updates(ccm.M{
				"generate_count":     gorm.Expr("generate_count + 1"),
				"generate_action_id": actionRow.ID,
			})
		ccm.MustNoError(result.Error)
		if result.RowsAffected != 1 {
			ccm.WriteError(cx, op+" failed")
			return
		}

		runGenerate()

	default:
		ccm.WriteError(cx, "invalid action: "+op)
		return
	}

	ccm.WriteData(cx, row.Uuid)
}

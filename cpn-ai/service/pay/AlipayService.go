package pay

import (
	"context"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"errors"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/alipay"
	"github.com/shopspring/decimal"
)

type alipayService_ struct {
	ctx             context.Context
	client          *alipay.Client
	appId           string //应用APPID
	privateKey      string //应用私钥
	alipayPublicKey string //支付宝平台获取的支付宝公钥
}

var AlipayService alipayService_

func (o *alipayService_) GetClientByCrt(returnUrl string) *alipay.Client {

	// 初始化支付宝客户端
	//    appid：应用ID
	//    privateKey：应用私钥，支持PKCS1和PKCS8
	//    isProd：是否是正式环境，沙箱环境请选择新版沙箱应用。
	appId := "2021004189667485"
	client, err := alipay.NewClient(appId, AppPrivateContent, true)
	if err != nil {
		logger.Error(err)
		return nil
	}
	// Debug开关，输出/关闭日志
	client.DebugSwitch = gopay.DebugOn

	// 配置公共参数
	client.SetCharset("utf-8").
		SetSignType(alipay.RSA2).
		SetNotifyUrl("https://www.chenyu.cn/api_online/alipay/notify")

	if returnUrl != "" {
		client.SetReturnUrl(returnUrl)
	}

	// 设置biz_content加密KEY，设置此参数默认开启加密（目前未测试成功）
	//client.SetAESKey("KvKUTqSVZX2fUgmxnFyMaQ==")

	// 自动同步验签（只支持证书模式）
	// 传入 支付宝公钥证书 alipayPublicCert.crt 内容
	client.AutoVerifySign(AlipayPublicContentRSA2)

	// 传入证书内容
	err = client.SetCertSnByContent(AppPublicContent, AlipayRootContent, AlipayPublicContentRSA2)
	// 传入证书文件路径
	//err := client.SetCertSnByPath("cert/appPublicCert.crt", "cert/alipayRootCert.crt", "cert/alipayPublicCert.crt")
	if err != nil {
		logger.Error(err)
		return nil
	}
	return client
}

func (o *alipayService_) getClient(returnUrl string) *alipay.Client {
	o.ctx = context.Background()
	o.appId = "2021003192690874"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              //晨羽AI效果图                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         // 商户号
	o.privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDRmaqDRaUNndgHGNmi35otPU5i7PxOkwKscSULDUFoEPzSFaMEYNwBaSsmzuzQiAuTCF78GYNmHqNdWVNY04VqCwvjBo75z+Ww4YSSZCIZG7cl1vTVGD8czPJwZOOMBmuMjLIr36jdEf/K17XBN/prfz8MZt0GwDAYNn/paULnVl1xhq7Jlf7jntlbkPdceN7urGpArG6oIJMlHm7JIvmyjN2MJvUDlI8CX4i3DgsCknTbDAymsFGqsm02+DUARpssty2hbnXjrfPsYqmYSajhUNOl+Ij68X6NpsgHjeeX4t2QM+0ZdDofOEwx6LowhhOTmsZWoz0KjQwkRcwHalmJAgMBAAECggEAZqmBnzKpmXXKrg856lDjT21Ly6CQJFxVXuk8X6Fqe/J2IuB+LFLU78v3WtbXn3xDTw02mPxWI9q14a8y57sTCdHHTLeVqD5wHqcXLCznlqzAlEQpP4bUwuKPjQWdfqRWYU04p8yxBTcfttbZK36jpat50jU/5WKVLAbluAnavzvmswE652ppxRXhicVii1v1l+WGtvMIiXUn79ICpRgjIqxWE/MvZV5XLoi58X3eKGq0afFXh+jsR3ZR6Tn05DiGmtg4bjY8l6uH009KoPay3IkWp6fzAB0UxylnRrAwnmo+nKsy/KS/rkzr8BZQk37mDbpvJbdzqMC9heeMMWQqQQKBgQD3tnovKXsJcuP7rSYdmRXibmkJt/MCB3i4DKzJaVt2i+lvENjUAXfsX/EFRtn2aEqCg+toZWTUN1rzfzcC8wQEjVMAnLpa03f4tr3T/AlFY+mZrk1/stUqJ6/gx6g9wF/uz0+fIgdhFXxbUOnh2a13WdJS9opEntAv9NB35L4UTQKBgQDYnManTeNAWKXi8pjRBEB+l3Y0klgh6cH4mHHBAcdiBBg2bjdLJOa4KQUUP35AYjGDm/RSAY3v140/mVrPfo8H59imUNw2xP0rvTotHGJtexMs8jlrBhOMy84FAwkcNzCGo7hxwoldNWafJGovMHloSXlc4mvmdZd8l0kIx4foLQKBgQD2QfI18IbgXddH3FqIEigvcpP5lWWAHu1kCzW7BYJn16+5OoZUPSF7W+W/2S/WRtfL6Fme7y3Nyzof19hYlj7oR5a8hRK+OHyMRR9yHp0Y3rrcjOt+h8pinhG65MHcoeK9BluhV7L+UN3P6iCY5k4M2L6+u1m53igZhMe6aSyOAQKBgQCPvwJpllt/oRR81ecplfvCxnqQCqnn33t0oJ2kqqyI1BTJUjzpZhSiE76mIDiFO2SGZSEupo9toYq/sOI9nkwGwAna3QA2bGVh+FI6C4MaCjnAcwI9jqu8nKccVlZeXhWa090byTBlJYHzjDPXGwEGfvPF+xAFwPWFndO+usLrBQKBgGfH9C1bWoJ7QPlBPeVOvhOcC8MloRjpR4z0t+3pYjHnibGhxuM63cw3YX8G0cn0pWvEYZVRWkEEQv84xAHX8Cp3fb3iD3JSJGZopI5vEF5mmygcPOGzfsWQ4AW82pjdyhL10+DoWIzQawCfuk3z3zCzsrfJVzltw/G82eM7r7EX" // 商户证书序列号
	o.alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuvjE3X8Ro03vhI2nk7MFhXtKBRQzAa3DX+6yK6wXIgbYLe8SG7Z6nriFh3eA3J+tOYmz7tahn9RJblfu1DQJ5p0KvIN7lXIWWKdzPocCaCYOaA5+1uLimJtjw7bJT+Jna82xnYBRJHDqwbik4PSKzomPmzJMguT41SxH/Xi2F86ypHh2lKCXW9TiWC6ZA7CgKxnGARZm2txq3Ad5HOpGQhMm3pQh4HjTFcD1xvFvmH2cafISuH23Pbs85sKNw/Fo81QUrD+a9+pBvp40LywlBEMlWhWUonehgW2m71N6JlWnNHHxooXBos2zlAON3+IEUEP8FLeNrwEdyrNNBbbnBwIDAQAB"

	//初始化支付宝客户端
	//    appId：应用ID
	//    privateKey：应用秘钥
	//    isProd：是否是正式环境
	client, err := alipay.NewClient(o.appId, o.privateKey, true)
	if err != nil {
		logger.Error(err)
		return nil
	}
	// 打开Debug开关，输出日志，默认关闭
	client.DebugSwitch = gopay.DebugOn

	// 设置支付宝请求 公共参数
	//    注意：具体设置哪些参数，根据不同的方法而不同，此处列举出所有设置参数

	callbackUrl := "https://design.cyuai.com/api/v1/alipay/callback"
	callbackUrl = "http://***********:5173/#/pages/duringPay/duringPay"
	callbackUrl = "https://design.cyuai.com/#/pages/duringPay/duringPay"
	callbackUrl = "https://suanyun.cyuai.com/duringPay"
	callbackUrl = "https://www.suanyun.cn/console/paygress"
	callbackUrl = "https://www.chenyu.cn/console/paygress"
	if config.Env == enums.EnvEnum.DEV {
		callbackUrl = "http://***********:5002/api/v1/alipay/callback"
	}
	if returnUrl != "" {
		callbackUrl = returnUrl
	}
	client.SetLocation(alipay.LocationShanghai). // 设置时区，不设置或出错均为默认服务器时间
							SetCharset(alipay.UTF8).   // 设置字符编码，不设置默认 utf-8
							SetSignType(alipay.RSA2).  // 设置签名类型，不设置默认 RSA2
							SetReturnUrl(callbackUrl). // 设置返回URL
							SetNotifyUrl("https://www.chenyu.cn/api/alipay/notify")
	//SetNotifyUrl("https://www.suanyun.cn/api/alipay/notify")
	//SetNotifyUrl("https://suanyun.cyuai.com/api/alipay/notify") // 设置异步通知URL //SetNotifyUrl("https://design.cyuai.com/api/v1/alipay/notify")

	//SetAppAuthToken()                           // 设置第三方应用授权
	if config.Env == enums.EnvEnum.ONLINE {
		client.SetNotifyUrl("https://www.chenyu.cn/api_online/alipay/notify")
	}

	client.AutoVerifySign([]byte(o.alipayPublicKey))

	return client

}

func (o *alipayService_) UserCertifyOpenInit(returnUrl string, orderNo string, certType string, certName string, certNo string) (string, error) {
	client := o.getClient(returnUrl)
	//请求参数

	bm := make(gopay.BodyMap)
	bm.Set("outer_order_no", orderNo)
	// 认证场景码：FACE：多因子人脸认证，CERT_PHOTO：多因子证照认证，CERT_PHOTO_FACE ：多因子证照和人脸认证，SMART_FACE：多因子快捷认证
	bm.Set("biz_code", "FACE")
	// 需要验证的身份信息参数，格式为json
	identity := make(map[string]string)
	identity["identity_type"] = "CERT_INFO"
	//identity["cert_type"] = "IDENTITY_CARD"
	identity["cert_type"] = certType
	identity["cert_name"] = certName
	identity["cert_no"] = certNo
	bm.Set("identity_param", identity)
	// 商户个性化配置，格式为json
	merchant := make(map[string]string)
	merchant["return_url"] = returnUrl
	bm.Set("merchant_config", merchant)

	// 发起请求
	if aliRsp, err := client.UserCertifyOpenInit(o.ctx, bm); err != nil {
		//if bizErr, ok := IsBizError(err); ok {
		//	xlog.Errorf("%+v", bizErr)
		//	// do something
		//	return
		//}
		logger.Error(err, "  ")
		return "", err
	} else {
		if aliRsp.Response != nil && aliRsp.Response.CertifyId != "" { //{"code":"10000","msg":"Success","certify_id":"b30748408a8022fc255267e6d8f1d880"}
			return aliRsp.Response.CertifyId, nil
		} else {
			err := errors.New("未获取到结果")
			logger.Error(err, "  identity:", utils.GetJsonFromStruct(identity), " aliRsp:", utils.GetJsonFromStruct(aliRsp))
			return "", err
		}
	}
}

func (o *alipayService_) UserCertifyOpenCertify(returnUrl string, certifyId string) (string, error) {
	client := o.getClient(returnUrl)
	bm := make(gopay.BodyMap)
	// 本次申请操作的唯一标识，由开放认证初始化接口调用后生成，后续的操作都需要用到
	//bm.Set("certify_id", "53827f9d085b3ce43938c6e5915b4729")
	bm.Set("certify_id", certifyId)

	// 发起请求
	certifyUrl, err := client.UserCertifyOpenCertify(o.ctx, bm)
	return certifyUrl, err
}

func (o *alipayService_) UserCertifyOpenQuery(certifyId string) (string, error) {
	client := o.getClient("")
	bm := make(gopay.BodyMap)
	// 本次申请操作的唯一标识，由开放认证初始化接口调用后生成，后续的操作都需要用到
	//bm.Set("certify_id", "53827f9d085b3ce43938c6e5915b4729")
	bm.Set("certify_id", certifyId)

	// 发起请求
	aliRsp, err := client.UserCertifyOpenQuery(o.ctx, bm)
	if err != nil {
		return utils.GetJsonFromStruct(aliRsp), err
	}
	if aliRsp != nil && aliRsp.Response != nil {
		if aliRsp.Response.Passed == "T" {
			return utils.GetJsonFromStruct(aliRsp.Response), nil
		} else {
			return utils.GetJsonFromStruct(aliRsp.Response), errors.New("验证未通过")
		}
	}
	return utils.GetJsonFromStruct(aliRsp), errors.New("验证失败")
}

func (o *alipayService_) TradePagePay(returnUrl string, outTradeNo string, amount decimal.Decimal, desc string) (string, error) {
	client := o.getClient(returnUrl)
	//请求参数
	bm := make(gopay.BodyMap)
	bm.Set("subject", desc) //电脑网站测试支付
	bm.Set("out_trade_no", outTradeNo)
	bm.Set("total_amount", amount.String())
	bm.Set("product_code", "FAST_INSTANT_TRADE_PAY")
	////电脑网站支付请求
	payUrl, err := client.TradePagePay(o.ctx, bm)
	if err != nil {
		logger.Error("err:", err)
	}
	return payUrl, err
}

func (o *alipayService_) TradeWapPay(returnUrl, outTradeNo string, amount decimal.Decimal, desc string) (string, error) {
	client := o.getClient(returnUrl)
	//请求参数
	bm := make(gopay.BodyMap)
	bm.Set("subject", desc) //手机网站测试支付
	bm.Set("out_trade_no", outTradeNo)
	//bm.Set("quit_url", "https://www.fmm.ink")
	bm.Set("total_amount", amount.String())
	bm.Set("product_code", "QUICK_WAP_WAY")
	//手机网站支付请求
	payUrl, err := client.TradeWapPay(o.ctx, bm)
	if err != nil {
		logger.Error("err:", err)
	}
	return payUrl, err
	//https://openapi.alipay.com/gateway.do?app_id=2021001191690325&biz_content=%7B%22out_trade_no%22%3A%22r2022121615331500000029%22%2C%22product_code%22%3A%22QUICK_WAP_WAY%22%2C%22subject%22%3A%22%E6%94%AF%E4%BB%98%E6%B5%8B%E8%AF%95%22%2C%22total_amount%22%3A%220.01%22%7D&charset=utf-8&format=JSON&method=alipay.trade.wap.pay&sign=QjuSgEBe7mVgJQejpDZgYuTzGv5DPJvJIiLkoF2Ql5aPI%2BYi99Ip1FXs5A7T18IVA%2FXk9s9FjyAM5jdIlrMZLplOx6KGkKH81lnHA88BX7dTHbyNjGQnIZTpw2O8SGRc4DP%2Fls%2BPJ4V52zeEvoao25nVqrwAUrSFpE%2FVyujED57AVfgOG1e8SWa480Dxf%2BROKNmApTAL1WRTRe637kf1kTUCTK2r%2FJI8sinR%2BCFY6n8X%2Bf90RzEN%2BMgnIi5C1pj3JSQkeve5oabrdwBEg%2BDAKMxAk6f%2Fn1uGtJwN9LOfIwnM%2BHgE6FY2kvJCy4aiYrQg%2FjAVlso2Q%2FdzGqjs2Azpbg%3D%3D&sign_type=RSA2&timestamp=2022-12-16+15%3A33%3A47&version=1.0
}

func (o *alipayService_) TradeAppPay(returnUrl, outTradeNo string, amount decimal.Decimal, desc string) (string, error) {
	client := o.getClient(returnUrl)
	//请求参数
	bm := make(gopay.BodyMap)
	bm.Set("subject", desc) //手机网站测试支付
	bm.Set("out_trade_no", outTradeNo)
	//bm.Set("quit_url", "https://www.fmm.ink")
	bm.Set("total_amount", amount.String())
	//手机网站支付请求
	payParam, err := client.TradeAppPay(o.ctx, bm)
	if err != nil {
		logger.Error("err:", err)
	}
	return payParam, err
}

func (o *alipayService_) TradePrecreate(returnUrl, outTradeNo string, amount decimal.Decimal, desc string) (*alipay.TradePrecreateResponse, error) {
	client := o.getClient(returnUrl)
	// 请求参数
	bm := make(gopay.BodyMap)
	bm.Set("subject", desc).
		Set("out_trade_no", outTradeNo).
		Set("total_amount", amount.String())

	// 创建订单
	aliRsp, err := client.TradePrecreate(o.ctx, bm)
	if err != nil {
		logger.Error("err:", err)
	}
	return aliRsp, err
}

func (o *alipayService_) VerifySign(notifyBean interface{}) (bool, error) {
	ok, err := alipay.VerifySign(o.alipayPublicKey, notifyBean)
	if err != nil {
		logger.Error("VerifySign(%+v),error:%+v", notifyBean, err)
	}
	return ok, err
}

func (o *alipayService_) TradeQueryByOutTradeNo(outTradeNo string) (*alipay.TradeQueryResponse, error) {
	client := o.getClient("")
	bm := make(gopay.BodyMap)
	bm.Set("out_trade_no", outTradeNo)
	aliRsp, err := client.TradeQuery(o.ctx, bm)
	if err != nil {
		logger.Error(err)
	}
	//// 查询订单
	//aliRsp, err := o.client.TradeQuery(o.ctx, bm)
	//if err != nil {
	//	logger.Error(err)
	//}
	//return aliRsp,err
	//logger.Info("aliRsp:%+v", aliRsp.Response)
	//
	// 同步返回验签
	//ok, err := alipay.VerifySyncSignWithCert(o.alipayPublicKey, aliRsp.SignData, aliRsp.Sign)
	//if !ok || err != nil {
	//	logger.Error(err)
	//}
	return aliRsp, err
	//logger.Debug("同步返回验签：", ok)
}

func (o *alipayService_) FundTransUniTransfer(client *alipay.Client, outTradeNo string, amount decimal.Decimal, aliPayLogonId string, trueName string, desc string) (*alipay.FundTransUniTransferResponse, error) {
	//请求参数
	bm := make(gopay.BodyMap)
	bm.Set("out_biz_no", outTradeNo).
		Set("trans_amount", amount).
		Set("biz_scene", "DIRECT_TRANSFER").
		Set("product_code", "TRANS_ACCOUNT_NO_PWD").
		SetBodyMap("payee_info", func(bm gopay.BodyMap) {
			bm.Set("identity", aliPayLogonId) //bm.Set("identity", "<EMAIL>")
			bm.Set("identity_type", "ALIPAY_LOGON_ID")
			bm.Set("name", trueName)
		})
	ctx := context.Background()
	aliRsp, err := client.FundTransUniTransfer(ctx, bm)
	return aliRsp, err
}

func (o *alipayService_) FundTransOrderQuery(outTradeNo string) (*alipay.FundTransOrderQueryResponse, error) {
	client := o.getClient("")
	bm := make(gopay.BodyMap)
	bm.Set("out_biz_no", outTradeNo)
	ctx := context.Background()
	aliRsp, err := client.FundTransOrderQuery(ctx, bm)
	return aliRsp, err
}

//

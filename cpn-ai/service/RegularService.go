package service

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"runtime"
	"time"
)

type regular_ struct {
}

type RegularItem struct {
	Task        string `json:"task"`
	RegularTime jsontime.JsonTime
	RegularUnix int64
}

var RegularService regular_

func (d *regular_) Add() error {
	time.Now().Unix()
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	return nil
}

func (d *regular_) Remove() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	return nil
}

func (d *regular_) Get(startupMark string) error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	return nil
}

func (d *regular_) List() error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
		}
	}()
	return nil
}

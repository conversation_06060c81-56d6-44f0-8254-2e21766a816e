package service

import (
	"bufio"
	"context"
	"cpn-ai/common/logger"
	"cpn-ai/model"
	"errors"
	"strconv"
	"time"

	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/client"
)

func newDockerLogsClient(host string) *client.Client {
	host = "tcp://" + host + ":2375"
	c, err := client.NewClientWithOpts(client.WithHost(host), client.WithVersion("1.41"))
	if err != nil {
		logger.Errorf("create docker client %s failed: %s", host, err)
		return nil
	}
	return c
}

func DockerLogs(line int, instance *model.Instance, virtual *model.Virtual) (a []string, err error) {
	c := newDockerLogsClient(virtual.Host)
	if c == nil {
		return nil, errors.New("创建容器日志客户端失败")
	}

	tail := "100"
	if line < 0 {
		tail = "all"
	} else if line > 0 {
		tail = strconv.Itoa(line)
	}

	ctx, cf := context.WithTimeout(context.Background(), time.Minute)
	defer cf()

	rc, err := c.ContainerLogs(ctx, instance.DockerId, container.LogsOptions{
		ShowStdout: true,
		ShowStderr: true,
		Since:      "",
		Until:      "",
		Timestamps: false,
		Follow:     false,
		Tail:       tail,
		Details:    false,
	})
	if err != nil {
		logger.Errorf("docker ContainerLogs %s failed: %s", instance.DockerId, err)
		return nil, errors.New("获取容器日志失败")
	}

	s := bufio.NewScanner(rc)
	for s.Scan() {
		a = append(a, s.Text())
	}

	if err := s.Err(); err != nil {
		logger.Errorf("docker ContainerLogs %s failed: %s", "11259b2ff2d0", err)
		return nil, errors.New("获取容器日志失败")
	}

	return a, nil
}

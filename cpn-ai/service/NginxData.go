package service

import (
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/model"
	"errors"
	"fmt"
	"io/ioutil"
	"runtime"
	"unsafe"
)

func NginxAddInstance(instanceUuid string) (string, error) {
	msg := ""
	var instance model.Instance
	if err := instance.GetByUuid(instanceUuid); err != nil {
		msg = "数据不存在"
		logger.Error(msg, err)
		return "", err
	}
	if instance.Status != enums.InstanceStatusEnum.Running {
		msg = "不可用,当前状态为" + enums.InstanceStatusEnum.Name(instance.Status)
		err := errors.New(msg)
		logger.Error(err)
		return "", err
	}
	if instance.WebUrl == "" {
		msg = "WebUi地址为空，请尝试重新启动"
		err := errors.New(msg)
		return "", err
	}
	str, err := nginxHandle("add", instanceUuid, instance.WebUrl)
	if err != nil {
		logger.Error(err, "  str:", str)
		return "", err
	}
	logger.Info("add nginx add:", instance.Uuid, "：", str)
	return str, nil
}

func NginxGetInstance(key string) (string, error) {
	msg := ""
	if key == "" {
		msg = "参数不能为空"
		return msg, errors.New(msg)
	}
	//var instance model.Instance
	//if err := instance.GetByUuid(instanceUuid); err != nil {
	//	msg = "数据不存在"
	//	logger.Error(msg, err)
	//	return "", err
	//}
	//if instance.Status != enums.InstanceStatusEnum.Running {
	//	msg = "不可用,当前状态为" + enums.InstanceStatusEnum.Name(instance.Status)
	//	err := errors.New(msg)
	//	logger.Error(err)
	//	return "", err
	//}
	//if instance.WebUrl == "" {
	//	msg = "WebUi地址为空，请尝试重新启动"
	//	err := errors.New(msg)
	//	return "", err
	//}
	str, err := nginxHandle("get", key, "")
	if err != nil {
		logger.Error(err, "  str:", str)
		return "", err
	}
	logger.Info("get nginx get:", key, "：", str)
	return str, nil
}

func NginxRemoveInstance(instanceUuid string) (string, error) {
	msg := ""
	var instance model.Instance
	if err := instance.GetByUuid(instanceUuid); err != nil {
		msg = "数据不存在"
		logger.Error(msg, err)
		return "", err
	}
	str, err := nginxHandle("remove", instanceUuid, instance.WebUrl)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	str, err = nginxHandle("remove", "lastusetime_"+instanceUuid, instance.WebUrl)
	if err != nil {
		logger.Error(err, "  str:", str)
		return "", err
	}
	logger.Info("add nginx add:", instance, "：", str)
	return str, nil
}

func NginxListInstance(instanceUuid string) (string, error) {
	msg := ""
	var instance model.Instance
	if err := instance.GetByUuid(instanceUuid); err != nil {
		msg = "数据不存在"
		logger.Error(msg, err)
		return "", err
	}
	str, err := nginxHandle("list", instanceUuid, instance.WebUrl)
	if err != nil {
		logger.Error(err, "  str:", str)
		return "", err
	}
	str1, err := nginxHandle("list", "lastusetime"+instanceUuid, instance.WebUrl)
	if err != nil {
		logger.Error(err, "  str:", str1)
		return "", err
	}
	logger.Info("add nginx add:", instance, "：", str, "  ", str1)
	return str, nil
}

func NginxInstance(instanceUuid string, action string) (string, error) {
	msg := ""
	var instance model.Instance
	if err := instance.GetByUuid(instanceUuid); err != nil {
		msg = "数据不存在"
		logger.Error(msg, err)
		return "", err
	}
	if action == "add" {
		return NginxAddInstance(instanceUuid)
	} else if action == "get" {
		return NginxGetInstance(instanceUuid)
	} else if action == "remove" {
		return NginxRemoveInstance(instanceUuid)
	} else if action == "list" {
		return NginxListInstance(instanceUuid)
	} else {
		msg = "操作参数错误"
		err := errors.New(msg)
		logger.Error(err)
		return msg, err
	}
}

func nginxHandle(action string, k string, v string) (string, error) {

	if e := recover(); e != nil {
		logger.Error("nginxHandle奔溃:", e)
		stack := make([]byte, 4096)
		runtime.Stack(stack, false)
		logger.Error("StartupLoop panicked:", e, "\nStack Trace:\n", string(stack))
		return "奔溃", errors.New("奔溃")
	}

	msg := ""
	headers := make(map[string]string, 0)
	headers["Content-Type"] = "text/html; charset=utf-8"
	headers["User-Agent"] = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.2 Safari/605.1.15"
	headers["accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
	headers["Authorization"] = "ac4f334sddbfe701dda3783124ef6822a"
	domain := "https://www.suanyun.cn"
	domain = "https://www.chenyu.cn"
	if config.Env == enums.EnvEnum.PRODUCTION || config.Env == enums.EnvEnum.ONLINE {
		domain = "http://10.20.100.201:6004"
	}
	url := fmt.Sprintf(`%s/setpodmapping?a=%s&k=%s&v=%s`, domain, action, k, v)
	res, err := utils.Get(url, headers, nil)
	defer res.Body.Close()
	if err != nil {
		msg = "nginx映射设置失败"
		logger.Error(msg, err, url)
		return "", err
	}
	content, err := ioutil.ReadAll(res.Body)
	if err != nil {
		msg = "读取返回内容失败"
		logger.Error(msg, err, url)
		return "", err
	}
	str := (*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	return *str, nil
}

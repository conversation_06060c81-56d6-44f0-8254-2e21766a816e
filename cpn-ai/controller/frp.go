package controller

import (
	"cpn-ai/common/logger"
	"github.com/gin-gonic/gin"
	"net/http"
)

type _frpApi struct {
}

var FrpApi _frpApi

func (obj _frpApi) GetFrp(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	msg = ""
	code = 0
}

package controller

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"cpn-ai/model"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
)

type liveApi_ struct {
}

var LiveApi liveApi_

func (obj liveApi_) Live(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		//cookie := &http.Cookie{
		//	Name:     "token", // cookie的名称
		//	Value:    "my-secret-token", // cookie的值
		//	MaxAge:   3600, // cookie的最大生存期（秒）
		//	Path:     "/", // cookie适用的路径
		//	Domain:   "localhost", // cookie适用的域
		//	Secure:   false, // 是否仅通过HTTPS传输
		//	HttpOnly: true, // 是否仅可通过HTTP协议访问，防止客户端脚本访问此cookie
		//}
		//// 使用Gin的SetCookie方法设置cookie
		//key := c.Request.Header.Get("Authorization")
		//if key == "" {
		//	key = "string is empty"
		//}
		//c.SetCookie("Authorization", key, 3*86400, "/", ".chenyu.cn", false, false)

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()
	result["v"] = common.Version

	var pod model.Pod
	if err := pod.GetById(0); err != nil {
		if err != gorm.ErrRecordNotFound {
			msg += "Mysql链接失败"
		}
	}

	if _, err := common.RedisLLen(enums.RedisKeyEnum.CpnSchedIpLocation); err != nil {
		if msg != "" {
			msg += ","
		}
		msg += "Redis链接失败"
	}

	if msg == "" {
		msg = "success"
		code = 0
	}
}

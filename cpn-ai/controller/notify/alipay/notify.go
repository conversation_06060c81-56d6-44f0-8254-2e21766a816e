package alipay

import (
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/service"
	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/alipay"
	"net/http"
	"time"
)

func Notify(c *gin.Context) {

	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	logger.Info("已经接收到支付宝回调v1.3")

	bm, err := alipay.ParseNotifyToBodyMap(c.Request)
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Info("notifyReq:", bm)

	ok, err := service.AlipayService.VerifySign(bm)
	if err != nil {
		logger.Error("err:", err)
		return
	}
	logger.Info("支付宝验签是否通过:", ok)

	tradeStatus := bm["trade_status"].(string)
	if tradeStatus == "TRADE_SUCCESS" {
		outTradeNo := bm["out_trade_no"].(string)
		tradeNo := bm["trade_no"].(string)

		payTime, err := time.ParseInLocation("2006-01-02 15:04:05", bm["gmt_payment"].(string), time.Local) //这里按照当前时区转
		logger.Info("payTime ", bm["gmt_payment"].(string), "  ", payTime)
		if err != nil {
			logger.Error(err)
			return
		}
		if err := service.RechargeService.HandlePaySuccessRecharge(outTradeNo, tradeNo, payTime, utils.GetJsonFromStruct(bm)); err == nil {
			logger.Info("HandleRecharge success")
			c.Writer.WriteString("success")
			return
		}
	} else {
		logger.Error("其它的支付状态:", tradeStatus, " bm: ", bm)
	}
}

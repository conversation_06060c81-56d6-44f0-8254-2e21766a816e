package controller

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/config"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
)

type filesApi_ struct {
}

var FilesApi filesApi_

type filesReq struct {
	Action  string `json:"action"` //GetDir CreateDir
	Path    string `json:"path"`
	NewName string `json:"new_name"`
	NewPath string `json:"new_path"` //移动文件或者文件夹时
}

func (obj filesApi_) Download(c *gin.Context) {

}

func (obj filesApi_) Files(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	action := ""
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if action == "Download" {

		} else {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq filesReq

	action, _ = c.GetPostForm("action")
	if action == "Upload" {
		oReq.Action = action
		oReq.Path, _ = c.GetPostForm("path")
	} else {
		if err := c.ShouldBindJSON(&oReq); err != nil {
			msg = "参数解析失败"
			logger.Error(msg, err)
			return
		}
	}
	oReq.Path = strings.TrimLeft(oReq.Path, "/")
	oReq.NewName = strings.TrimLeft(oReq.NewName, "/")
	oReq.NewPath = strings.TrimLeft(oReq.NewPath, "/")

	if oReq.Path != "" && strings.HasSuffix(oReq.Path, "/") == false {
		oReq.Path += "/"
	}
	if oReq.NewPath != "" && strings.HasSuffix(oReq.NewPath, "/") == false {
		oReq.NewPath += "/"
	}

	if config.PrivateStorage == "" {
		msg = "用户私有存储路径为空"
		logger.Error(msg, oReq)
		return
	}
	if _, err := os.Stat(config.PrivateStorage); err == nil || os.IsExist(err) {
		if strings.HasPrefix(config.PrivateStorage, "/") && strings.HasSuffix(config.PrivateStorage, "/") {

		} else {
			msg = "存储路径格式错误"
			logger.Error(msg, oReq)
			return
		}
	} else {
		msg = "检测存储路径失败"
		logger.Error(msg, err, oReq)
		return
	}

	userBasePath := config.PrivateStorage

	if err := service.CheckPrivateStorage(); err != nil {
		msg = "基础存储验证失败"
		logger.Error(msg, err, oReq, claims.UserId)
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "获取用户信息失败"
		logger.Error(err)
		return
	}
	if user.ShortId == "" {
		if err := user.SetShortId(); err != nil {
			msg = "设置用户存储路径失败"
			logger.Error(msg, err)
			return
		}
	}
	if user.ShortId == "" {
		msg = "设置用户存储路径失败，请重试"
		logger.Error(msg, oReq)
		return
	}
	if len(user.PrivateStorage) < 10 {
		msg = "用户存储路径不正确，请重试"
		logger.Error(msg, oReq)
		return
	}
	if strings.HasPrefix(user.PrivateStorage, "/") == true || strings.HasSuffix(user.PrivateStorage, "/") == false {
		msg = "用户存储路径格式不正确，请重试"
		logger.Error(msg, oReq, user.PrivateStorage, " ", user.ID)
		return
	}

	userBasePath += user.PrivateStorage
	if _, err := os.Stat(userBasePath); os.IsNotExist(err) {
		logger.Info("用户存储路径不存在 开始创建存储路径", userBasePath, claims.UserId)
		if err := os.MkdirAll(userBasePath, os.ModePerm); err != nil {
			msg = "创建用户文件夹出错"
			logger.Error(msg, err, oReq)
			return
		}
	} else if err != nil {
		msg = "检测用户存储路径失败"
		logger.Error(msg, err, oReq, claims.UserId)
		return
	}

	action = oReq.Action
	if action == "" {
		msg = "参数错误"
		logger.Error(msg, oReq)
		return
	}
	if action == "Upload" {
		ary := strings.Split(oReq.Path, "/")
		if len(ary) > 50 {
			msg = "不支持自动创建三级结构目录"
			logger.Error(msg, oReq, claims.UserId)
			return
		}

		f, errf := c.FormFile("file")
		if errf != nil {
			msg = "文件上传失败"
			logger.Error(msg, errf)
			return
		}
		rootPath := userBasePath + oReq.Path
		if _, err := os.Stat(rootPath); err != nil {
			if os.IsNotExist(err) {
				if err := os.MkdirAll(rootPath, os.ModePerm); err != nil {
					msg = "创建文件夹出错"
					logger.Error(msg, err, oReq)
					return
				}
			} else {
				msg = "路径检测错误"
				logger.Error(msg, err)
				return
			}
		}
		filePath := rootPath + f.Filename
		if _, err := os.Stat(filePath); err != nil {
			if os.IsNotExist(err) {
			} else {
				msg = "路径检测错误"
				logger.Error(msg, err)
				return
			}
		} else {
			msg = "文件已存在，上传失败"
			logger.Error(msg, oReq, claims.UserId)
			return
		}
		if err := c.SaveUploadedFile(f, filePath); err != nil {
			msg = "文件保存失败"
			logger.Error(msg, err)
			return
		} else {
			msg = "文件上传成功"
			code = 0
			return
		}
	} else if action == "Download" {
		rootPath := userBasePath + oReq.Path
		info, err := os.Stat(rootPath)
		if err != nil {
			if os.IsNotExist(err) {
				msg = "路径不存在"
				logger.Error(msg, oReq)
				return
			} else {
				msg = "获取路径信息时出错"
				logger.Error(msg, oReq)
				return
			}
		}

		// 检查文件类型
		if info.Mode().IsRegular() {
			baseName := path.Base(rootPath)
			c.Header("Content-Disposition", "attachment; filename="+baseName)
			c.Header("Content-Type", "application/octet-stream")
			// 下载文件
			c.File(rootPath)
			return
		} else if info.Mode().IsDir() {
			msg = "文件路径错误"
			logger.Error(msg, oReq)
			return
		} else {
			msg = "是其它类型的文件"
			logger.Error(msg, oReq)
			return
		}

	} else if action == "GetDir" {
		rootPath := userBasePath + oReq.Path
		if arr, err := readDir(rootPath); err != nil {
			if strings.Contains(err.Error(), "no such file or directory") {
				msg = "no such file or directory"
				result["dir"] = arr
				code = 0
				return
			}
			msg = "列出文件出错"
			logger.Error(msg, err)
			return
		} else {
			result["root_path"] = strings.Replace(rootPath, userBasePath, "", -1)
			result["dir"] = arr
			code = 0
			return
		}
	} else if action == "DeleteFile" || action == "DeleteDir" {
		rootPath := userBasePath + oReq.Path
		info, err := os.Stat(rootPath)
		if err != nil {
			if os.IsNotExist(err) {
				msg = "路径不存在"
				logger.Error(msg, oReq)
				return
			} else {
				msg = "获取路径信息时出错"
				logger.Error(msg, oReq)
				return
			}
		}

		// 检查文件类型
		if info.Mode().IsRegular() {
			if action != "DeleteFile" {
				msg = "请选择要删除的文件而不是文件夹"
				return
			}
			if err = os.Remove(rootPath); err != nil {
				msg = "删除文件出错"
				logger.Error(msg, err)
				return
			}
			msg = "文件删除成功"
			code = 0
			return
		} else if info.Mode().IsDir() {
			if action != "DeleteDir" {
				msg = "请选择要删除的文件夹"
				return
			}
			if err = os.RemoveAll(rootPath); err != nil {
				msg = "删除文件夹出错"
				logger.Error(msg, err)
				return
			}
			msg = "文件夹删除成功"
			code = 0
			return
		} else {
			msg = "是其它类型的文件"
			logger.Error(msg, oReq)
			return
		}
	} else if action == "Rename" {
		rootPath := userBasePath + oReq.Path
		info, err := os.Stat(rootPath)
		if err != nil {
			if os.IsNotExist(err) {
				msg = "文件不存在"
				logger.Error(msg, oReq)
				return
			} else {
				msg = "获取文件信息时出错"
				logger.Error(msg, oReq)
				return
			}
		}
		dir := filepath.Dir(rootPath)
		newPath := dir + "/" + oReq.NewName

		// 检查文件类型
		if info.Mode().IsRegular() {
			if err = os.Rename(rootPath, newPath); err != nil {
				msg = "文件重命名出错"
				logger.Error(msg, err)
				return
			}
			msg = "文件重命名成功"
			code = 0
			return
		} else if info.Mode().IsDir() {
			if err = os.Rename(rootPath, newPath); err != nil {
				msg = "文件夹重命名出错"
				logger.Error(msg, err)
				return
			}
			msg = "文件夹重命名成功"
			code = 0
			return
		} else {
			msg = "是其它类型的文件"
			logger.Error(msg, oReq)
			return
		}
	} else if action == "CreateDir" {
		rootPath := userBasePath + oReq.Path
		_, err := os.Stat(rootPath)
		if err == nil {
			msg = "路径已存在"
			logger.Error(msg, oReq)
			return
		} else {
			if os.IsNotExist(err) {
				msg = "路径不存在"
			} else {
				msg = "获取信息出错"
				logger.Error(msg, oReq)
				return
			}
		}

		if err := os.MkdirAll(rootPath, os.ModePerm); err != nil {
			msg = "创建文件夹出错"
			logger.Error(msg, err, oReq)
			return
		} else {
			msg = "文件夹创建成功"
			code = 0
			return
		}

	} else if action == "CreateFile" {
		rootPath := userBasePath + oReq.Path
		_, err := os.Stat(rootPath)
		if err == nil {
			msg = "文件路径已存在"
			logger.Error(msg, oReq)
			return
		} else {
			if os.IsNotExist(err) {
				msg = "路径不存在"
			} else {
				msg = "获取信息出错"
				logger.Error(msg, oReq)
				return
			}
		}

		file, err := os.Create(rootPath)
		defer file.Close()
		if err != nil {
			msg = "创建文件出错"
			logger.Error(msg, err, oReq)
			return
		} else {
			msg = "文件创建成功"
			code = 0
			return
		}
	} else if action == "Move" {
		rootPath := userBasePath + oReq.Path
		targetPath := userBasePath + oReq.NewPath

		if strings.HasSuffix(targetPath, "/") == false {
			targetPath += "/"
		}
		info, err := os.Stat(rootPath)
		if err != nil {
			if os.IsNotExist(err) {
				msg = "路径不存在"
				logger.Error(msg, oReq)
				return
			} else {
				msg = "获取路径信息时出错"
				logger.Error(msg, oReq)
				return
			}
		}

		// 检查文件类型
		if info.Mode().IsRegular() {
			baseFileName := path.Base(rootPath)
			targetFilePath := targetPath + baseFileName
			if err = os.Rename(rootPath, targetFilePath); err != nil {
				msg = "文件移动出错"
				logger.Error(msg, err)
				return
			}
			msg = "文件移动成功"
			code = 0
			return
		} else if info.Mode().IsDir() {
			dirName := filepath.Base(rootPath)
			targetDirPath := targetPath + dirName
			if err = os.Rename(rootPath, targetDirPath); err != nil {
				msg = "文件夹移动出错"
				logger.Error(msg, err)
				return
			}
			msg = "文件夹移动成功"
			code = 0
			return
		} else {
			msg = "是其它类型的文件"
			logger.Error(msg, oReq)
			return
		}
	}

	msg = ""
	code = 0
}

type fileInfo struct {
	Path    string            `json:"path"`
	Name    string            `json:"name"`
	Size    int64             `json:"size"`
	IsDir   bool              `json:"is_dir"`
	ModTime jsontime.JsonTime `json:"mod_time"` //修改时间
	Mode    uint32            `json:"mode"`
}

func readDir(rootPath string) ([]fileInfo, error) {
	arr := make([]fileInfo, 0)
	if dirEntry, err := os.ReadDir(rootPath); err != nil {
		logger.Error(err)
		return arr, err
	} else {
		for i := 0; i < len(dirEntry); i++ {
			file := dirEntry[i]
			info, _ := file.Info()
			path := ""
			tmp := fileInfo{
				Path:    path,
				Name:    file.Name(),
				Size:    info.Size(),
				IsDir:   file.IsDir(),
				ModTime: jsontime.JsonTime(info.ModTime()),
				Mode:    uint32(file.Type()),
			}
			arr = append(arr, tmp)
		}
	}
	return arr, nil
}

func walkDir(root string) ([]fileInfo, error) {
	// 使用 Walk 函数遍历文件夹下的所有文件和目录
	arr := make([]fileInfo, 0)
	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			logger.Error(err)
			return err
		}
		tmp := fileInfo{
			Path:    path,
			Name:    info.Name(),
			Size:    info.Size(),
			IsDir:   info.IsDir(),
			ModTime: jsontime.JsonTime(info.ModTime()),
			Mode:    uint32(info.Mode()),
		}
		arr = append(arr, tmp)
		// 打印文件或目录的路径
		fmt.Println(path)
		return nil
	})
	if err != nil {
		logger.Error(err)
		return arr, fmt.Errorf("walkDir: %v", err)
	}
	return arr, nil
}

package controller

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"net/http"
	"runtime"
	"strings"
)

type couponApi_ struct {
}

var CouponApi couponApi_

type couponListReq struct {
	CouponCode string `json:"coupon_code"`
	CouponUuid string `json:"coupon_uuid"`
	Status     int    `json:"status"`
	Kw         string `json:"kw"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type couponResp struct {
	Uuid       string                   `json:"uuid"`
	CouponCode string                   `json:"coupon_code"`
	UserId     uint                     `json:"-"`
	Title      string                   `json:"title"`
	SalePrice  decimal.Decimal          `json:"sale_price"`
	FacePrice  decimal.Decimal          `json:"face_price"`
	ExpireDate jsontime.JsonTime        `json:"expire_date"`
	ValidDays  int                      `json:"valid_days"`
	Quantity   int                      `json:"quantity"`
	SingleMax  int                      `json:"single_max"`
	SoldCount  int                      `json:"sold_count"`
	LeaveCount int                      `json:"leave_count"`
	PodIds     string                   `json:"-"`
	Pods       string                   `json:"-"`
	PodsObj    []map[string]interface{} `json:"pods"`
	Remark     string                   `json:"-"`
	Status     int                      `json:"status"`
	StatusTxt  string                   `json:"status_txt"`
}

func (obj couponApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq couponListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}
	oReq.CouponCode = strings.TrimSpace(oReq.CouponCode)
	if len(oReq.CouponCode) != 6 {
		msg = "请输入6位优惠券"
		logger.Error(msg, oReq.CouponCode)
		return
	}
	oReq.CouponCode = strings.ToUpper(oReq.CouponCode)

	var coupon model.Coupon
	var ary = make([]couponResp, 0)
	var aryOut = make([]couponResp, 0)

	queryParm := make(map[string]interface{})

	queryParm["coupon_code"] = oReq.CouponCode

	if oReq.CouponUuid != "" {
		if err := coupon.GetByUuid(oReq.CouponUuid); err != nil {
			msg = "记录不存在"
			logger.Error(err)
			return
		}
		queryParm["id"] = coupon.ID
	}
	if oReq.Status >= 0 {
		queryParm["status"] = oReq.Status
	}

	if total, err := coupon.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		for i := 0; i < len(ary); i++ {
			if oReq.CouponCode != "" {
				if service.OutDateTime(ary[i].ExpireDate.Time()) {
					total--
					continue
				}
			}
			if ary[i].Status == 1 {
				ary[i].StatusTxt = "有效"
			} else {
				ary[i].StatusTxt = "无效"
			}
			ary[i].LeaveCount = ary[i].Quantity - ary[i].SoldCount
			ary[i].PodsObj = utils.GetMapAryFromJson(ary[i].Pods)
			if ary[i].UserId != claims.UserId {
				ary[i].Quantity = 0
				ary[i].SoldCount = 0
			}
			aryOut = append(aryOut, ary[i])
		}
		result["coupons"] = aryOut
		result["total"] = total
	}
	msg = ""
	code = 0
}

package controller

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/internal/ccm/dockerfile"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/service/tasklog"
	"cpn-ai/structs"
	"errors"
	"fmt"
	"net/http"
	"runtime"
	"slices"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type _instanceApi struct {
}

var InstanceApi _instanceApi

type instanceCreateReq struct {
	PodUuid      string          `json:"pod_uuid"`
	ImageUuid    string          `json:"image_uuid"`
	ImageTag     string          `json:"image_tag"`
	GpuModelUuid string          `json:"gpu_model_uuid"`
	Gpus         int             `json:"gpus"`
	ChargingType int             `json:"charging_type"`
	ChargingNum  int             `json:"charging_num"`
	Amount       decimal.Decimal `json:"amount"`

	NodeId    uint `json:"node_id"`
	VirtualId uint `json:"virtual_id"`
	NoCard    int  `json:"no_card"`
}

type instanceRenewalReq struct {
	InstanceUuid string          `json:"instance_uuid"`
	ChargingType int             `json:"charging_type"`
	ChargingNum  int             `json:"charging_num"`
	Amount       decimal.Decimal `json:"amount"`
}

type instanceStartupReq struct {
	InstanceUuid string `json:"instance_uuid"`
	GpuModelUuid string `json:"gpu_model_uuid"`
	Gpus         int    `json:"gpus"`
	NodeId       uint   `json:"node_id"`
	VirtualId    uint   `json:"virtual_id"`
	NoCard       int    `json:"no_card"`
}

type instanceSaveImageReq struct {
	InstanceUuid string `json:"instance_uuid"`
	NodeId       uint   `json:"node_id"`
	VirtualId    uint   `json:"virtual_id"`
	ImageTitle   string `json:"image_title"`
	ImageTag     string `json:"image_tag"`
	StorageMode  int    `json:"storage_mode"`
	Shutdown     bool   `json:"shutdown"`
}

type instanceShutdownReq struct {
	InstanceUuid   string `json:"instance_uuid"`
	Force          bool   `json:"force"`
	Destroy        bool   `json:"destroy"`
	Task           string `json:"task"`
	ShutdownReason string `json:"shutdown_reason"`
}

type instanceShutdownRegularTimeReq struct {
	InstanceUuid string `json:"instance_uuid"`
	RegularTime  string `json:"regular_time"`
	Cancel       bool   `json:"cancel"`
	Save         bool   `json:"save"`
}

type instanceCheckDockerReq struct {
	InstanceUuid string `json:"instance_uuid"`
	DockerId     string `json:"docker_id"`
}

type instanceReq struct {
	InstanceUuid string `json:"instance_uuid"`
	PodCategory  uint   `json:"pod_category"`
}

type instanceLogReq struct {
	InstanceUuid string `json:"instance_uuid"`
	Line         int    `json:"line"`
}

type checkShutdownReq struct {
	Action       string `json:"action"`
	InstanceUuid string `json:"instance_uuid"`
}

type instanceActionReq struct {
	Action       string `json:"action"`
	InstanceUuid string `json:"instance_uuid"`
	Title        string `json:"title"`
}

type instanceListReq struct {
	InstanceUuid string `json:"instance_uuid"`
	InstanceType int    `json:"instance_type"`
	PodCategory  uint   `json:"pod_category"`
	Status       int    `json:"status"`
	Page         int    `json:"page"`
	PageSize     int    `json:"page_size"`
}

type saveImageCommitReq struct {
	InstanceUuid string `json:"instance_uuid"`
}

type instanceResp struct {
	Uuid                string                      `json:"uuid"`
	Title               string                      `json:"title"`
	UserId              uint                        `json:"-"`
	PodId               uint                        `json:"-"`
	PodUuid             string                      `json:"pod_uuid"`
	PodTitle            string                      `json:"pod_title"`
	PodName             string                      `json:"pod_name"`
	PodPrice            string                      `json:"pod_price"`
	PodStartupElapse    int                         `json:"pod_startup_elapse"`
	PodCatalogue        []service.CatalogueListItem `json:"pod_catalogue" gorm:"-"`
	ImageTag            string                      `json:"image_tag"`
	ImageTitle          string                      `json:"image_title"`
	DockerId            string                      `json:"-"`
	OutWebUrl           string                      `json:"out_web_url"`
	Category            int                         `json:"category"` //1语言模型
	Gpus                int                         `json:"gpus"`
	NoCard              int                         `json:"no_card"`
	GpuModelId          uint                        `json:"-"`
	GpuModelName        string                      `json:"gpu_model_name"`
	GpuModel            map[string]interface{}      `json:"gpu_model"`
	ChargingType        int                         `json:"charging_type"`
	ChargingTypeName    string                      `json:"charging_type_name"`
	ChargingNum         int                         `json:"charging_num"`
	PriceH              decimal.Decimal             `json:"price_h"`
	InstanceType        int                         `json:"-"`
	InstanceTypeTxt     string                      `json:"instance_type_txt"`
	CurrentTime         jsontime.JsonTime           `json:"current_time"`
	StartTime           jsontime.JsonTime           `json:"start_time"`
	EndTime             jsontime.JsonTime           `json:"end_time"`
	StartupTime         jsontime.JsonTime           `json:"startup_time"`
	ShutdownTime        jsontime.JsonTime           `json:"shutdown_time"`
	ShutdownRegularTime jsontime.JsonTime           `json:"shutdown_regular_time"`
	ShutdownRegularSave bool                        `json:"shutdown_regular_save"`
	ShutdownDestroy     bool                        `json:"shutdown_destroy"`
	ShutdownTask        string                      `json:"shutdown_task"`
	Status              int                         `json:"status"`
	StatusTxt           string                      `json:"status_txt"`
	ImageId             uint                        `json:"-"`
	ImageName           string                      `json:"-"`
	ImageType           int                         `json:"image_type"`
	ImageLayerCount     int                         `json:"image_layer_count"`
	ImageTypeTxt        string                      `json:"image_type_txt"`
	ImageStatus         int                         `json:"image_status"`
	ImageStatusTxt      string                      `json:"image_status_txt"`
	ImageStartupLogTime string                      `json:"image_startup_log_time"`
	SaveImageId         uint                        `json:"-"`
	SaveImageStatus     int                         `json:"save_image_status"`
	SaveImageStatusTxt  string                      `json:"save_image_status_txt"`
	SaveImageStatusTime jsontime.JsonTime           `json:"save_image_status_time"`
	StartupImageId      uint                        `json:"-"`
	StartupImageUuid    string                      `json:"-"`
	StartupTxt          string                      `json:"startup_txt"`
	StartupLogTime      string                      `json:"startup_log_time"`
	StartupMark         string                      `json:"-"`
	StartupMaps         string                      `json:"startup_maps"`
	StartupMarkLog      []string                    `json:"startup_mark_log"`
	StartupInstLog      []string                    `json:"startup_inst_log"`
	StartupImageLog     []string                    `json:"startup_image_log"`

	TaskTxt      string            `json:"task_txt,omitempty"`
	TaskLastTime jsontime.JsonTime `json:"task_last_time,omitempty"`
	TaskPercent  float64           `json:"task_percent,omitempty"`

	ClassType  int                           `json:"class_type"`           //pod
	Components map[string]dockerfile.Version `json:"components,omitempty"` //ccm
}

type instanceStatusResp struct {
	Uuid        string `json:"uuid"`
	Status      int    `json:"status"`
	StatusTxt   string `json:"status_txt"`
	StartupMaps string `json:"startup_maps"`
}

type instanceMonitorReq struct {
	InstanceUuid string   `json:"instance_uuid"`
	StartTime    int64    `json:"start_time"`
	EndTime      int64    `json:"end_time"`
	Step         int64    `json:"step"` // 时间间隔
	Metrics      []string `json:"metrics"`
}

func (obj _instanceApi) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	if c.Value("claims") == nil {
		logger.Error("c.Value(\"claims\") is nil")
	}
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page < 1 {
		oReq.Page = 1
	}
	if oReq.PageSize < 1 || oReq.PageSize > 50 {
		oReq.PageSize = 20
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		logger.Error(err)
	}

	var instance model.Instance
	if oReq.InstanceUuid != "" {
		if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
			msg = "数据不存在"
			logger.Error(msg, err)
			return
		}
		if instance.ImageType == enums.ImageTypeEnum.Private || instance.ImageType == enums.ImageTypeEnum.PrivateInstance {
			var podImage model.PodImage
			if err := podImage.GetById(instance.ImageId); err != nil {

			} else {
				result["image_layer_count"] = podImage.LayerCount
			}
		}
	}

	if user.Insider > 0 {
		oReq.InstanceType = -1
	}

	var ary = make([]instanceResp, 0)
	if total, err := instance.List(&ary, instance.ID, claims.UserId, oReq.InstanceType, 0, -1, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {
			ary[i].CurrentTime = jsontime.Now()
			if ary[i].InstanceType > 0 {
				ary[i].InstanceTypeTxt = enums.InstanceTypeEnum.Name(ary[i].InstanceType)
			}

			ccm := ary[i].InstanceType == enums.InstanceTypeEnum.CCM || ary[i].ImageType == enums.ImageTypeEnum.CCM
			if ccm && ary[i].ImageType != enums.ImageTypeEnum.Private {
				if row := dockerfile.GetByKey(ary[i].ImageName); row != nil {
					ary[i].Components = row.Components
				}
			}

			if oReq.InstanceUuid != "" {
				if ary[i].ImageType == enums.ImageTypeEnum.Private || ary[i].ImageType == enums.ImageTypeEnum.PrivateInstance {
					var podImage model.PodImage
					if err := podImage.GetById(ary[i].ImageId); err != nil {

					} else {
						ary[i].ImageLayerCount = podImage.LayerCount
					}
				}
			}

			ary[i].StatusTxt = enums.InstanceStatusEnum.Name(ary[i].Status)
			if ary[i].Status == enums.InstanceStatusEnum.BootInProgress || instance.ID > 0 {
				if instance.StartupMark != "" {
					if ary[i].Status != enums.InstanceStatusEnum.ShutdownComplete && ary[i].Status != enums.InstanceStatusEnum.Hidden {
						if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.StartupMark, ary[i].StartupMark); err != nil {
							logger.Error(err)
						} else {
							if item, _, err := tasklog.Last(logKey); err != nil {
								if err != gorm.ErrEmptySlice {
									logger.Error(err, "StartupMark: ", ary[i].StartupMark)
								}
							} else {
								ary[i].StartupTxt = item.Msg
								if item.Percent > 0 && item.Percent <= 0.9999 {
									ary[i].StartupTxt = fmt.Sprintf("%s%.2f%%", item.Msg, item.Percent*100)
								}
								ary[i].StartupLogTime = item.Time.String()

							}
						}
					}
				}
			}

			//if ary[i].ImageType == enums.ImageTypeEnum.Private {
			//	ary[i].ImageTypeTxt = enums.ImageTypeEnum.Name(ary[i].ImageType)
			//} else {
			//	if ary[i].StartupImageId > 0 && ary[i].StartupImageId != ary[i].ImageId {
			//		var podImage model.PodImage
			//		if err := podImage.GetById(ary[i].StartupImageId); err != nil {
			//			logger.Error(err, " instanceUuid:", ary[i].Uuid)
			//		} else {
			//			if podImage.ImageType == enums.ImageTypeEnum.Private {
			//				ary[i].ImageTypeTxt = enums.ImageTypeEnum.Name(podImage.ImageType)
			//			}
			//		}
			//	}
			//}

			ary[i].ImageTypeTxt = enums.ImageTypeEnum.Name(ary[i].ImageType)
			if ary[i].StartupImageId > 0 {
				var podImage model.PodImage
				if err := podImage.GetById(ary[i].StartupImageId); err != nil {
					logger.Error(err, " instanceUuid:", ary[i].Uuid)
				} else {
					if podImage.ImageType == enums.ImageTypeEnum.Private {
						ary[i].ImageTypeTxt = enums.ImageTypeEnum.Name(podImage.ImageType)
						if ary[i].UserId != podImage.UserId {
							ary[i].ImageTypeTxt = "分享镜像"
						}
						ary[i].ImageTitle = podImage.Title
						if ary[i].Title == "" {
							ary[i].Title = podImage.Title
						}
						ary[i].ImageType = podImage.ImageType
						ary[i].ImageTag = podImage.ImageTag

						if ccm && podImage.ParentId > 0 {
							var parentImage model.PodImage
							if err := parentImage.GetById(podImage.ParentId); err != nil {
								logger.Error(err, " instanceUuid:", ary[i].Uuid)
							} else {
								if row := dockerfile.GetByKey(parentImage.ImageName); row != nil {
									ary[i].Components = row.Components
								}
							}
						}
					}
				}
			}

			if ary[i].SaveImageId > 0 {
				var podImage model.PodImage
				if err := podImage.GetById(ary[i].SaveImageId); err != nil {
					logger.Error(err, " instanceUuid:", ary[i].Uuid)
					//ary[i].ImageStatusTxt = err.Error()
				} else {

					ary[i].SaveImageStatus = podImage.AuditStatus
					ary[i].SaveImageStatusTxt = enums.ImageAuditStatusEnum.Name1(podImage.AuditStatus)
					if podImage.AuditStatus == enums.ImageAuditStatusEnum.PushFail && podImage.Reason != "" {
						ary[i].SaveImageStatusTxt += "(" + podImage.Reason + ")"
					}
					if podImage.ImageType == enums.ImageTypeEnum.PrivateInstance {
						ary[i].SaveImageStatusTxt = enums.ImageAuditStatusEnum.ReplaceImageToInstance(ary[i].SaveImageStatusTxt)
					}
					if podImage.AuditStatus == enums.ImageAuditStatusEnum.Pushing || podImage.AuditStatus == enums.ImageAuditStatusEnum.Commiting {

						if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(ary[i].SaveImageId)); err != nil {
							logger.Error(err)
						} else {
							if item, _, err := tasklog.Last(logKey); err != nil {
								if err != gorm.ErrEmptySlice {
									logger.Error(err)
								}
							} else {
								ary[i].SaveImageStatusTxt = item.Msg
								if item.Percent > 0 && item.Percent < 1 {
									ary[i].SaveImageStatusTxt = fmt.Sprintf("%s%.2f%%", item.Msg, item.Percent*100)
								}
								ary[i].SaveImageStatusTime = item.Time
							}
						}
					}
					if ary[i].Status == enums.InstanceStatusEnum.Running {
						ary[i].SaveImageStatusTxt = ""
					}
				}
			}

			//if user.Insider > 0 {
			//	ary[i].StartupMarkLog, _ = service.StartupLog.List(ary[i].StartupMark)
			//	ary[i].StartupInstLog, _ = service.StartupLog.List("inst:" + ary[i].Uuid)
			//	if ary[i].SaveImageId > 0 {
			//		ary[i].StartupImageLog, _ = service.StartupLog.List(fmt.Sprintf("SaveImage_%d", ary[i].SaveImageId))
			//	}
			//}

			ary[i].ChargingTypeName = enums.ChargingTypeEnum.Name(ary[i].ChargingType)
			//if instance.ID > 0 {

			priceH := decimal.Zero
			var pod model.Pod
			if err := pod.GetById(ary[i].PodId); err != nil {
				msg = "数据不存在"
				logger.Error(msg, err)
			} else {
				if pod.Title == "" && pod.AuditContent != "" {
					mm := utils.GetMapFromJson(pod.AuditContent)
					if mm != nil {
						if _, ok := mm["title"]; ok {
							pod.Title = mm["title"].(string)
						}
					}
				}

				if ccm {
					ary[i].ClassType = 9
				} else {
					ary[i].ClassType = pod.ClassType
				}

				ary[i].PodTitle = pod.Title
				ary[i].PodUuid = pod.Uuid
				ary[i].PodPrice = pod.Price
				ary[i].PodStartupElapse = pod.StartupElapse
				tmpAry1 := make([]service.CatalogueListItem, 0)
				if pod.Catalogue != "" {
					tmpAry := strings.Split(pod.Catalogue, ",")
					for _, val := range tmpAry {
						if aa := service.UserCatalogue.GetListItem(val); aa != nil {
							if power, err := service.UserCatalogue.GetNeedPrivilege(val); err != nil {
								logger.Error(err)
							} else {
								if power == enums.CataLoguePrivilegeEnum.USER {
									tmpAry1 = append(tmpAry1, *aa)
								} else if power == enums.CataLoguePrivilegeEnum.KOL && claims.UserId == pod.UserId {
									tmpAry1 = append(tmpAry1, *aa)
								}
							}
						}
					}
					sort.Slice(tmpAry1, func(i, j int) bool {
						return tmpAry1[i].Order < tmpAry1[j].Order
					})

				}
				ary[i].PodCatalogue = tmpAry1

				priceH = pod.CalculateAmount(enums.ChargingTypeEnum.Usage, 3600)
			}

			var gpuModel model.GpuModel
			if err := gpuModel.GetById(ary[i].GpuModelId); err != nil {
				msg = "数据不存在"
				logger.Error(msg, err)
			} else {

				if ary[i].NoCard == 1 {

				} else {
					settleAmount := gpuModel.CalculateAmount(enums.ChargingTypeEnum.Usage, 3600, ary[i].Gpus)
					priceH = priceH.Add(settleAmount)
				}

				m := make(map[string]interface{})
				m["gpu_name"] = gpuModel.GpuName
				m["price"] = gpuModel.Price
				m["uuid"] = gpuModel.Uuid
				m["title"] = gpuModel.Title
				if gpuModel.Title == "" {
					m["title"] = gpuModel.GpuName
				}
				ary[i].GpuModel = m
			}
			if ary[i].ChargingType == enums.ChargingTypeEnum.Free {
				ary[i].PriceH = decimal.Zero
			} else {
				ary[i].PriceH = priceH
			}
			//}
			if ary[i].Category == enums.PodCategoryEnum.PodInstance {
				ary[i].OutWebUrl = fmt.Sprintf("https://%s.hz01.%s", ary[i].Uuid, service.GetSiteDomain(c.Request.Referer()))
			}

			{
				outStartupMaps := ary[i].StartupMaps
				if outStartupMaps != "" {
					outStartupAry := make([]structs.OutStartupPortMap, 0)
					if err := utils.GetStructAryFromJson(&outStartupAry, outStartupMaps); err != nil {
						logger.Error(err, "  instance.StartupMaps:", instance.StartupMaps)
						return
					}
					ary[i].StartupMaps = utils.GetJsonFromStruct(outStartupAry)
				}
			}

			if service.GetSiteDomain(c.Request.Referer()) == "chenyu.cn" {
				ary[i].StartupMaps = strings.Replace(ary[i].StartupMaps, "suanyun.cn", "chenyu.cn", -1)
			}

			if ary[i].Status == enums.InstanceStatusEnum.ShutdownComplete || ary[i].Status == enums.InstanceStatusEnum.Created {
				if ary[i].ShutdownDestroy {
					//ary[i].Status = enums.InstanceStatusEnum.Hidden
					ary[i].StatusTxt += "(销毁中)"
					ary[i].SaveImageStatusTxt = ""
				}
			}

		}
		result["instance"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj _instanceApi) ContainerInfo(c *gin.Context) { //续费
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err, oReq)
		return
	}

	if instance.Status != enums.InstanceStatusEnum.Running {
		msg = "实例不在运行中"
		return
	}

	postData := make(map[string]interface{})
	postData["action"] = "ContainerInfo"
	postData["virtual_id"] = instance.StartupVirtualId
	postData["startup_mark"] = instance.StartupMark
	postData["container_id"] = instance.DockerId

	if claims.UserId == 2 {
		postData["o_req"] = true
	}

	if ginH, err := service.NodeService.ActionVirtual(instance.StartupVirtualId, postData); err != nil {
		msg = "容器信息获取失败"
		logger.Error(err)
		result["err"] = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj _instanceApi) ContainerLogs(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceLogReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err, oReq)
		return
	}

	if instance.Status != enums.InstanceStatusEnum.Running {
		msg = "实例不在运行中"
		return
	}

	var virtual model.Virtual
	if err := virtual.GetById(instance.StartupVirtualId); err != nil {
		msg = "虚拟机不存在"
		logger.Error(msg, err, oReq)
		return
	}

	if logs, err := service.DockerLogs(oReq.Line, &instance, &virtual); err != nil {
		msg = "容器日志获取失败"
		logger.Error(err)
		result["err"] = err.Error()
	} else {
		result["logs"] = logs
		code = 0
		msg = ""
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}

}

func (obj _instanceApi) Price(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}
	var oReq instanceCreateReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var gpuModel model.GpuModel
	if err := gpuModel.GetByUuid(oReq.GpuModelUuid); err != nil {
		msg = "显卡型号信息获取失败"
		logger.Error(msg, err)
		return
	}
	amount := gpuModel.CalculateAmount(oReq.ChargingType, oReq.ChargingNum, oReq.Gpus)

	result["amount"] = amount.String()
	msg = ""
	code = 0
}

func (obj _instanceApi) Create(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	var operatorLog model.OperationLog
	operatorShowMsg := "创建"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		ginH := gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		}
		if operatorLog.ID > 0 {
			if err := service.InstanceAccessService.UpdateInstanceAccess(operatorLog, ginH); err != nil {
				logger.Error(operatorShowMsg, " 更新实例关机日志失败 err:", err)
			}
		}
		c.JSON(http.StatusOK, ginH)
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceCreateReq
	logger.Info("创建实例：oReq:", utils.GetJsonFromStruct(oReq))
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	ccm := oReq.PodUuid == "ccm"
	if ccm {
		if !slices.Contains([]int{1, 2, 4, 8}, oReq.Gpus) {
			msg = "显卡数量不对"
			return
		}

		oReq.PodUuid = "046cff0c6c3444a8884800d0fdfc5d95" //
	}

	var pod model.Pod
	if err := pod.GetByUuid(oReq.PodUuid); err != nil {
		msg = "pod信息获取失败"
		logger.Error(msg, err)
		return
	}

	if pod.ClassType == 1 {
		if ok, msg1, err := service.CheckClassStuByPod(claims.UserId, pod); err != nil {
			msg = msg1
			logger.Error(msg, err)
			return
		} else {
			if !ok {
				msg = msg1
				return
			}
		}
	}

	if pod.Category != enums.PodCategoryEnum.PodInstance && pod.Category != enums.PodCategoryEnum.GpuInstance {
		msg = "pod类别不正确"
		logger.Error(msg, pod.ID)
		return
	}

	if !ccm {
		oReq.Gpus = pod.NeedGpus
		//if oReq.Gpus < pod.NeedGpus {
		//	//msg = fmt.Sprintf("该Pod至少需要个%d显卡", pod.NeedGpus)
		//	//logger.Error(msg, oReq)
		//	//return
		//	oReq.Gpus = pod.NeedGpus
		//}
		//if oReq.Gpus > pod.NeedGpus {
		//	msg = fmt.Sprintf("显卡数量最多选择3个")
		//	logger.Error(msg, oReq)
		//	return
		//}
	}

	var gpuModel model.GpuModel
	if err := gpuModel.GetByUuid(oReq.GpuModelUuid); err != nil {
		msg = "显卡型号信息获取失败"
		logger.Error(msg, err)
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "用户信息获取失败"
		logger.Error(msg, err)
		return
	}

	//if user.Insider == 0 {
	//	msg = "站点正在更新服务，暂时不能启动"
	//	return
	//}

	//var instanceLimit model.Instance
	//if total, err := instanceLimit.CountForLimit(claims.UserId); err != nil {
	//	msg = "查询实例数量失败"
	//	logger.Error(msg, err, "user_id:", claims.UserId)
	//	return
	//} else {
	//	if total >= 5 {
	//		msg = "实例数量已到限额，请先到【我的POD实例】中删除再创建"
	//		return
	//	}
	//}

	unitPrice := pod.UnitPrice(enums.ChargingTypeEnum.Usage)
	if unitPrice.GreaterThan(decimal.Zero) {
		if user.Amount.LessThan(decimal.Zero) {
			msg = "该Pod作者需要收取服务费，您的余额不足，请先充值"
			code = 5
			return
		}
	}

	instanceTitle := ""

	var podImage model.PodImage
	if ccm && len(oReq.ImageUuid) == 64 {
		row := dockerfile.GetByKey(oReq.ImageUuid)
		if row == nil {
			msg = "算力市场镜像不存在"
			return
		}

		err := podImage.GetByImageTypeAndTag(enums.ImageTypeEnum.CCM, row.Key, row.Tag)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			podImage = model.PodImage{
				Uuid:        utils.GetUUID(),
				UserId:      pod.UserId,
				ImageType:   enums.ImageTypeEnum.CCM,
				Title:       string(row.Components["CUDA"]), //TODO
				PodId:       pod.ID,
				PodUuid:     pod.Uuid,
				ImageName:   row.Key,
				ImageTag:    row.Tag,
				AuditStatus: enums.ImageAuditStatusEnum.AuditPass,
				Status:      1,
				StorageMode: enums.ImageStorageModeEnum.Registry,
			}

			err = podImage.Save()
			if err != nil {
				msg = "市场保存镜像信息失败"
				return
			}
		} else if err != nil {
			msg = "查询算力市场镜像信息失败"
			return
		}

		instanceTitle = pod.Title
	} else if oReq.ImageUuid != "" {
		if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
			msg = "查询镜像信息出错"
			logger.Error(msg, err)
			return
		}
		if podImage.LastSaveTime.Before(common.DefaultTime.Add(time.Hour)) {
			msg = "该镜像还没保存成功"
			return
		}
		if (podImage.ImageType == enums.ImageTypeEnum.Private || podImage.ImageType == enums.ImageTypeEnum.PrivateInstance) && podImage.AuditStatus != enums.ImageAuditStatusEnum.PushSuccess {
			msg = "该镜像还没保存成功"
			return
		}
		if podImage.ImageType == enums.ImageTypeEnum.Private && podImage.Title != "" {
			instanceTitle = podImage.Title
		}
		if podImage.ImageType == enums.ImageTypeEnum.Private && podImage.UserId != claims.UserId {
			checkStr := podImage.Share
			if !strings.Contains(checkStr, fmt.Sprintf("|%s|", user.Mobile)) && !strings.Contains(checkStr, fmt.Sprintf("|%s|", user.DisplayName)) {
				msg = "该镜像不是您的私有镜像，无法创建"
				logger.Error(msg, utils.GetJsonFromStruct(oReq))
				return
			}
		}
	} else if oReq.ImageTag != "" {
		if err := podImage.GetByPodIdAndTag(pod.ID, oReq.ImageTag); err != nil {
			if pod.UserId > 0 {
				msg = "查询Pod版本信息出错"
				logger.Error(msg, err)
				return
			}
		}
	} else {
		if err := podImage.GetLastTagByPodId(pod.ID); err != nil {
			msg = "查询最新Pod版本信息出错"
			logger.Error(err)
			//return
		}
	}

	if podImage.PodId > 0 {
		if podImage.PodId != pod.ID {
			msg = "Pod镜像不匹配"
			logger.Error(msg)
			return
		}
	}

	if user.Free != 1 { //1不做金额检测
		if oReq.ChargingType == enums.ChargingTypeEnum.Day || oReq.ChargingType == enums.ChargingTypeEnum.Week || oReq.ChargingType == enums.ChargingTypeEnum.Month {
			msg = "目前不支持该计费方式"
			logger.Error(msg, " oReq:", utils.GetJsonFromStruct(oReq))
			return
		}
		if oReq.ChargingType == enums.ChargingTypeEnum.Usage {
			validAmount := user.Amount
			var card model.Card
			if leaveAmount, err := card.CardValidAmount(claims.UserId, pod.ID); err != nil {
				logger.Error("CardValidAmount   ", "err:", err, " leaveAmount:", leaveAmount.String(), " userId:", claims.UserId, "   podId:", pod.ID)
			} else {
				logger.Info("CardValidAmount   ", "leaveAmount:", leaveAmount.String(), " userId:", claims.UserId, "   podId:", pod.ID)
				validAmount = validAmount.Add(leaveAmount)
			}

			if validAmount.LessThanOrEqual(decimal.Zero) {
				msg = "余额不足，请充值" //余额不足，Pod启动需要预存一个小时的余额，用于支付超时的算力和Pod服务费请充值
				msg = fmt.Sprintf("您当前的余额为%s，Pod启动需要至少预存一个小时的备用额度，用于支付可能超出的Pod服务费、镜像存储费、算力等。", user.Amount.Truncate(2).String())
				logger.Error(msg)
				code = 5 //余额不足，前端需要引导充值
				return
			}

			var countOfInstance model.Instance
			if count, err := countOfInstance.CountOfBootInOrRunning(claims.UserId); err != nil {
				logger.Error(err, " userId:", claims.UserId)
			} else {
				if count >= 1 {
					showValidAmount := validAmount
					for i := 0; i < count; i++ {
						validAmount = validAmount.Sub(decimal.NewFromInt(2))
						if validAmount.LessThan(decimal.Zero) {
							msg = fmt.Sprintf("已有%d个实例在运行中，您当前的有效余额为%s，不够再启动实例，请先充值或关闭运行中的实例。", count, showValidAmount.Truncate(2).String())
							logger.Error(msg)
							code = 5 //余额不足，前端需要引导充值
							return
						}
					}
				}
			}
		}
	}

	instance := model.Instance{
		Uuid:         utils.GetUUID(),
		UserId:       claims.UserId,
		PodId:        pod.ID,
		PodName:      pod.PodName,
		Category:     pod.Category,
		Title:        instanceTitle,
		ImageId:      podImage.ID,
		ImageType:    podImage.ImageType,
		ImageName:    podImage.ImageName,
		ImageTag:     podImage.ImageTag,
		ChargingType: oReq.ChargingType,
		ChargingNum:  oReq.ChargingNum,
		Gpus:         oReq.Gpus,
		GpuModelId:   gpuModel.ID,
		GpuModelName: gpuModel.GpuName,
	}

	if ccm {
		instance.InstanceType = 9
	}

	if oReq.ChargingType == enums.ChargingTypeEnum.Day || oReq.ChargingType == enums.ChargingTypeEnum.Week || oReq.ChargingType == enums.ChargingTypeEnum.Month {
		now := time.Now().Truncate(time.Second)
		endTime := service.CalculateEndTime(oReq.ChargingType, now, oReq.ChargingNum)
		instance.StartTime = now
		instance.EndTime = endTime

		price := gpuModel.CalculateAmount(oReq.ChargingType, oReq.ChargingNum, oReq.Gpus)
		if price.Cmp(decimal.Zero) == 0 {
			msg = "金额计算为零"
			logger.Error(msg)
			return
		}
		if user.Amount.LessThan(price) {
			msg = "余额不足，请充值"
			logger.Error(msg, user.ID, " ", price)
			return
		}

		if !price.Equal(oReq.Amount) {
			msg = "输入金额不一致"
			logger.Error(msg, oReq)
			return
		}
		instance.LastSettleAmount = price
		instance.LastSettleTime = now
	} else {
		instance.ChargingType = enums.ChargingTypeEnum.Usage
		instance.ChargingNum = 0
	}

	if err := instance.Create(); err != nil {
		msg = "实例创建失败"
		logger.Error(msg, err, oReq)
		return
	} else {
		resp := instanceResp{
			StatusTxt: enums.InstanceStatusEnum.Name(instance.Status),
		}
		if err := utils.Scan(instance, &resp); err != nil {
			msg = "转换失败，请刷新"
			logger.Error(err)
			return
		}
		result["instance"] = resp

		if operatorLog_, err := service.InstanceAccessService.SaveInstanceAccess(instance, claims.UserId, enums.OperatorUserTypeEnum.User, operatorShowMsg, oReq, c); err != nil {
			logger.Error("保存实例日志失败 uuid:", instance.Uuid, " showMsg:", operatorShowMsg, " err:", err)
		} else {
			operatorLog = operatorLog_
		}
	}

	if err := pod.UpUsageCount(); err != nil {
		logger.Error(err, "podId:", pod.ID)
	}

	if ccm {
		if config.Env == enums.EnvEnum.PRODUCTION {
			//
		} else {
			oReq.NodeId = 2
		}

		if txt, err := service.InstanceNodeService.StartupPro(instance.Uuid, oReq.NodeId, oReq.VirtualId, oReq.NoCard); err != nil {
			msg = txt
			logger.Error(msg, err)
			return
		} else {
			msg = txt
		}
	} else {
		msg = "创建完成"
	}
	code = 0
}

func (obj _instanceApi) CreateLLM(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceCreateReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var pod model.Pod
	if err := pod.GetByUuid(oReq.PodUuid); err != nil {
		msg = "pod信息获取失败"
		logger.Error(msg, err)
		return
	}
	if pod.Category != enums.PodCategoryEnum.LLM {
		msg = "pod类别不正确"
		logger.Error(msg, pod.ID)
		return
	}
	if oReq.Gpus < pod.NeedGpus {
		msg = fmt.Sprintf("该Pod至少需要个%d显卡", pod.NeedGpus)
		logger.Error(msg, oReq)
		return
	}
	if oReq.Gpus > 3 {
		msg = fmt.Sprintf("显卡数量最多选择3个")
		logger.Error(msg, oReq)
		return
	}
	var gpuModel model.GpuModel
	if err := gpuModel.GetByUuid(oReq.GpuModelUuid); err != nil {
		msg = "显卡型号信息获取失败"
		logger.Error(msg, err)
		return
	}

	oReq.Gpus = pod.NeedGpus
	oReq.ChargingType = enums.ChargingTypeEnum.Usage
	oReq.ChargingNum = 0

	instance := model.Instance{
		Uuid:         utils.GetUUID(),
		UserId:       0,
		PodId:        pod.ID,
		PodName:      pod.PodName,
		Category:     pod.Category,
		ChargingType: oReq.ChargingType,
		ChargingNum:  oReq.ChargingNum,
		Gpus:         oReq.Gpus,
		GpuModelId:   gpuModel.ID,
		GpuModelName: gpuModel.GpuName,
	}

	if err := instance.Create(); err != nil {
		msg = "实例创建失败"
		logger.Error(msg, err, oReq)
		return
	} else {
		resp := instanceResp{
			StatusTxt: enums.InstanceStatusEnum.Name(instance.Status),
		}
		if err := utils.Scan(instance, &resp); err != nil {
			msg = "转换失败，请刷新"
			logger.Error(err)
			return
		}
		result["instance"] = resp
	}
	msg = "创建完成"
	code = 0
}

func (obj _instanceApi) Status(c *gin.Context) { //续费
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceRenewalReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err, oReq)
		return
	}

	if instance.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	outStartupMaps := instance.StartupMaps
	if instance.Status == enums.InstanceStatusEnum.Running {
		if instance.StartupMaps != "" {

			ary := make([]structs.StrarupPortMap, 0)
			if err := utils.GetStructAryFromJson(&ary, instance.StartupMaps); err != nil {
				logger.Error(err, "  instance.StartupMaps:", instance.StartupMaps)
				return
			}

			needSafe := false
			for i := 0; i < len(ary); i++ {
				if ary[i].InnerAt == 0 {
					if ary[i].InnerUrl == "" {
						ary[i].InnerAt = 10
						needSafe = true
					} else {
						if utils.CheckUrl(ary[i].InnerUrl) {
							ary[i].InnerAt = time.Now().Unix()
							needSafe = true
						}
					}
				}
			}
			if needSafe {
				outStartupMaps = utils.GetJsonFromStruct(ary)
				if err := instance.SetStartupMaps(outStartupMaps); err != nil {
					logger.Error("设置SetStartupMaps失败  instanceUuid:", instance.Uuid)
				}
			}
		}
	}

	if outStartupMaps != "" {
		outStartupAry := make([]structs.OutStartupPortMap, 0)
		if err := utils.GetStructAryFromJson(&outStartupAry, outStartupMaps); err != nil {
			logger.Error(err, "  instance.StartupMaps:", instance.StartupMaps)
			return
		}
		outStartupMaps = utils.GetJsonFromStruct(outStartupAry)
	}

	result["instance"] = instanceStatusResp{
		Uuid:        instance.Uuid,
		Status:      instance.Status,
		StatusTxt:   enums.InstanceStatusEnum.Name(instance.Status),
		StartupMaps: outStartupMaps,
	}

	msg = ""
	code = 0
}

func (obj _instanceApi) MointorData(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceMonitorReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	for _, metric := range oReq.Metrics {
		if metric == "" {
			msg = "指标不能为空"
			return
		}
		// 判断指标是否存在
		if _, ok := model.AllPrometheusLabels[metric]; !ok {
			msg = "指标不存在"
			logger.Error(msg, metric)
			return
		}
	}

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err, oReq)
		return
	}

	if instance.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if instance.StartTime.UnixMilli() < ************ {
		msg = "实例尚未启动，无法查询"
		return
	}

	if oReq.StartTime == 0 || oReq.StartTime < instance.StartTime.UnixMilli() {
		logger.Debug("查询开始时间小于启动时间，按照启动时间查询")
		oReq.StartTime = instance.StartTime.UnixMilli()
	}

	if oReq.EndTime == 0 || oReq.EndTime > instance.EndTime.UnixMilli() {
		// 如果结束时间是默认值 小于2000年01月01日00时00分00秒,或者处于正在运行中
		if instance.Status == enums.InstanceStatusEnum.Running || instance.EndTime.UnixMilli() < ************ {
			oReq.EndTime = time.Now().UnixMilli()
		} else {
			logger.Debug("查询结束时间大于结束时间，按照结束时间查询")
			oReq.EndTime = instance.EndTime.UnixMilli()
		}
	}

	var virtual model.Virtual
	if err := virtual.GetById(instance.StartupVirtualId); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err, oReq)
		return
	}
	var gpus []int
	if err := utils.GetStructFromJson(&gpus, instance.StartupGpus); err != nil {
		logger.Error(err)
	}

	err, data := service.MonitorData(instance.Uuid, gpus, virtual.Host, oReq.Metrics, oReq.StartTime, oReq.EndTime, oReq.Step)
	if err != nil {
		msg = err.Error()
		logger.Error(msg)
		return
	}

	result["data"] = data
	msg = "查询成功"
	code = 0

}

func (obj _instanceApi) MointorMetrics(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}
	var metrics = make([]model.PrometheusLabel, 0)
	// 获取可用指标的列表
	for _, metric := range model.AllPrometheusLabels {
		metrics = append(metrics, metric)
	}

	result["data"] = metrics
	msg = "查询成功"
	code = 0

}

func (obj _instanceApi) Renewal(c *gin.Context) { //续费
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceRenewalReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err, oReq)
		return
	}

	if instance.ChargingType == enums.ChargingTypeEnum.Usage {
		msg = "按量计费模式无续费功能"
		return
	}

	var pod model.Pod
	if err := pod.GetById(instance.PodId); err != nil {
		msg = "pod信息获取失败"
		logger.Error(msg, err)
		return
	}
	if pod.Category != enums.PodCategoryEnum.PodInstance && pod.Category != enums.PodCategoryEnum.GpuInstance {
		msg = "pod类别不正确"
		logger.Error(msg, pod.ID)
		return
	}

	if err := instance.Renewal(oReq.ChargingType, oReq.ChargingNum, oReq.Amount); err != nil {
		msg = "续费失败"
		logger.Error(msg, err, oReq)
		return
	} else {
		resp := instanceResp{
			StatusTxt: enums.InstanceStatusEnum.Name(instance.Status),
		}
		if err := utils.Scan(instance, &resp); err != nil {
			msg = "转换失败，请刷新"
			logger.Error(err)
			return
		}
		result["instance"] = resp
	}
	msg = "续费成功"
	code = 0
}

func (obj _instanceApi) SettlePrice(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err, oReq)
		return
	}
	//var pod model.Pod
	//if err := pod.GetById(instance.PodId); err != nil {
	//	msg = "Pod不存在"
	//	logger.Error(msg, err, oReq)
	//	return
	//}
	var gpuModel model.GpuModel
	if err := gpuModel.GetById(instance.GpuModelId); err != nil {
		msg = "显卡型号不存在"
		logger.Error(msg, err, oReq)
		return
	}
	if instance.ChargingType == enums.ChargingTypeEnum.Usage {

		startTime := instance.StartupTime
		if instance.LastSettleTime.After(startTime) {
			startTime = instance.LastSettleTime
		}
		settleTime := time.Now()
		seconds := settleTime.Sub(startTime).Seconds()
		if startTime.Before(time.Now().AddDate(-10, 0, 0)) { //没有启动过
			seconds = 0
		}

		settleAmount := gpuModel.CalculateAmount(instance.ChargingType, int(seconds), instance.Gpus)

		result["settle_amount"] = settleAmount.String()
		result["settle_txt"] = fmt.Sprintf("如转为包日模式，您现在大概有%s金额需要结算", settleAmount.String())
	} else {
		settleTime := time.Now()
		settleAmount := gpuModel.CalculateCostedAmount(instance.ChargingType, instance.StartTime, settleTime, instance.Gpus)
		if settleAmount.LessThanOrEqual(decimal.Zero) {
			err := errors.New("计算消耗的金额出错")
			logger.Error(msg, err, oReq)
			return
		}
		refundAmount := instance.LastSettleAmount.Sub(settleAmount)
		result["settle_amount"] = settleAmount.String()
		result["refund_amount"] = refundAmount.String()
		result["settle_txt"] = fmt.Sprintf("本次已消耗%s金额，如现在转换为按量付费，您大概可获得%s金额的退款", settleAmount.String(), refundAmount.String())
	}
	code = 0
}

func (obj _instanceApi) ChangeToUsage(c *gin.Context) { //转按量计费
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceRenewalReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err, oReq)
		return
	}

	if instance.ChargingType == enums.ChargingTypeEnum.Usage {
		msg = "当前是按量计费模式"
		return
	}

	if err := service.ChargingService.Sub2Usage(oReq.InstanceUuid); err != nil {
		msg = "转按量计费失败"
		logger.Error(msg, err, oReq)
		return
	}

	msg = "转按量计费成功"
	code = 0
}

func (obj _instanceApi) ChangeToSub(c *gin.Context) { //转按量计费
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceRenewalReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err, oReq)
		return
	}

	if instance.ChargingType == enums.ChargingTypeEnum.Day || instance.ChargingType == enums.ChargingTypeEnum.Week || instance.ChargingType == enums.ChargingTypeEnum.Month {
		msg = fmt.Sprintf("当前是%s模式", enums.ChargingTypeEnum.Name(instance.ChargingType))
		return
	}
	if oReq.ChargingType != enums.ChargingTypeEnum.Day && oReq.ChargingType != enums.ChargingTypeEnum.Week && oReq.ChargingType != enums.ChargingTypeEnum.Month {
		msg = "要转换的计费模式不正确"
		return
	}

	if err := service.ChargingService.Usage2Sub(oReq.InstanceUuid, oReq.ChargingType, oReq.ChargingNum, oReq.Amount); err != nil {
		msg = "转包年包月计费失败"
		msg = err.Error()
		logger.Error(msg, err, oReq)
		return
	}

	msg = "转包年包月计费成功"
	code = 0
}

func (obj *_instanceApi) Startup(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	lockKey := ""
	var operatorLog model.OperationLog
	operatorShowMsg := "开机"
	defer func() {
		if lockKey != "" {
			common.RedisUnLock(lockKey)
		}
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		ginH := gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		}
		if operatorLog.ID > 0 {
			if err := service.InstanceAccessService.UpdateInstanceAccess(operatorLog, ginH); err != nil {
				logger.Error(operatorShowMsg, " 更新实例关机日志失败 err:", err)
			}
		}
		c.JSON(http.StatusOK, ginH)
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceStartupReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}
	lockKey = enums.RedisKeyEnum.LockKey + "instance_startup_" + oReq.InstanceUuid
	if common.RedisLock(lockKey, 1, 1000*10) {

	} else {
		msg = "请勿频繁操作"
		return
	}

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err)
		return
	}

	if operatorLog_, err := service.InstanceAccessService.SaveInstanceAccess(instance, claims.UserId, enums.OperatorUserTypeEnum.User, operatorShowMsg, oReq, c); err != nil {
		logger.Error("保存实例日志失败 uuid:", instance.Uuid, " showMsg:", operatorShowMsg, " err:", err)
	} else {
		operatorLog = operatorLog_
	}

	if instance.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "用户信息获取失败"
		logger.Error(msg, err)
		return
	}

	//if user.Insider == 0 {
	//	msg = "站点正在更新服务，暂时不能启动"
	//	return
	//}

	if user.Free != 1 { //1不做金额检测
		if instance.ChargingType == enums.ChargingTypeEnum.Day || instance.ChargingType == enums.ChargingTypeEnum.Week || instance.ChargingType == enums.ChargingTypeEnum.Month {
			if instance.EndTime.Before(time.Now()) {
				msg = enums.ChargingTypeEnum.Name(instance.ChargingType) + "已到期，请续费后再启动"
				return
			}
		}
		if instance.ChargingType == enums.ChargingTypeEnum.Usage {
			validAmount := user.Amount
			var card model.Card
			if leaveAmount, err := card.CardValidAmount(claims.UserId, instance.PodId); err != nil {
				logger.Error("CardValidAmount   ", "err:", err, " leaveAmount:", leaveAmount.String(), " userId:", claims.UserId, "   podId:", instance.PodId)
			} else {
				logger.Info("CardValidAmount   ", "leaveAmount:", leaveAmount.String(), " userId:", claims.UserId, "   podId:", instance.PodId)
				validAmount = validAmount.Add(leaveAmount)
			}

			if validAmount.LessThanOrEqual(decimal.Zero) {
				msg = "余额不足，请充值" //余额不足，Pod启动需要预存一个小时的余额，用于支付超时的算力和Pod服务费请充值
				msg = fmt.Sprintf("您当前的余额为%s，Pod启动需要至少预存一个小时的备用额度，用于支付可能超出的Pod服务费、镜像存储费、算力等。", user.Amount.Truncate(2).String())
				logger.Error(msg, "  userID:", claims.UserId)
				code = 5 //余额不足，前端需要引导充值
				return
			}

			var countOfInstance model.Instance
			if count, err := countOfInstance.CountOfBootInOrRunning(instance.UserId); err != nil {
				logger.Error(err, " userId:", instance.UserId)
			} else {
				if count >= 1 {
					showValidAmount := validAmount
					for i := 0; i < count; i++ {
						validAmount = validAmount.Sub(decimal.NewFromInt(2))
						if validAmount.LessThan(decimal.Zero) {
							msg = fmt.Sprintf("已有%d个实例在运行中，您当前的有效余额为%s，不够再启动实例，请先充值或关闭运行中的实例。", count, showValidAmount.Truncate(2).String())
							logger.Error(msg)
							code = 5 //余额不足，前端需要引导充值
							return
						}
					}
				}
			}
		}
	}

	if user.ShortId == "" {
		if err := user.SetShortId(); err != nil {
			msg = "创建用户存储路径失败"
			logger.Error(msg, err)
			return
		}
	}

	if oReq.VirtualId > 0 {
		if user.Insider <= 0 {
			msg = "无权限"
			logger.Error(msg, user.ID, " ", instance.Uuid)
			return
		}
		var virtual model.Virtual
		if err := virtual.GetById(oReq.VirtualId); err != nil {
			msg = "指定服务器不存在"
			logger.Error(err, virtual.ID)
			return
		}

		if virtual.GpuModelId != instance.GpuModelId {
			var gpuModel model.GpuModel
			if err := gpuModel.GetById(virtual.GpuModelId); err != nil {
				msg = "显卡型号信息获取失败"
				logger.Error(msg, err)
				return
			} else {
				if instance.GpuModelId != gpuModel.ID {
					if err := instance.SetGpuModel(gpuModel.ID, gpuModel.GpuName); err != nil {
						msg = "显卡型号设置失败"
						logger.Error(msg, err, "instanceId:", instance.ID, "gpuModelId:", gpuModel.ID)
						return
					}
				}
			}
		}

	} else if oReq.GpuModelUuid != "" {
		var gpuModel model.GpuModel
		if err := gpuModel.GetByUuid(oReq.GpuModelUuid); err != nil {
			msg = "显卡型号信息获取失败"
			logger.Error(msg, err)
			return
		} else {
			if instance.GpuModelId != gpuModel.ID {
				if err := instance.SetGpuModel(gpuModel.ID, gpuModel.GpuName); err != nil {
					msg = "显卡型号设置失败"
					logger.Error(msg, err, "instanceId:", instance.ID, "gpuModelId:", gpuModel.ID)
					return
				}
			}
		}
	}

	if config.Env == enums.EnvEnum.PRODUCTION {
		if txt, err := service.InstanceNodeService.StartupPro(oReq.InstanceUuid, oReq.NodeId, oReq.VirtualId, oReq.NoCard); err != nil {
			msg = txt
			logger.Error(msg, err)
			return
		} else {
			msg = txt
		}
	} else {
		oReq.NodeId = 27
		if txt, err := service.InstanceNodeService.StartupPro(oReq.InstanceUuid, oReq.NodeId, oReq.VirtualId, oReq.NoCard); err != nil {
			msg = txt
			logger.Error(msg, err)
			return
		} else {
			msg = txt
		}
	}
	code = 0
}

func (obj _instanceApi) StartupAbort(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	var operatorLog model.OperationLog
	operatorShowMsg := "终止开机"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		if code != 0 || msg == "开机已终止" {
			ginH := gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			}
			if operatorLog.ID > 0 {
				if err := service.InstanceAccessService.UpdateInstanceAccess(operatorLog, ginH); err != nil {
					logger.Error(operatorShowMsg, " 更新实例关机日志失败 err:", err)
				}
			}
			c.JSON(http.StatusOK, ginH)
		}
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "查询用户信息失败"
		logger.Error(msg, err)
		return
	}

	var oReq instanceStartupReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("接收到用户取消Pod开机操作 oReq:", utils.GetJsonFromStruct(oReq))
	defer func() {
		logger.Info("接收到用户取消Pod开机操作 oReq:", utils.GetJsonFromStruct(oReq), " Result:", utils.GetJsonFromStruct(result))
	}()

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err)
		return
	}

	if operatorLog_, err := service.InstanceAccessService.SaveInstanceAccess(instance, claims.UserId, enums.OperatorUserTypeEnum.User, operatorShowMsg, oReq, c); err != nil {
		logger.Error("保存实例日志失败 uuid:", instance.Uuid, " showMsg:", operatorShowMsg, " err:", err)
	} else {
		operatorLog = operatorLog_
	}

	if instance.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if instance.Status != enums.InstanceStatusEnum.BootInProgress {
		msg = "实例不在开机中"
		return
	}

	if instance.StartupMark == "" {
		if str, err := service.InstanceNodeService.StartupFail(instance.Uuid, instance.StartupMark, nil, "主动取消"); err != nil {
			msg = str
			logger.Error(err, str, instance.Uuid)
			return
		} else {
			msg = "开机已终止"
			code = 0
			return
		}
	}

	if ginH, err := service.NodeService.StartupAbort(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark); err != nil {
		msg = err.Error()
		logger.Error(err, " ginH:", ginH)
	} else {
		c.JSON(http.StatusOK, ginH)
		code = 0
		if operatorLog.ID > 0 {
			if err := service.InstanceAccessService.UpdateInstanceAccess(operatorLog, ginH); err != nil {
				logger.Error(operatorShowMsg, " 更新实例关机日志失败 err:", err)
			}
		}
	}
}

// 关机并保存不释放  destroy:false task:SaveImageAndShutdown 关机并保存
// 关机释放并保存 destroy:true task:SaveImageAndDestroy  保存镜像并且销毁
// 关机释放  destroy:true task:ShutdownAndDestroy 关闭并销毁实例
func (obj *_instanceApi) Shutdown(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	var operatorLog model.OperationLog
	operatorShowMsg := ""
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("Shutdown panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		ginH := gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		}
		if operatorLog.ID > 0 {
			if err := service.InstanceAccessService.UpdateInstanceAccess(operatorLog, ginH); err != nil {
				logger.Error(operatorShowMsg, " 更新实例关机日志失败 err:", err)
			}
		}
		c.JSON(http.StatusOK, ginH)
	}()
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceShutdownReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}
	logger.Info("接收到用户关机操作 oReq:", utils.GetJsonFromStruct(oReq))
	defer func() {
		logger.Info("接收到用户关机操作 oReq:", utils.GetJsonFromStruct(oReq), " code:", code, " msg:", msg, " result:", utils.GetJsonFromStruct(result))
	}()

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err)
		return
	}
	operatorShowMsg = "保存并关机"
	if oReq.Destroy {
		operatorShowMsg = "关机并销毁"
	}
	if operatorLog_, err := service.InstanceAccessService.SaveInstanceAccess(instance, claims.UserId, enums.OperatorUserTypeEnum.User, operatorShowMsg, oReq, c); err != nil {
		logger.Error("保存实例日志失败 uuid:", instance.Uuid, " showMsg:", operatorShowMsg, " err:", err)
	} else {
		operatorLog = operatorLog_
	}

	if instance.UserId != claims.UserId {
		msg = "无权限"
		return
	}
	logger.Info("执行实例关闭操作 instanceUuid:", instance.Uuid, "   startupMark:", instance.StartupMark, "   oReq:", utils.GetJsonFromStruct(oReq))

	if oReq.Force == false {
		if instance.SaveImageId > 0 {
			var podImage model.PodImage
			if err := podImage.GetById(instance.SaveImageId); err != nil {
				logger.Error(err, " instanceUuid:", instance.Uuid)
				//ary[i].ImageStatusTxt = err.Error()
			} else {

				if podImage.AuditStatus == enums.ImageAuditStatusEnum.PushFail {
					msg = "该实例镜像保存失败，请等待后台处理，如不想保存，请勾选强制关机释放该实例"
					code = 1003
					logger.Error(msg, "instanceUuid:", instance.Uuid)
					return
				}

				if podImage.AuditStatus == enums.ImageAuditStatusEnum.Pushing || podImage.AuditStatus == enums.ImageAuditStatusEnum.Commiting {
					msg = "该实例镜像正在保存，关闭实例会导致镜像保存失败，如果一定要关闭该实例，请勾选强制关机"
					code = 1003
					logger.Error(msg, "instanceUuid:", instance.Uuid)
					return
				}
			}
		}
	}

	if oReq.ShutdownReason == "" {
		oReq.ShutdownReason = enums.ShutdownReasonEnum.Normal
	}

	if txt, err := service.InstanceNodeService.Shutdown(oReq.InstanceUuid, oReq.ShutdownReason, oReq.Destroy, oReq.Task); err != nil {
		msg = txt
		logger.Error(msg, err)
		return
	} else {
		msg = txt
	}
	code = 0
}

func (obj *_instanceApi) Destroy(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	var operatorLog model.OperationLog
	operatorShowMsg := "销毁"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("Destroy panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}

		ginH := gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		}
		if operatorLog.ID > 0 {
			if err := service.InstanceAccessService.UpdateInstanceAccess(operatorLog, ginH); err != nil {
				logger.Error(operatorShowMsg, " 更新实例关机日志失败 err:", err)
			}
		}
		c.JSON(http.StatusOK, ginH)
	}()
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceShutdownReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}
	logger.Info("接收到用户释放实例操作 oReq:", utils.GetJsonFromStruct(oReq))
	defer func() {
		logger.Info("接收到用户释放实例操作 oReq:", utils.GetJsonFromStruct(oReq), " code:", code, " msg:", msg, " result:", utils.GetJsonFromStruct(result))
	}()

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err)
		return
	}
	if operatorLog_, err := service.InstanceAccessService.SaveInstanceAccess(instance, claims.UserId, enums.OperatorUserTypeEnum.User, operatorShowMsg, oReq, c); err != nil {
		logger.Error("保存实例日志失败 uuid:", instance.Uuid, " showMsg:", operatorShowMsg, " err:", err)
	} else {
		operatorLog = operatorLog_
	}
	if instance.UserId != claims.UserId {
		msg = "无权限"
		return
	}
	logger.Info("执行释放实例操作 instanceUuid:", instance.Uuid, "   startupMark:", instance.StartupMark, "   oReq:", utils.GetJsonFromStruct(oReq))

	if txt, err := service.InstanceNodeService.Destroy(oReq.InstanceUuid); err != nil {
		msg = txt
		logger.Error(msg, err)
		return
	} else {
		msg = txt
		code = 0
	}
}

func (obj *_instanceApi) SetShutdownRegularTime(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	var operatorLog model.OperationLog
	operatorShowMsg := "设置定时关机"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("StartupLoop panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}

		ginH := gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		}
		if operatorLog.ID > 0 {
			if err := service.InstanceAccessService.UpdateInstanceAccess(operatorLog, ginH); err != nil {
				logger.Error(operatorShowMsg, " 更新实例关机日志失败 err:", err)
			}
		}
		c.JSON(http.StatusOK, ginH)
	}()
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceShutdownRegularTimeReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}
	regularTime := common.DefaultTime
	if oReq.Cancel == false {

		// 将时间转换为标准时区
		//ShanghaiLocal, err := time.LoadLocation("Asia/Shanghai")
		//if err != nil {
		//	fmt.Println("加载上海时区时发生错误:", err)
		//}

		if tmpTime, err := time.ParseInLocation(jsontime.TimeFormat, oReq.RegularTime, time.Local); err != nil {
			msg = "转换关机时间失败"
			logger.Error(msg, " ", err)
			return
		} else {
			fmt.Println("转换关机时间:", tmpTime)
			regularTime = tmpTime
		}
	} else {
		operatorShowMsg = "关闭定时关机"
	}

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err)
		return
	}
	if operatorLog_, err := service.InstanceAccessService.SaveInstanceAccess(instance, claims.UserId, enums.OperatorUserTypeEnum.User, operatorShowMsg, oReq, c); err != nil {
		logger.Error("保存实例日志失败 uuid:", instance.Uuid, " showMsg:", operatorShowMsg, " err:", err)
	} else {
		operatorLog = operatorLog_
	}
	if instance.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if txt, err := service.InstanceNodeService.SetShutdownRegularTime(oReq.InstanceUuid, regularTime, oReq.Cancel, oReq.Save); err != nil {
		msg = txt
		logger.Error(msg, err)
		return
	} else {
		msg = txt
		code = 0
	}
}

func (obj *_instanceApi) Restart(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	var operatorLog model.OperationLog
	operatorShowMsg := "重启"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("StartupLoop panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			ginH := gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			}
			if operatorLog.ID > 0 {
				if err := service.InstanceAccessService.UpdateInstanceAccess(operatorLog, ginH); err != nil {
					logger.Error(operatorShowMsg, " 更新实例关机日志失败 err:", err)
				}
			}
			c.JSON(http.StatusOK, ginH)
		}

	}()
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceStartupReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}
	logger.Info("接收到用户重启操作 oReq:", utils.GetJsonFromStruct(oReq))
	defer func() {
		logger.Info("接收到用户重启操作 oReq:", utils.GetJsonFromStruct(oReq), " Result:", utils.GetJsonFromStruct(result))
	}()

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err)
		return
	}

	if operatorLog_, err := service.InstanceAccessService.SaveInstanceAccess(instance, claims.UserId, enums.OperatorUserTypeEnum.User, operatorShowMsg, oReq, c); err != nil {
		logger.Error("保存实例日志失败 uuid:", instance.Uuid, " showMsg:", operatorShowMsg, " err:", err)
	} else {
		operatorLog = operatorLog_
	}

	if instance.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if instance.Status != enums.InstanceStatusEnum.Running {
		msg = "实例不在运行中，不能进行重启操作"
		return
	}

	//if ginH, err := service.NodeService.ReStartDocker(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark); err != nil {
	//	msg = "访问节点获取信息失败"
	//	logger.Error(err)
	//	result["err"] = err.Error()
	//	return
	//} else {
	//	code = 0
	//	c.JSON(http.StatusOK, ginH)
	//}

	if ginH, err := service.InstanceNodeService.ReStart(oReq.InstanceUuid); err != nil {
		msg = err.Error()
		logger.Error(err, " ginH:", ginH)
	} else {
		c.JSON(http.StatusOK, ginH)
		code = 0
		if operatorLog.ID > 0 {
			if err := service.InstanceAccessService.UpdateInstanceAccess(operatorLog, ginH); err != nil {
				logger.Error(operatorShowMsg, " 更新实例关机日志失败 err:", err)
			}
		}
	}
}

func (obj _instanceApi) Check(c *gin.Context) { //续费
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq checkShutdownReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err, oReq)
		return
	}

	var pod model.Pod
	if err := pod.GetById(instance.PodId); err != nil {
		msg = "获取POD信息失败"
		logger.Error(msg, err)
		return
	}

	if oReq.Action == tasklog.TaskEnum.SaveImageAndShutdown {
		resp := struct {
			Operate    string          `json:"operate"`
			ImageUuid  string          `json:"image_uuid"`
			ImageTitle string          `json:"image_title"`
			ImageTag   string          `json:"image_tag"`
			AddSize    int64           `json:"add_size"`
			FreeSize   int64           `json:"free_size"`
			LimitSize  int64           `json:"limit_size"`
			HourPrice  decimal.Decimal `json:"hour_price"`
		}{
			FreeSize:  common.ImageStoreFreeSize.IntPart(),
			LimitSize: common.ImageStoreLimitSize.IntPart(),
			HourPrice: common.ImageStoreHourPrice,
		}

		pre := ""
		var startupImage model.PodImage
		if err := startupImage.GetById(instance.StartupImageId); err != nil {
			msg = "查询启动镜像失败"
			logger.Error(pre, msg, err)
			return
		} else {
			if startupImage.ImageType == enums.ImageTypeEnum.Private {
				if startupImage.UserId != instance.UserId {
					msg = "分享镜像不能保存"
					return
				}
				resp.Operate = "覆盖"
				resp.ImageUuid = startupImage.Uuid
				resp.ImageTitle = startupImage.Title
				resp.ImageTag = startupImage.ImageTag
			} else {
				/*
					var privatePodImage model.PodImage
					if err := privatePodImage.GetPrivatePodImage(instance.UserId, instance.PodId, instance.ImageTag); err != nil {
						if err != gorm.ErrRecordNotFound {
							msg = "查询个人镜像失败"
							logger.Error(pre, msg, err)
							return
						} else {
							resp.Operate = "创建"
							resp.ImageTitle = startupImage.Title
							resp.ImageTag = startupImage.ImageTag
						}
					} else {
						resp.Operate = "覆盖"
						resp.ImageUuid = privatePodImage.Uuid
						resp.ImageTitle = privatePodImage.Title
						resp.ImageTag = privatePodImage.ImageTag
					}*/

				newTag := service.GenPrivateImageTag(instance.UserId, startupImage.PodId, startupImage.ImageTag)
				resp.Operate = "创建"
				resp.ImageTitle = startupImage.Title
				resp.ImageTag = newTag
			}
		}

		if container, err := service.NodeService.ContainerInfo(instance.StartupMark); err != nil {
			msg = "获取容器信息失败"
			logger.Error(pre, msg, err, instance.StartupMark)
		} else {
			if resp.Operate == "创建" {
				resp.AddSize = container.SizeRootFs
			} else {
				resp.AddSize = container.SizeRw
			}
		}

		if resp.ImageTitle == "" {
			resp.ImageTitle = pod.Title
		}
		result["resp"] = resp
		msg = fmt.Sprintf("本次保存将%sPOD镜像%s[%s]", resp.Operate, resp.ImageTitle, resp.ImageTag)
		code = 0
	} else {
		msg = "参数不存在"
		return
	}
}

func (obj *_instanceApi) SaveImage(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	var operatorLog model.OperationLog
	operatorShowMsg := "镜像另存为"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("StartupLoop panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			ginH := gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			}
			if operatorLog.ID > 0 {
				if err := service.InstanceAccessService.UpdateInstanceAccess(operatorLog, ginH); err != nil {
					logger.Error(operatorShowMsg, " 更新实例关机日志失败 err:", err)
				}
			}
			c.JSON(http.StatusOK, ginH)
		}

	}()
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq instanceSaveImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}
	logger.Info("接收到用户镜像保存操作 oReq:", utils.GetJsonFromStruct(oReq))
	defer func() {
		logger.Info("接收到用户镜像保存操作 oReq:", utils.GetJsonFromStruct(oReq), " Result:", utils.GetJsonFromStruct(result))
	}()

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err)
		return
	}

	if operatorLog_, err := service.InstanceAccessService.SaveInstanceAccess(instance, claims.UserId, enums.OperatorUserTypeEnum.User, operatorShowMsg, oReq, c); err != nil {
		logger.Error("保存实例日志失败 uuid:", instance.Uuid, " showMsg:", operatorShowMsg, " err:", err)
	} else {
		operatorLog = operatorLog_
	}

	if instance.UserId != claims.UserId {
		msg = "无权限"
		return
	}
	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		logger.Error("查询用户信息失败 err:", err, " userId:", claims.UserId)
		msg = "查询用户信息失败"
		return
	}

	if service.OutOfAmount(claims.UserId) {
		msg = "账户已欠费，请先充值再使用该功能"
		logger.Error(msg, " userId:", user.ID, "  amount:", user.Amount)
		code = 5
		return
	}

	if instance.StartupMark == "" {
		msg = "启动标记为空，请将该实例开机后再做该操作"
		return
	}
	if _, err := service.NodeService.ContainerInfo(instance.StartupMark); err != nil {
		msg = "获取容器信息失败"
		if err == gorm.ErrRecordNotFound {
			msg = "该实例容器已释放，请开机生成容器再保存"
		}
		logger.Error(msg, err, instance.StartupMark)
		return
	} else {
		//if container.SizeRw > 20*common.SizeB2G {
		//	msg = "容器系统盘新增内容已经大于20G，请减少后再关机或者直接销毁"
		//	logger.Error(pre, msg)
		//	return msg, errors.New(msg)
		//}
	}

	if ginH, err := service.InstanceNodeService.SaveImage(oReq.InstanceUuid, oReq.ImageTitle, oReq.ImageTag, oReq.StorageMode, oReq.Shutdown); err != nil {
		msg = err.Error()
		logger.Error(err, " ginH:", ginH)
	} else {
		c.JSON(http.StatusOK, ginH)
		code = 0
		if operatorLog.ID > 0 {
			if err := service.InstanceAccessService.UpdateInstanceAccess(operatorLog, ginH); err != nil {
				logger.Error(operatorShowMsg, " 更新实例关机日志失败 err:", err)
			}
		}
	}
}

func (obj _instanceApi) SaveImageAbort(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	var operatorLog model.OperationLog
	operatorShowMsg := "取消镜像保存"
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		ginH := gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		}
		if operatorLog.ID > 0 {
			if err := service.InstanceAccessService.UpdateInstanceAccess(operatorLog, ginH); err != nil {
				logger.Error(operatorShowMsg, " 更新实例关机日志失败 err:", err)
			}
		}
		c.JSON(http.StatusOK, ginH)
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "查询用户信息失败"
		logger.Error(msg, err)
		return
	}

	var oReq saveImageCommitReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	logger.Info("接收到用户取消镜像保存操作 oReq:", utils.GetJsonFromStruct(oReq))
	defer func() {
		logger.Info("接收到用户取消镜像保存操作 oReq:", utils.GetJsonFromStruct(oReq), " Result:", utils.GetJsonFromStruct(result))
	}()

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "实例不存在"
		logger.Error(msg, err)
		return
	}
	if operatorLog_, err := service.InstanceAccessService.SaveInstanceAccess(instance, claims.UserId, enums.OperatorUserTypeEnum.User, operatorShowMsg, oReq, c); err != nil {
		logger.Error("保存实例日志失败 uuid:", instance.Uuid, " showMsg:", operatorShowMsg, " err:", err)
	} else {
		operatorLog = operatorLog_
	}

	var podImage model.PodImage
	if err := podImage.GetById(instance.SaveImageId); err != nil {
		msg = "查询镜像信息出错"
		logger.Error(msg, err)
		return
	}

	if podImage.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if podImage.AuditStatus != enums.ImageAuditStatusEnum.Pushing {
		msg = "镜像不在推送中"
		return
	}

	if ginH, err := service.NodeService.SaveImageDockerAbort(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark, instance.SaveImageId, false); err != nil {
		result["errInfo"] = err.Error()
	} else {
		result["errInfo"] = utils.GetJsonFromStruct(ginH)
	}

	//if tmpKey, err := GenLogKey(TaskEnum.PushDocker, fmt.Sprintf("%d", imageId)); err != nil {
	//	logger.Error(err)
	//	return false, err
	//} else {
	//	logKey = tmpKey
	//}

	if err := podImage.SetAuditStatus(enums.ImageAuditStatusEnum.PushFail); err != nil {
		msg = "设置状态失败"
		return
	} else {
		msg = "本次镜像保存已取消"
		code = 0
		return
	}

}

func (obj *_instanceApi) RestartFrpc(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq virtualInfoReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err, oReq.Host)
		msg = "参数获取失败"
		return
	}
	virtualNode := service.VirtualNode{
		Host: oReq.Host,
		Port: oReq.Port,
	}
	if oReq.VirtualId > 0 {
		var virtual model.Virtual
		if err := virtual.GetById(oReq.VirtualId); err != nil {
			logger.Error(fmt.Sprintf("virtual.GetById(%d)  ", oReq.VirtualId), err)
		} else {
			//hostPort = fmt.Sprintf("%s:%d", virtual.Host, virtual.Port)
			result["virtual_host"] = virtual.Host
			result["virtual_port"] = virtual.Port
			virtualNode = service.VirtualNode{
				Host:        virtual.Host,
				Port:        virtual.Port,
				SshUser:     virtual.SshUser,
				SshPassword: virtual.SshPassword,
			}
		}
	} else {
		msg = "请输入虚拟机ID"
		return
	}
	defer virtualNode.Close()
	if _, err := virtualNode.SshDial(); err != nil {
		logger.Error(err)
		msg = "ssh链接失败"
		return
	}
	if output, err := virtualNode.RestartFrpc(); err != nil {
		logger.Error(err)
		msg += err.Error()
	} else {
		result["restart_frpc"] = output
	}
	if msg == "" {
		code = 0
		msg = "success"
	}
}

func (obj *_instanceApi) Hidden(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}
	var oReq instanceReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}

	lockKey := enums.RedisKeyEnum.LockKey + "instance_" + oReq.InstanceUuid
	if common.RedisLock(lockKey, 1, 0) {
		defer common.RedisUnLock(lockKey)
		var instance model.Instance
		if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
			msg = "获取实例信息失败"
			logger.Error(msg, err)
			return
		}

		if instance.UserId != claims.UserId {
			msg = "无权限"
			return
		}

		if instance.Status == enums.InstanceStatusEnum.Running {
			msg = "请关闭实例后再删除"
			logger.Error(msg, instance.Uuid)
			return
		}

		b := instance.Status == enums.InstanceStatusEnum.Created || instance.Status == enums.InstanceStatusEnum.ShutdownComplete
		if !b {
			msg = fmt.Sprintf("当前实例实例状态为[%s]，不能做删除操作", enums.InstanceStatusEnum.Name(instance.Status))
			logger.Error(msg, instance.ID)
			return
		}

		if msg1, err := service.InstanceNodeService.DestroyClear(oReq.InstanceUuid); err != nil {
			logger.Error(msg1, err, oReq.InstanceUuid)
		}

		//oldStatus := instance.Status
		if err := instance.SetStatus(enums.InstanceStatusEnum.Hidden); err != nil {
			msg = "删除实例失败"
			logger.Error(msg, err, instance.ID)
			return
		}

	} else {
		msg = "实例正在删除中"
	}
	msg = "删除成功"
	code = 0
}

func (obj *_instanceApi) SetAction(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})

	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}
	var oReq instanceActionReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error("参数获取失败", err)
		msg = "参数获取失败"
		return
	}

	var instance model.Instance
	if err := instance.GetByUuid(oReq.InstanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err)
		return
	}

	if instance.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if oReq.Action == "SetTitle" {
		if oReq.Title == "" {
			msg = "请输入标题"
			return
		}
		if err := instance.SetTitle(oReq.Title); err != nil {
			msg = "设置失败"
			logger.Error(msg, err)
			return
		} else {
			msg = "设置成功"
			code = 0
		}
	}
}

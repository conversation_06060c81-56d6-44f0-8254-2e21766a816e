package controller

import (
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/model"
	"github.com/gin-gonic/gin"
	"net/http"
)

type labelApi_ struct {
}

var LabelApi labelApi_

type listLabelReq struct {
	LabelId  uint   `json:"label_id"`
	ParentId uint   `json:"parent_id"`
	Title    string `json:"title"`
}

type labelResp struct {
	Id         uint    `json:"id"`
	ParentId   uint    `json:"parent_id"`
	Title      string  `json:"title"`
	OrderIndex float64 `json:"order_index"`
}

type selectResp struct {
	labelResp
	Childs []*selectResp `json:"childs"`
}

func (obj labelApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.<PERSON>(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq listLabelReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var label model.Label
	ary := make([]labelResp, 0)
	queryParm := make(map[string]interface{})
	if oReq.ParentId > 0 {
		queryParm["parent_id"] = oReq.ParentId
	}

	if _, err := label.List(&ary, queryParm, 1, 100); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		result["labels"] = ary
		code = 0
	}
}

/*
func (obj labelApi_) Select(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq listLabelReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var label model.Label
	ary := make([]labelResp, 0)
	queryParm := make(map[string]interface{})
	if oReq.ParentId > 0 {
		queryParm["parent_id"] = oReq.ParentId
	}
	queryParm["status"] = 1

	if _, err := label.List(&ary, queryParm, 1, 1000); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {

		// 创建一个map来存储所有的selectResp
		aryMap := make(map[uint]*selectResp)
		for _, item := range ary {
			aryMap[item.Id] = &selectResp{
				labelResp: item,
				Childs:    []selectResp{},
			}
		}

		respAry := make([]selectResp, 0)

		// 重新组织数据
		for _, item := range ary {
			if item.ParentId > 0 {
				parent, exists := aryMap[item.ParentId]
				if exists {
					parent.Childs = append(parent.Childs, *aryMap[item.Id])
				}
			}
		}

		for _, item := range ary {
			if item.ParentId == 0 {
				respAry = append(respAry, *aryMap[item.Id])
			}
		}

		// 对respAry按Item.Index排序
		sort.Slice(respAry, func(i, j int) bool {
			return respAry[i].OrderIndex > respAry[j].OrderIndex
		})

		// 对每个selectResp的Childs按Index排序
		for _, resp := range respAry {
			sort.Slice(resp.Childs, func(i, j int) bool {
				return resp.Childs[i].OrderIndex > resp.Childs[j].OrderIndex
			})
		}
		result["labels"] = respAry

		code = 0
	}
}
*/

func (obj labelApi_) Select(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var oReq listLabelReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var label model.Label
	ary := make([]labelResp, 0)
	queryParm := make(map[string]interface{})
	if oReq.ParentId > 0 {
		queryParm["parent_id"] = oReq.ParentId
	}
	queryParm["status"] = 1
	queryParm["order"] = "select"

	if _, err := label.List(&ary, queryParm, 1, 1000); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {

		// 创建一个map来存储所有的selectResp
		aryMap := make(map[uint]*selectResp)
		for _, item := range ary {
			aryMap[item.Id] = &selectResp{
				labelResp: item,
				Childs:    []*selectResp{},
			}
		}

		respAry := make([]*selectResp, 0)

		// 重新组织数据
		for _, item := range ary {
			if item.ParentId > 0 {
				if parent, ok := aryMap[item.ParentId]; ok {
					parent.Childs = append(parent.Childs, aryMap[item.Id])
				}
			}
		}

		for _, item := range ary {
			if item.ParentId == 0 {
				respAry = append(respAry, aryMap[item.Id])
			}
		}

		// 对respAry按Item.Index排序  不用排序了，从数据库拉取数据的时候已经排序过了
		//sort.Slice(respAry, func(i, j int) bool {
		//	return respAry[i].OrderIndex > respAry[j].OrderIndex
		//})

		// 对每个selectResp的Childs按Index排序
		//for _, resp := range respAry {
		//	sort.Slice(resp.Childs, func(i, j int) bool {
		//		return resp.Childs[i].OrderIndex > resp.Childs[j].OrderIndex
		//	})
		//}
		result["labels"] = respAry

		code = 0
	}
}

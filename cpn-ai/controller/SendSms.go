package controller

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"errors"
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
	"time"
)

type sendSmsReq struct {
	Token     string `json:"token"`
	Mobile    string `json:"mobile"`
	Mould     int    `json:"mould"`
	CodeId    string `json:"code_id"`
	CodeValue string `json:"code_value"`
}

func SendSms(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	var smsReq sendSmsReq

	if err := c.Should<PERSON>ind<PERSON>(&smsReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if strings.Contains(smsReq.Mobile, "**") {
		tokenHeader := c.Request.Header.Get("Authorization")
		claims, err := middleware.GetClaimsByToken(tokenHeader)
		if claims == nil || err != nil {
			logger.Error(err)
			msg = "参数错误"
			return
		}
		var user model.User
		if err := user.GetById(claims.UserId); err != nil {
			logger.Error("获取用户信息失败", claims.UserId)
			msg = "获取用户信息失败"
			return
		}
		if user.Mobile == "" {
			logger.Error("未绑定手机号码，请先绑定手机号码")
			msg = "未绑定手机号码，请先绑定手机号码"
			return
		}
		smsReq.Mobile = user.Mobile
	}

	if !utils.IsMobile(smsReq.Mobile) {
		logger.Error("smsReq.Mobile：", smsReq.Mobile, "  token：", smsReq.Token)
		msg = "手机号码不正确"
		return
	}

	if len(smsReq.CodeId) > 0 {
		if len(smsReq.CodeValue) < 5 {
			logger.Error(errors.New("验证码不正确"), smsReq.CodeValue)
			msg = "验证码不正确"
			return
		}
	}

	//if !verifycode.VerifyCaptcha(smsReq.CodeId, smsReq.CodeValue) {
	//	errmsg.Abort(c, errmsg.FAIL, "验证码不正确")
	//	return
	//}

	smsCode := utils.CreateCaptcha(4)

	redisKey := getRedisKey(smsReq.Mould, smsReq.Mobile)
	if len(redisKey) == 0 {
		logger.Error(errors.New("参数不正确"))
		msg = "参数不正确"
		return
	}

	if err := common.RedisSet(redisKey, smsCode, time.Minute*10); err != nil {
		logger.Error(err)
		msg = "短信验证码生成失败"
		return
	}

	if err := common.RedisSet(redisKey+":testcount", "0", time.Minute*10); err != nil {
		logger.Error(err)
		msg = "短信验证码生成失败"
		return
	}

	go func() {
		remark := "获取短信验证码"
		if smsReq.Mould == 2 {
			remark = "注册登录获取短信验证码"
		} else if smsReq.Mould == 3 {
			remark = "修改密码获取短信验证码"
		}

		if err := service.AliSms.SendSmsCode(smsReq.Mould, smsReq.Mobile, smsCode); err != nil {
			logger.Error(err)
			msg = "短信验证码发送失败"
			remark += "," + msg
			return
		} else {
			remark += "," + "短信验证码已经发送"
			logger.Info("短信验证码已经发送", smsReq, "  ", smsCode)
		}

		logStruct := struct {
			Mould     int       `json:"mould"`
			Mobile    string    `json:"mobile"`
			SmsCode   string    `json:"sms_code"`
			CreatedAt time.Time `json:"created_at"`
			Remark    string    `json:"remark"`
		}{
			Mobile:    smsReq.Mobile,
			SmsCode:   smsCode,
			Mould:     smsReq.Mould,
			CreatedAt: time.Now(),
			Remark:    remark,
		}
		userEnv := utils.MakeInterfaceMap()
		userEnv["user_agent"] = c.Request.UserAgent()

		logJson := utils.GetJsonFromStruct(logStruct)
		operationLog := model.OperationLog{
			LogType:   enums.OperationLogTypeEnum.DisCard,
			OrigWhere: enums.OperationOrigWhereEnum.Normal,
			OrigId:    0,
			Ip:        utils.GetClientIp(c.Request.Header),
			LogJson:   logJson,
			UserEnv:   utils.GetJsonFromStruct(userEnv),
		}
		if err := operationLog.Save(); err != nil {
			msg = "保存日志失败"
			logger.Error(msg, err)
		}

	}()
	msg = "验证码已发送"
	code = 0
}

func getRedisKey(mould int, mobile string) string {
	if mould == enums.SmsMouldEnum.Reg {
		return enums.RedisKeyEnum.SmsReg + mobile
	} else if mould == enums.SmsMouldEnum.Login {
		return enums.RedisKeyEnum.SmsLogin + mobile
	} else if mould == enums.SmsMouldEnum.ModifyPassword {
		return enums.RedisKeyEnum.SmsModifyPassword + mobile
	} else {
		return ""
	}
}

package controller

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"net/http"

	"github.com/gin-gonic/gin"
)

type teacherApi struct {
}

var TeacherApi teacherApi

type teacherReq struct {
	TeacherUuid string `json:"teacher_uuid"`
	InstitUuid  string `json:"instit_uuid"`
	SortBy      string `json:"sort_by"`
	Page        int    `json:"page"`
	PageSize    int    `json:"page_size"`
}

type teacherResp struct {
	ID         uint   `json:"-"`
	Uuid       string `json:"uuid"`
	InstitId   uint   `json:"-"`
	InstitUuid string `json:"instit_uuid"`
	Name       string `json:"name"`
	Intro      string `json:"intro"`
	Avatar     string `json:"avatar"`

	Contact       string `json:"contact"`
	ContactDetail string `json:"contact_detail"`

	Position string            `json:"position"`
	Video    string            `json:"video"`
	Links    model.SocialLinks `json:"links"`
	Detail   string            `json:"detail"`
	// Status    int               `json:"status"`
	// StatusTxt string            `json:"status_txt"`
	CreatedAt jsontime.JsonTime `json:"created_at"`
	UpdatedAt jsontime.JsonTime `json:"updated_at"`
}

func (obj teacherApi) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(model.M)
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var req teacherReq
	if err := c.ShouldBindJSON(&req); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 100
	}

	if req.SortBy == "" {
		req.SortBy = "priority"
	}
	if req.SortBy != "priority" && req.SortBy != "id" {
		msg = "排序不正确"
		return
	}

	queryParams := make(model.M)
	queryParams["status"] = 2

	var instit model.Instit
	if req.InstitUuid != "" {
		if err := instit.GetByUuid(req.InstitUuid); err != nil {
			msg = "记录不存在"
			logger.Error(msg, err, req)
			return
		}
		queryParams["instit_id"] = instit.ID
	}

	var teacher model.Teacher
	if req.TeacherUuid != "" {
		if err := teacher.GetByUuid(req.TeacherUuid); err != nil {
			msg = "记录不存在"
			logger.Error(msg, err, req)
			return
		}
		queryParams["id"] = teacher.ID
	}

	var rows = make([]teacherResp, 0)
	if total, err := teacher.List(&rows, queryParams, req.SortBy, req.Page, req.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		if len(rows) > 0 {
			a := make([]uint, len(rows))

			for i := 0; i < len(rows); i++ {
				a[i] = rows[i].InstitId

				rows[i].Avatar = model.InstitAvatarUrl(rows[i].Avatar)
				rows[i].Contact = model.InstitContactUrl(rows[i].Contact)

				rows[i].Video = model.InstitVideoUrl(rows[i].Video)
				for j := range rows[i].Links {
					rows[i].Links[j].Icon = model.InstitLinkUrl(rows[i].Links[j].Icon)
				}

				// rows[i].StatusTxt = enums.InstitStatusTxt[rows[i].Status]
			}

			m, err := instit.Id2Uuid(a)
			if err != nil {
				msg = "查询失败"
				logger.Error(msg, err)
				return
			}

			for i := 0; i < len(rows); i++ {
				rows[i].InstitUuid = m[rows[i].InstitId]
			}
		}

		result["data"] = rows
		result["total"] = total
	}

	msg = ""
	code = 0
}

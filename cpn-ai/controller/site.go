package controller

import (
	"cpn-ai/common"
	"cpn-ai/common/logger"
	"github.com/gin-gonic/gin"
	"net/http"
	"runtime"
	"time"
)

type siteApi_ struct {
}

var SiteApi siteApi_

type testReq struct {
	Action string `json:"action"`
}

func (obj siteApi_) Time(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.J<PERSON>(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	result["cur_unix_milli"] = time.Now().UnixMilli()
	result["version"] = common.Version
	msg = ""
	code = 0
}

func (obj siteApi_) Conf(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	result["cur_unix_milli"] = time.Now().UnixMilli()
	result["version"] = common.Version
	msg = ""
	code = 0
}

func (obj siteApi_) Test(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	//claims := c.Value("claims").(*middleware.MyClaims)
	//if claims == nil {
	//	msg = "请先登录"
	//	code = 2
	//	return
	//}

	result["cur_unix_milli"] = time.Now().UnixMilli()
	result["version"] = common.Version
	msg = ""
	code = 0
}

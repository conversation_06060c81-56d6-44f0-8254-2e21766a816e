package kol

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"cpn-ai/service/tasklog"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type podImageApi_ struct {
}

var PodImageApi podImageApi_

type podImageReq struct {
	ImageUuid string `json:"image_uuid"`
	PodUuid   string `json:"pod_uuid"`
	//ImageType int    `json:"image_type"`
	//ImageName string `json:"image_name"`
	Status int `json:"status"`
	//Kw        string `json:"kw"`
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

type podImageCommitReq struct {
	ImageUuid string `json:"image_uuid"`
	PodUuid   string `json:"pod_uuid"`
	ImageTag  string `json:"image_tag"`
	Remark    string `json:"remark"`
	Force     bool   `json:"force"`
}

type podImageResp struct {
	ID        uint   `json:"-"`
	Uuid      string `json:"uuid"`
	Title     string `json:"title"`
	PodTitle  string `json:"pod_title"`
	PodId     uint   `json:"-"`
	PodUuid   string `json:"pod_uuid"`
	ImageName string `json:"image_name"`
	ImageTag  string `json:"image_tag"`

	LayerCount int `json:"layer_count"`
	Squash     int `json:"squash"` //0 1should 2must

	Size           float64           `json:"size"`
	Reason         string            `json:"reason"`
	Remark         string            `json:"remark"`
	Status         int               `json:"status"`
	StatusTxt      string            `json:"status_txt"`
	AuditStatus    int               `json:"audit_status"`
	AuditStatusTxt string            `json:"audit_status_txt"`
	StartupTxt     string            `json:"startup_txt"`
	StartupLogTime jsontime.JsonTime `json:"startup_log_time"`
	StartupMarkLog []string          `json:"startup_mark_log"`
	//TaskTxt         string            `json:"task_txt,omitempty"`
	//TaskLastTime    jsontime.JsonTime `json:"task_last_time,omitempty"`
	//TaskPercent     float64           `json:"task_percent,omitempty"`
	//TaskLog         []string          `json:"task_log,omitempty"`
	CommitStartTime jsontime.JsonTime `json:"commit_start_time"`
	LastSaveTime    jsontime.JsonTime `json:"last_save_time"`
	LastUseTime     jsontime.JsonTime `json:"last_use_time"`
	CreatedAt       jsontime.JsonTime `json:"created_at"`
	UpdatedAt       jsontime.JsonTime `json:"updated_at"`
}

type podImageSelect struct {
	ImageTitle string `json:"image_title"`

	ImageTags []map[string]interface{} `json:"image_tags"`
}

func (obj podImageApi_) CommitLocal(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podImageCommitReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	oReq.ImageTag = strings.Trim(oReq.ImageTag, " ")
	if oReq.ImageTag == "" {
		msg = "请输入版本号"
		return
	}

	oReq.ImageTag = strings.TrimSpace(oReq.ImageTag)
	if strings.Contains(oReq.ImageTag, " ") {
		msg = "版本号不能带空格"
		return
	}
	if utils.IsVersion(oReq.ImageTag) == false {
		msg = "版本号必须以数字字母.和-组成"
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(oReq.PodUuid); err != nil {
		msg = "查询Pod信息出错"
		logger.Error(msg, err)
		return
	}
	if pod.UserId != claims.UserId {
		msg = "无权限"
		return
	}
	if pod.InstanceUuid == "" {
		msg = "该Pod未启动实例"
		logger.Error(msg, pod.Uuid)
		return
	}

	var instance model.Instance
	if err := instance.GetByUuid(pod.InstanceUuid); err != nil {
		msg = "获取实例信息失败"
		logger.Error(msg, err)
		return
	}

	lockKey := enums.RedisKeyEnum.LockKey + "instance_" + pod.InstanceUuid
	if common.RedisLock(lockKey, 1, 1000*60) {
		defer common.RedisUnLock(lockKey)

		var podImage model.PodImage

		var unCompleteImage model.PodImage
		var ary = make([]model.PodImage, 0)
		if total, err := podImage.List(&ary, 0, claims.UserId, enums.ImageTypeEnum.Public, pod.ID, "", -1, 1, 100); err != nil {
			msg = "查询失败"
			logger.Error(msg, err)
			return
		} else {
			if total > 10 {
				msg = "最多只能有10个版本，请先删除不常用的版本"
			}
			for _, tmp := range ary {
				if tmp.AuditStatus != enums.ImageAuditStatusEnum.AuditPass {
					unCompleteImage = tmp
				}
			}
		}

		if err := podImage.GetByPodIdAndTag(pod.ID, oReq.ImageTag); err != nil {
			if err != gorm.ErrRecordNotFound {
				msg = "查询镜像信息出错"
				logger.Error(msg, err)
				return
			}
		}

		if unCompleteImage.ID > 0 && podImage.ID != unCompleteImage.ID {
			msg = fmt.Sprintf("版本[%s]正在编辑中，请输入该版本号覆盖该版本或者删除该版本后再提交", unCompleteImage.ImageTag)
			logger.Error(msg, oReq)
			return
		}

		bExists := false
		if podImage.ID == 0 {
			podImage = model.PodImage{
				Uuid:              utils.GetUUID(),
				UserId:            pod.UserId,
				ImageType:         enums.ImageTypeEnum.Public,
				PodId:             pod.ID,
				PodUuid:           pod.Uuid,
				ImageName:         pod.Uuid,
				ImageTag:          oReq.ImageTag,
				Remark:            oReq.Remark,
				AuditStatus:       enums.ImageAuditStatusEnum.Makeing,
				CommitStartupMark: instance.StartupMark,
				CommitVirtualId:   instance.StartupVirtualId,
			}
			if err := podImage.Save(); err != nil {
				msg = "保存镜像信息失败"
				return
			}
		} else {
			if podImage.Status == 9 {
				msg = "该版本号已经使用过，请重新输入版本号"
				return
			}
			bExists = true
			if oReq.Remark != "" && oReq.Remark != podImage.Remark {
				if err := podImage.SetRemark(oReq.Remark); err != nil {
					msg = "保存镜像备注信息失败"
					return
				}
			}
		}
		if podImage.ID == 0 {
			msg = "获取镜像信息失败"
			return
		}

		if podImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass {
			msg = "该版本已审核完成，请输入新的版本号"
			return
		}

		if podImage.AuditStatus == enums.ImageAuditStatusEnum.Commiting {

			checkTime := podImage.CommitStartTime
			duration := time.Now().Sub(checkTime)
			minutes := int(duration.Minutes())
			if minutes < 5 {
				seconds := int(duration.Seconds()) % 60
				msg = fmt.Sprintf("镜像提交中，请在%d分%d秒后再操作", minutes, seconds)
				return
			}
		}

		if podImage.AuditStatus == enums.ImageAuditStatusEnum.Pushing {
			msg = "镜像推送中，请勿重复操作"
			return
		}

		if err := podImage.SetAuditStatus(enums.ImageAuditStatusEnum.Commiting); err != nil {
			msg = "设置镜像为提交状态失败"
			logger.Error(msg, err)
			return
		}

		userPath := ""
		if instance.UserId == 2 {
			var user model.User
			if err := user.GetById(instance.UserId); err != nil {
				msg = "查询用户信息失败"
				return
			}
			userPath = user.PrivateStorage
		}
		if ginH, err := service.NodeService.CommitDocker(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark, podImage.ID, userPath); err != nil {
			msg = "发送保存指令失败"
			logger.Error(err)
			result["err"] = err.Error()
			return
		} else {
			if tmpH, err := service.ResultGinH(ginH); err != nil {
				msg = "解析出错"
				result["image_uuid"] = podImage.Uuid
			} else {
				code = tmpH.Code
				msg = tmpH.Msg
				result["image_uuid"] = podImage.Uuid
				result["exists"] = bExists
			}
			return
		}

		/*
			go func() {
				if bSaved, mstr, err := service.NodeService.SaveImageDocker(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark, podImage.ID); err != nil {
					msg = mstr
					logger.Error("调用节点保存镜像接口出错", podImage.ID)
					if err := podImage.SetAuditStatus(enums.ImageAuditStatusEnum.PushFail); err != nil {
						msg = "设置推送状态失败"
						logger.Error(msg, err)
						return
					}
					return
				} else {
					logger.Error("镜像保存成功 imageID:", podImage.ID)
					if podImage.ID > 0 {
						code = 0
						logger.Error("镜像保存成功 直接返回不处理 imageID:", podImage.ID)
						return
					}
					if bSaved {
						logger.Info("开始获取镜像信息 imageID:", podImage.ID)
						var hub structs.HubRepositorie
						if err := service.GetHubImage(podImage.ImageType, podImage.ImageName, podImage.ImageTag, &hub); err != nil {
							logger.Error(err)
						}
						if hub.Size > 0 {
							mm := make(map[string]interface{})
							mm["size"] = hub.Size
							mm["sha256"] = hub.Digest
							mm["last_save_time"] = hub.PushTime
							mm["audit_status"] = enums.ImageAuditStatusEnum.PushSuccess
							mm["status"] = 1
							if err := podImage.Updates(mm); err != nil {
								msg = "保存镜像信息失败"
								logger.Error(msg, err)
								return
							}
						} else {
							logger.Info("开始设置镜像保存时间 imageID:", podImage.ID)
							if err := podImage.SetLastSaveTime(); err != nil {
								msg = "设置镜像保存时间出错"
								logger.Error(msg, err)
								return
							}
							if len(mstr) == 64 {
								logger.Info("开始设置Sha256 imageID:", podImage.ID)
								if err := podImage.SetSha256("sha256:" + mstr); err != nil {
									msg = "设置镜像哈希值出错"
									logger.Error(msg, err)
									return
								}
							}
						}
						logger.Info("开始设置实例的 imageID:", podImage.ID)
						if err := instance.SetSaveImageId(podImage.ID); err != nil {
							msg = "保存镜像ID出错"
							logger.Error(msg, err)
							return
						}

						if pod.AuditStatus == enums.PodAuditStatusEnum.AuditPass {
							if err := pod.SetAuditStatus(enums.PodAuditStatusEnum.Makeing); err != nil {
								msg = "设置Pod状态失败"
								logger.Error(msg, err)
							}
						}

						//var podTmp model.Pod
						//if err := podTmp.GetByUuid(oReq.PodUuid); err != nil {
						//	msg = "查询Pod信息出错"
						//	logger.Error(msg, err)
						//	return
						//}

						msg = mstr
						msg = "镜像提交成功"
						if bExists {
							msg = "版本号存在，已更新成功"
						}
						code = 0
						return
					} else {
						msg = mstr
						logger.Error(msg)
						if err := podImage.SetAuditStatus(enums.ImageAuditStatusEnum.PushFail); err != nil {
							msg = "设置推送状态失败"
							logger.Error(msg, err)
							return
						}
						return
					}
				}
			}()*/
		//result["image_uuid"] = podImage.Uuid
		//msg = "推送中"
		//code = 0
	} else {
		msg = "正在保存中，请无重复提交"
		return
	}

}

func (obj podImageApi_) CommitPush(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	userId := claims.UserId

	var oReq podImageCommitReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	oReq.ImageTag = strings.Trim(oReq.ImageTag, " ")
	if oReq.ImageTag == "" {
		msg = "请输入版本号"
		return
	}

	oReq.ImageTag = strings.TrimSpace(oReq.ImageTag)
	if strings.Contains(oReq.ImageTag, " ") {
		msg = "版本号不能带空格"
		return
	}
	if utils.IsVersion(oReq.ImageTag) == false {
		msg = "版本号必须以数字字母.和-组成"
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(oReq.PodUuid); err != nil {
		msg = "查询Pod信息出错"
		logger.Error(msg, err)
		return
	}
	if pod.UserId != userId {
		msg = "无权限"
		return
	}
	if pod.InstanceUuid == "" {
		msg = "该Pod未启动实例"
		logger.Error(msg, pod.Uuid)
		return
	}

	lockKey := enums.RedisKeyEnum.LockKey + "instance_" + pod.InstanceUuid
	if common.RedisLock(lockKey, 1, 1000*60) {
		defer common.RedisUnLock(lockKey)

		var podImage model.PodImage

		var unCompleteImage model.PodImage
		var ary = make([]model.PodImage, 0)
		if total, err := podImage.List(&ary, 0, userId, enums.ImageTypeEnum.Public, pod.ID, "", -1, 1, 100); err != nil {
			msg = "查询失败"
			logger.Error(msg, err)
			return
		} else {
			if total > 10 {
				msg = "最多只能有10个版本，请先删除不常用的版本"
			}
			for _, tmp := range ary {
				if tmp.AuditStatus != enums.ImageAuditStatusEnum.AuditPass {
					unCompleteImage = tmp
				}
			}
		}

		if err := podImage.GetByPodIdAndTag(pod.ID, oReq.ImageTag); err != nil {
			if err != gorm.ErrRecordNotFound {
				msg = "查询镜像信息出错"
				logger.Error(msg, err)
				return
			}
		}

		if unCompleteImage.ID > 0 && podImage.ID != unCompleteImage.ID {
			msg = fmt.Sprintf("版本[%s]正在编辑中，请输入该版本号覆盖该版本或者删除该版本后再提交", unCompleteImage.ImageTag)
			logger.Error(msg, oReq)
			return
		}

		var instance model.Instance
		if err := instance.GetByUuid(pod.InstanceUuid); err != nil {
			msg = "获取实例信息失败"
			logger.Error(msg, err)
			return
		}

		var instImage model.PodImage
		if err := instImage.GetById(instance.ImageId); err != nil {
			msg = "查询实例镜像失败"
			logger.Error(msg, err, " instance.ImageId", instance.ImageId)
		}

		bExists := false
		if podImage.ID == 0 {
			parentId := uint(0)
			if instImage.ImageType == enums.ImageTypeEnum.Base || instImage.ImageType == enums.ImageTypeEnum.Public || instImage.ImageType == enums.ImageTypeEnum.CCM {
				parentId = instImage.ID
			}
			podImage = model.PodImage{
				Uuid:        utils.GetUUID(),
				ParentId:    parentId,
				UserId:      pod.UserId,
				ImageType:   enums.ImageTypeEnum.Public,
				PodId:       pod.ID,
				PodUuid:     pod.Uuid,
				ImageName:   pod.Uuid,
				ImageTag:    oReq.ImageTag,
				Remark:      oReq.Remark,
				AuditStatus: enums.ImageAuditStatusEnum.Makeing,
				StorageMode: enums.ImageStorageModeEnum.Registry,
			}
			if err := podImage.Save(); err != nil {
				msg = "保存镜像信息失败"
				return
			}
		} else {
			if podImage.Status == 9 {
				msg = "该版本号已经使用过，请重新输入版本号"
				return
			}
			bExists = true
			if oReq.Remark != "" && oReq.Remark != podImage.Remark {
				if err := podImage.SetRemark(oReq.Remark); err != nil {
					msg = "保存镜像备注信息失败"
					return
				}
			}
		}
		if podImage.ID == 0 {
			msg = "获取镜像信息失败"
			return
		}

		if podImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass {
			msg = "该版本已审核完成，请输入新的版本号"
			return
		}

		if podImage.AuditStatus == enums.ImageAuditStatusEnum.Pushing {
			msg = "推送中，请勿重复操作"
			return
		}

		if err := instance.SetSaveImageId(podImage.ID); err != nil {
			msg = "修改保存镜像ID出错"
			logger.Error(msg, err)
			return
		}

		//if err := podImage.SetAuditStatus(enums.ImageAuditStatusEnum.Pushing); err != nil {
		//	msg = "设置推送状态失败"
		//	logger.Error(msg, err)
		//	return
		//}

		if err := podImage.SetAuditStatusToPushing(instance.StartupMark, instance.StartupVirtualId, instance.ID); err != nil {
			msg = "设置推送状态失败"
			logger.Error(msg, err)
			return
		}

		if ginH, err := service.NodeService.SaveImageDockerKol(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark, podImage.ID); err != nil {
			msg = "发送保存指令失败"
			logger.Error(err)
			result["err"] = err.Error()
			return
		} else {
			if tmpH, err := service.ResultGinH(ginH); err != nil {
				msg = "解析出错"
				result["image_uuid"] = podImage.Uuid
			} else {
				code = tmpH.Code
				msg = tmpH.Msg
				result["image_uuid"] = podImage.Uuid
				result["exists"] = bExists
			}
			return
		}
	} else {
		msg = "正在保存中，请无重复提交"
		return
	}

}

func (obj podImageApi_) CommitLogs(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "查询用户信息失败"
		logger.Error(msg, err)
		return
	}

	var oReq podImageCommitReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var podImage model.PodImage
	if oReq.ImageUuid != "" {
		if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
			msg = "查询镜像信息出错"
			logger.Error(msg, err)
			return
		}
	} else {
		msg = "请输入镜像参数"
		return
	}

	if podImage.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(podImage.ID)); err != nil {
		logger.Error(err)
		result["err"] = err.Error()
	} else {
		if ary, err := tasklog.List(logKey); err != nil {
			msg = err.Error()
			logger.Error(err)
			return
		} else {
			for i := 0; i < len(ary); i++ {
				var logItem tasklog.TaskLogItem
				if err := utils.GetStructFromJson(&logItem, ary[i]); err != nil {
					logger.Error(err)
				} else {
					logItem.Log = ""
					logItem.Data = nil
					ary[i] = utils.GetJsonFromStruct(logItem)
				}
			}
			code = 0
			msg = "completed"
			result["logs"] = ary
		}
	}
}

func (obj podImageApi_) CommitAbort(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "查询用户信息失败"
		logger.Error(msg, err)
		return
	}

	var oReq podImageCommitReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var podImage model.PodImage
	if oReq.ImageUuid != "" {
		if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
			msg = "查询镜像信息出错"
			logger.Error(msg, err)
			return
		}
	} else {
		msg = "请输入镜像参数"
		return
	}

	if podImage.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if podImage.AuditStatus != enums.ImageAuditStatusEnum.Pushing && podImage.AuditStatus != enums.ImageAuditStatusEnum.Commiting {
		msg = "镜像不在推送中"
		return
	}

	if oReq.Force == false {
		if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(podImage.ID)); err != nil {
			logger.Error(err)
			result["err"] = err.Error()
		} else {

			if logItem, _, err := tasklog.Last(logKey); err != nil {
				msg = err.Error()
				logger.Error(err)
				//return
			} else {
				if logItem.Time.Time().Before(time.Now().Add(-5 * time.Minute)) {

				} else {
					msg = "镜像还在推送中"
					return
				}
			}
		}
	}

	var pod model.Pod
	if err := pod.GetById(podImage.PodId); err != nil {
		msg = "未找到Pod"
		logger.Error(msg, err)
		return
	}
	var instance model.Instance
	if err := instance.GetByUuid(pod.InstanceUuid); err != nil {
		msg = "未找到Pod实例"
		logger.Error(msg, err)
	}

	if ginH, err := service.NodeService.SaveImageDockerAbort(instance.StartupNodeId, instance.StartupVirtualId, instance.StartupMark, instance.SaveImageId, false); err != nil {
		result["errInfo"] = err.Error()
	} else {
		result["errInfo"] = utils.GetJsonFromStruct(ginH)
	}

	if err := podImage.SetAuditStatus(enums.ImageAuditStatusEnum.PushFail); err != nil {
		msg = "设置状态失败"
		return
	} else {
		msg = "本次推送已设置为失败，您可以重新推送该镜像"
		code = 0
		return
	}

}

func (obj podImageApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var user model.User
	if err := user.GetById(claims.UserId); err != nil {
		msg = "查询用户信息失败"
		logger.Error(msg, err)
		return
	}

	var oReq podImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	oReq.Status = -1
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var pod model.Pod
	if oReq.PodUuid != "" {
		if err := pod.GetByUuid(oReq.PodUuid); err != nil {
			msg = "查询Pod信息出错"
			logger.Error(msg, err)
			return
		}
		if pod.UserId != claims.UserId {
			msg = "无权限"
			logger.Error(msg, oReq)
			return
		}
	}

	var podImage model.PodImage
	if oReq.ImageUuid != "" {
		if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
			msg = "查询镜像信息出错"
			logger.Error(msg, err)
			return
		}
	}

	var ary = make([]podImageResp, 0)
	if total, err := podImage.List(&ary, podImage.ID, claims.UserId, enums.ImageTypeEnum.Public, pod.ID, "", oReq.Status, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {
			if ary[i].PodId > 0 {
				var pod model.Pod
				if err := pod.GetById(ary[i].PodId); err != nil {
					logger.Error(err)
				} else {
					ary[i].PodTitle = pod.Title
				}
			}
			if service.IsSquashing(ary[i].Uuid) {
				ary[i].Squash = -1
			} else if ary[i].LayerCount > 120 {
				ary[i].Squash = 2
			} else if ary[i].LayerCount > 30 {
				ary[i].Squash = 1
			}

			if ary[i].Status == 1 {
				if ary[i].AuditStatus == enums.ImageAuditStatusEnum.AuditPass {
					ary[i].StatusTxt = "已上架"
				} else {
					ary[i].StatusTxt = "审核通过自动上架"
				}
			} else {
				ary[i].StatusTxt = "未上架"
			}

			ary[i].AuditStatusTxt = enums.ImageAuditStatusEnum.Name(ary[i].AuditStatus)
			if ary[i].AuditStatus == enums.ImageAuditStatusEnum.PushFail && ary[i].Reason != "" {
				ary[i].AuditStatusTxt += "(" + ary[i].Reason + ")"
			}

			if ary[i].AuditStatus == enums.ImageAuditStatusEnum.Pushing || ary[i].AuditStatus == enums.ImageAuditStatusEnum.Commiting || podImage.ID > 0 {

				if logKey, err := tasklog.GenLogKey(tasklog.TaskEnum.SaveImage, utils.Uint2String(ary[i].ID)); err != nil {
					logger.Error(err)
				} else {
					if item, _, err := tasklog.Last(logKey); err != nil {
						if err != gorm.ErrEmptySlice {
							logger.Error(err)
						}
					} else {

						ary[i].StartupTxt = item.Msg
						if item.Percent > 0 && item.Percent < 1 {
							ary[i].StartupTxt = fmt.Sprintf("%s%.2f%%", item.Msg, item.Percent*100)
						}
						ary[i].StartupLogTime = item.Time

						//ary[i].TaskTxt = item.Msg
						//ary[i].TaskLastTime = item.Time
						//ary[i].TaskPercent = item.Percent
					}
					//if user.Insider > 0 {
					//	ary[i].TaskLog, _ = tasklog.List(logKey)
					//}
				}
			}

			//if user.Insider > 0 {
			//	ary[i].StartupMarkLog, _ = service.StartupLog.List(logKey)
			//}
		}
		result["images"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj podImageApi_) Select(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	oReq.Status = -1
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	if oReq.PodUuid == "" {
		msg = "请输入PodId"
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(oReq.PodUuid); err != nil {
		msg = "查询Pod信息出错"
		logger.Error(msg, err)
		return
	}

	mPod := make(map[uint]model.Pod)
	if oReq.Page == 1 {
		var podImage model.PodImage
		var ary = make([]podImageResp, 0)
		if _, err := podImage.List(&ary, 0, claims.UserId, enums.ImageTypeEnum.Public, pod.ID, "", -1, 1, 100); err != nil {
			msg = "查询失败"
			logger.Error(msg, err)
			return
		} else {
			for i := 0; i < len(ary); i++ {
				//if ary[i].PodId > 0 {
				//	var pod model.Pod
				//	if err := pod.GetById(ary[i].PodId); err != nil {
				//		logger.Error(err)
				//	} else {
				//		ary[i].PodTitle = pod.Title
				//	}
				//}
				if ary[i].Status == 1 {
					ary[i].StatusTxt = "已上架"
				} else {
					ary[i].StatusTxt = "未上架"
				}
				ary[i].AuditStatusTxt = enums.ImageAuditStatusEnum.Name(ary[i].AuditStatus)
			}
			result["public_images"] = ary
		}
	}
	if oReq.Page == 1 {
		queryParm := make(map[string]interface{})
		queryParm["user_id"] = claims.UserId
		queryParm["image_type"] = enums.ImageTypeEnum.Private
		var podImagePrivate model.PodImage
		var aryPrivate = make([]podImageResp, 0)
		if _, err := podImagePrivate.ListPro(&aryPrivate, queryParm, 1, 100); err != nil {
			msg = "查询失败"
			logger.Error(msg, err)
			return
		} else {
			for i := 0; i < len(aryPrivate); i++ {
				if aryPrivate[i].Title == "" {
					if aryPrivate[i].PodId > 0 {
						if val, ok := mPod[aryPrivate[i].PodId]; ok {
							aryPrivate[i].Title = val.Title
						} else {
							var tmpPod model.Pod
							if err := tmpPod.GetById(aryPrivate[i].PodId); err != nil {

							} else {
								mPod[aryPrivate[i].PodId] = tmpPod
								aryPrivate[i].Title = tmpPod.Title
							}
						}
					}
				}
				if aryPrivate[i].Status == 1 {
					aryPrivate[i].StatusTxt = "已上架"
				} else {
					aryPrivate[i].StatusTxt = "未上架"
				}
				aryPrivate[i].AuditStatusTxt = enums.ImageAuditStatusEnum.Name(aryPrivate[i].AuditStatus)
			}
			result["private_images"] = aryPrivate
		}
	}

	var outAryPublic = make([]podImageResp, 0)
	if oReq.Page == 1 {
		ids := make([]uint, 0)
		key := enums.RedisKeyEnum.CpnSchedKolOutImage + utils.Uint2String(claims.UserId)
		if imageIds, err := common.RedisGet(key); err != nil {
			logger.Error("查询指定镜像失败", err)
		} else if imageIds != "" {
			ary := strings.Split(imageIds, "|")
			for _, imageIdStr := range ary {
				if imageIdStr == "" {
					continue
				}
				if imageId := utils.String2Uint(imageIdStr); imageId > 0 {
					ids = append(ids, imageId)
				}
			}
		}
		var aryPublic = make([]podImageResp, 0)
		if len(ids) > 0 {
			queryParm := make(map[string]interface{})
			queryParm["ids"] = ids
			queryParm["order"] = "pod_id desc, id desc"
			var podImagePrivate model.PodImage

			if _, err := podImagePrivate.ListPro(&aryPublic, queryParm, oReq.Page, oReq.PageSize); err != nil {
				msg = "查询失败"
				logger.Error(msg, err)
				return
			} else {
				for i := 0; i < len(aryPublic); i++ {
					if aryPublic[i].Title == "" {
						if aryPublic[i].PodId > 0 {
							if val, ok := mPod[aryPublic[i].PodId]; ok {
								aryPublic[i].Title = val.Title
							} else {
								var tmpPod model.Pod
								if err := tmpPod.GetById(aryPublic[i].PodId); err == nil {
									if tmpPod.Title == "" && tmpPod.AuditContent != "" {
										if mm := utils.GetMapFromJson(tmpPod.AuditContent); mm != nil {
											if _, ok := mm["title"]; ok {
												tmpPod.Title = mm["title"].(string)
											}
										}
									}
									mPod[aryPublic[i].PodId] = tmpPod
									aryPublic[i].Title = tmpPod.Title
								}
							}
						}
					}
					aryPublic[i].Title = "[外部]" + aryPublic[i].Title
					if aryPublic[i].Status == 1 {
						aryPublic[i].StatusTxt = "已上架"
					} else {
						aryPublic[i].StatusTxt = "未上架"
					}
					aryPublic[i].AuditStatusTxt = enums.ImageAuditStatusEnum.Name(aryPublic[i].AuditStatus)
				}
			}
		}

		outAryPublic = aryPublic
		result["out_images"] = aryPublic
	}

	{
		queryParm := make(map[string]interface{})
		queryParm["user_id"] = claims.UserId
		queryParm["image_type"] = enums.ImageTypeEnum.Public
		queryParm["order"] = "pod_id desc, id desc"
		var podImagePrivate model.PodImage
		var aryPublic = make([]podImageResp, 0)
		if _, err := podImagePrivate.ListPro(&aryPublic, queryParm, oReq.Page, oReq.PageSize); err != nil {
			msg = "查询失败"
			logger.Error(msg, err)
			return
		} else {
			for i := 0; i < len(aryPublic); i++ {
				if aryPublic[i].Title == "" {
					if aryPublic[i].PodId > 0 {
						if val, ok := mPod[aryPublic[i].PodId]; ok {
							aryPublic[i].Title = val.Title
						} else {
							var tmpPod model.Pod
							if err := tmpPod.GetById(aryPublic[i].PodId); err == nil {
								if tmpPod.Title == "" && tmpPod.AuditContent != "" {
									if mm := utils.GetMapFromJson(tmpPod.AuditContent); mm != nil {
										if _, ok := mm["title"]; ok {
											tmpPod.Title = mm["title"].(string)
										}
									}
								}
								mPod[aryPublic[i].PodId] = tmpPod
								aryPublic[i].Title = tmpPod.Title
							}
						}
					}
				}
				if aryPublic[i].Status == 1 {
					aryPublic[i].StatusTxt = "已上架"
				} else {
					aryPublic[i].StatusTxt = "未上架"
				}
				aryPublic[i].AuditStatusTxt = enums.ImageAuditStatusEnum.Name(aryPublic[i].AuditStatus)
			}
		}

		if len(outAryPublic) > 0 {
			aryPublic = append(outAryPublic, aryPublic...)
		}

		result["kol_images"] = aryPublic
	}

	if oReq.Page == 1 {
		var aryBase = make([]podImageResp, 0)
		var podImage model.PodImage
		if _, err := podImage.List(&aryBase, 0, 0, enums.ImageTypeEnum.Base, 0, "", 1, 1, 100); err != nil {
			msg = "查询失败"
			logger.Error(msg, err)
			return
		} else {
			for i := 0; i < len(aryBase); i++ {
				//if ary[i].PodId > 0 {
				//	var pod model.Pod
				//	if err := pod.GetById(ary[i].PodId); err != nil {
				//		logger.Error(err)
				//	} else {
				//		ary[i].PodTitle = pod.Title
				//	}
				//}
				if aryBase[i].Status == 1 {
					aryBase[i].StatusTxt = "已上架"
				} else {
					aryBase[i].StatusTxt = "未上架"
				}
			}
			result["base_images"] = aryBase
		}
	}

	msg = ""
	code = 0
}

func (obj podImageApi_) OnSale(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ImageUuid == "" {
		msg = "参数错误"
		logger.Error(msg, oReq)
		return
	}

	var podImage model.PodImage
	if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
		msg = "查询镜像信息出错"
		logger.Error(msg, err)
		return
	}
	if podImage.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if podImage.Status == 1 {
		msg = "已经是上架状态"
		return
	}

	if podImage.AuditStatus != enums.ImageAuditStatusEnum.AuditPass {
		msg = "不是审核通过的镜像，不能上架"
		return
	}

	if err := podImage.SetStatus(1); err != nil {
		msg = "上架失败"
		logger.Error(msg, err)
		return
	} else {
		msg = "上架成功"
		if podImage.PodId > 0 && podImage.ImageType == enums.ImageTypeEnum.Public {
			var pod model.Pod
			if err := pod.GetById(podImage.PodId); err == nil {
				queryParm := make(map[string]interface{})
				//queryParm["user_id"] = pod.UserId
				queryParm["image_type"] = enums.ImageTypeEnum.Public
				queryParm["pod_id"] = pod.ID
				queryParm["status"] = 1
				queryParm["audit_status"] = enums.ImageAuditStatusEnum.AuditPass
				var aryPass = make([]model.PodImage, 0)
				if _, err := podImage.ListPro(&aryPass, queryParm, 1, 100); err != nil {
					msg = "查询失败"
					logger.Error(msg, err)
					return
				} else {
					tags := ""
					for _, tmp := range aryPass {
						tags += tmp.ImageTag + " "
					}
					tags = strings.Trim(tags, " ")
					if err := pod.SetImageTags(tags); err != nil {
						logger.Error(err)
						msg += "，更新发布镜像失败"
					}
				}
			}
		}
		code = 0
		return
	}
}

func (obj podImageApi_) OffSale(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}
	var oReq podImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ImageUuid == "" {
		msg = "参数错误"
		logger.Error(msg, oReq)
		return
	}

	var podImage model.PodImage
	if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
		msg = "查询镜像信息出错"
		logger.Error(msg, err)
		return
	}
	if podImage.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	if podImage.Status == 0 {
		msg = "已经是下架状态"
		return
	}

	if err := podImage.SetStatus(0); err != nil {
		msg = "下架失败"
		logger.Error(msg, err)
		return
	} else {
		msg = "下架成功"
		if podImage.PodId > 0 && podImage.ImageType == enums.ImageTypeEnum.Public {
			var pod model.Pod
			if err := pod.GetById(podImage.PodId); err == nil {
				queryParm := make(map[string]interface{})
				//queryParm["user_id"] = pod.UserId
				queryParm["image_type"] = enums.ImageTypeEnum.Public
				queryParm["pod_id"] = pod.ID
				queryParm["status"] = 1
				queryParm["audit_status"] = enums.ImageAuditStatusEnum.AuditPass
				var aryPass = make([]model.PodImage, 0)
				if _, err := podImage.ListPro(&aryPass, queryParm, 1, 100); err != nil {
					msg = "查询失败"
					logger.Error(msg, err)
					return
				} else {
					tags := ""
					for _, tmp := range aryPass {
						tags += tmp.ImageTag + " "
					}
					tags = strings.Trim(tags, " ")
					if err := pod.SetImageTags(tags); err != nil {
						logger.Error(err)
						msg += "，更新发布镜像失败"
					}
				}
			}
		}

		code = 0

		return
	}
}

func (obj podImageApi_) Delete(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq podImageReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.ImageUuid == "" {
		msg = "参数错误"
		logger.Error(msg, oReq)
		return
	}

	var podImage model.PodImage
	if err := podImage.GetByUuid(oReq.ImageUuid); err != nil {
		msg = "查询镜像信息出错"
		logger.Error(msg, err)
		return
	}
	if podImage.UserId != claims.UserId {
		msg = "无权限"
		return
	}

	var pod model.Pod
	if err := pod.GetByUuid(podImage.PodUuid); err != nil {
		msg = "Pod信息获取失败"
		logger.Error(msg, err, podImage.PodUuid)
		return
	}
	if podImage.AuditStatus == enums.ImageAuditStatusEnum.Pushing {
		msg = "镜像上传中，不能做删除操作"
		return
	}

	if podImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass {
		if pod.ImageTags == "" || pod.ImageTags == podImage.ImageTag {
			if pod.Status == 1 {
				msg = "这是该Pod唯一审核通过的版本，请先下架该Pod再删除"
				return
			}
		}
	}

	if podImage.LastSaveTime.After(common.DefaultTime) {
		msg = "需要删除镜像文件"
		if err := service.DeleteHubImage(podImage.ImageType, podImage.ImageName, podImage.ImageTag, podImage.Sha256); err != nil {
			msg = "删除镜像文件失败"
			logger.Error(msg, err, utils.GetJsonFromStruct(oReq))
			return
		}
	}

	if err := podImage.SetDelete(); err != nil {
		msg = "删除失败"
		logger.Error(msg, err, oReq)
		return
	}

	if podImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass {
		queryParm := make(map[string]interface{})
		//queryParm["user_id"] = pod.UserId
		queryParm["image_type"] = enums.ImageTypeEnum.Public
		queryParm["pod_id"] = pod.ID
		queryParm["status"] = 1
		queryParm["audit_status"] = enums.ImageAuditStatusEnum.AuditPass
		var aryPass = make([]model.PodImage, 0)
		if _, err := podImage.ListPro(&aryPass, queryParm, 1, 100); err != nil {
			msg = "查询失败"
			logger.Error(msg, err)
			return
		} else {
			tags := ""
			for _, tmp := range aryPass {
				tags += tmp.ImageTag + " "
			}
			tags = strings.Trim(tags, " ")
			if err := pod.SetImageTags(tags); err != nil {
				logger.Error(err)
				msg += "，更新发布镜像失败"
			}
		}
	}
	//if podImage.AuditStatus == enums.ImageAuditStatusEnum.AuditPass {
	//	queryParm := make(map[string]interface{})
	//	queryParm["user_id"] = podImage.UserId
	//	queryParm["pod_id"] = podImage.PodId
	//	queryParm["audit_status"] = enums.ImageAuditStatusEnum.AuditPass
	//	var aryPass = make([]model.PodImage, 0)
	//	if _, err := podImage.ListPro(&aryPass, queryParm, 1, 100); err != nil {
	//		msg = "查询失败"
	//		logger.Error(msg, err)
	//		return
	//	} else {
	//		tags := ""
	//		for _, tmp := range aryPass {
	//			tags += tmp.ImageTag + " "
	//		}
	//		tags = strings.Trim(tags, " ")
	//		mm := make(map[string]interface{})
	//		mm["image_tags"] = tags
	//
	//		if err := pod.Updates(mm); err != nil {
	//			msg = "保存Pod信息失败"
	//			logger.Error(msg, err, podImage.PodUuid)
	//			return
	//		}
	//	}
	//}

	msg = "删除成功"
	code = 0
}

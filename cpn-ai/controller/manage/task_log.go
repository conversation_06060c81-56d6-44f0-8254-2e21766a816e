package manage

import (
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/service/tasklog"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
)

type taskLog_ struct {
}

var TaskLog taskLog_

type taskLogReq struct {
	LogKey string `json:"log_key"`
	Task   string `json:"task"`
	Last   bool   `json:"last"`
}

func (obj taskLog_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}
	var oReq taskLogReq
	if err := c.ShouldBindJSO<PERSON>(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if logKey, err := tasklog.GenLogKey(oReq.Task, oReq.LogKey); err != nil {
		logger.Error(err)
		result["err"] = err.Error()
	} else {
		if oReq.Last == false {
			if log, err := tasklog.List(logKey); err != nil {
				msg = err.Error()
				logger.Error(err)
				return
			} else {
				result["logs"] = log
			}
		}
		if item, _, err := tasklog.Last(logKey); err != nil {
			if err != gorm.ErrEmptySlice {
				logger.Error(err)
			}
		} else {
			result["last"] = item
			result["last_time"] = item.Time
		}

		if out, err := tasklog.OutTime(logKey, 5*60); err != nil {
			result["out_err"] = err.Error()
		} else {
			result["out_time"] = out
		}
	}

	msg = ""
	code = 0
}

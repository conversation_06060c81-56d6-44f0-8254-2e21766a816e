package manage

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"github.com/gin-gonic/gin"
	"net/http"
)

type nodeApi_ struct {
}

var NodeApi nodeApi_

type nodeReq struct {
	NodeId    uint   `json:"node_id"`
	Status    int    `json:"status"`
	NeedToken bool   `json:"need_token"`
	Kw        string `json:"kw"`
	Page      int    `json:"page"`
	PageSize  int    `json:"page_size"`
}

type nodeActionReq struct {
	NodeId uint   `json:"node_id"`
	Action string `json:"action"`
	Key    string `json:"key"`
}

type nodeResp struct {
	ID            uint              `json:"id"`
	Uuid          string            `json:"uuid"`
	Title         string            `json:"title"`
	ApiBaseUrl    string            `json:"api_base_url"`
	Domain        string            `json:"domain"`
	Remark        string            `json:"remark"`
	AccessToken   string            `json:"access_token"`
	Token         string            `json:"token"`
	TotalGpus     int               `json:"total_gpus"`
	FreeGpus      int               `json:"free_gpus"`
	TotalInstance int               `json:"total_instance"`
	TotalVirtual  int               `json:"total_virtual"`
	LastCheckTime jsontime.JsonTime `json:"last_check_time"`
	Status        int               `json:"status"`
	StatusTxt     string            `json:"status_txt"`
	CreatedAt     jsontime.JsonTime `json:"created_at"`
}

func (obj nodeApi_) DetailFromNode(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}
	var oReq nodeReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	if ginH, err := service.NodeService.GetNodeDetail(oReq.NodeId); err != nil {
		msg = "从节点获取信息失败"
		logger.Error(err)
		result["err"] = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj nodeApi_) ActionFromNode(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}
	var oReq nodeActionReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Action == "" || oReq.Key == "" {
		msg = "参数错误"
		return
	}

	postData := make(map[string]interface{})
	postData["action"] = oReq.Action
	postData["key"] = oReq.Key

	if ginH, err := service.NodeService.NodeAction(oReq.NodeId, postData); err != nil {
		msg = "从节点获取信息失败"
		logger.Error(err)
		result["err"] = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj nodeApi_) Action(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}
	var oReq nodeActionReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Action == "" {
		msg = "操作参数错误"
		return
	}

	var node model.Node
	if err := node.GetById(oReq.NodeId); err != nil {
		msg = err.Error()
		return
	}

	if oReq.Action == "OnSale" {
		if node.Status == 1 {
			msg = "当前为上架状态，不必修改"
			code = 0
			return
		}
		if err := node.SetStatus(1); err != nil {
			msg = err.Error()
			return
		}
		msg = "上架成功"
		code = 0
		return
	} else if oReq.Action == "OffSale" {
		if node.Status == 0 {
			msg = "当前为下架状态，不必修改"
			code = 0
			return
		}
		if err := node.SetStatus(0); err != nil {
			msg = err.Error()
			return
		}
		msg = "下架完成"
		code = 0
		return
	} else {
		msg = "为设置的操作"
	}
}

func (obj nodeApi_) RunningCommandFromNode(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		if code != 0 {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    msg,
				"result": result,
			})
		}
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}
	var oReq nodeReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	if ginH, err := service.NodeService.GetNodeRunningCommand(oReq.NodeId); err != nil {
		msg = "从节点获取信息失败"
		logger.Error(err)
		result["err"] = err.Error()
		return
	} else {
		code = 0
		c.JSON(http.StatusOK, ginH)
	}
}

func (obj nodeApi_) StaticFromNode(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}
	var oReq nodeReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	if mm, err := service.NodeService.GetNodeStatic(oReq.NodeId); err != nil {
		msg = "从节点获取信息失败"
		logger.Error(err)
		return
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": mm,
		})
	}
	msg = ""
	code = 0
}

func (obj nodeApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}
	var oReq nodeReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var node model.Node
	var ary = make([]nodeResp, 0)
	if total, err := node.List(&ary, oReq.NodeId, oReq.Status, oReq.Page, oReq.PageSize, ""); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {
			if ary[i].AccessToken == "" {
				if err := node.GetById(ary[i].ID); err == nil {
					node.SetAccessToken(utils.GetUUID())
				}
			}
			if oReq.NeedToken {
				if token := middleware.CreateNodeToken(ary[i].ID, ary[i].AccessToken); token == "" {
					logger.Error(token)
				} else {
					ary[i].Token = token

					//a, err := middleware.ParserNodeToken(aaa)
					//if err == nil {
					//	logger.Info(a.AccessToken)
					//}
				}
			}
			ary[i].StatusTxt = "未知"
			if ary[i].Status == 0 {
				ary[i].StatusTxt = "未上线"
			} else if ary[i].Status == 1 {
				ary[i].StatusTxt = "已上线"
			}
		}
		result["nodes"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

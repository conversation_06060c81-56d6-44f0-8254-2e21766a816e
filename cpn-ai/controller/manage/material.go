package manage

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/config"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path"
	"runtime"
	"strings"
	"time"
)

type materialApi_ struct {
}

var MaterialApi materialApi_

type materialReq struct {
	Uuid string `json:"uuid"`
}

type materialListReq struct {
	Uuid     string `json:"uuid"`
	Status   int    `json:"status"`
	Kw       string `json:"kw"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type materialResp struct {
	Uuid      string            `json:"uuid"`
	Title     string            `json:"title"`
	Path      string            `json:"-"`
	Url       string            `json:"url"`
	Remark    string            `json:"remark"`
	UpdatedAt time.Time         `json:"-"`
	CreatedAt jsontime.JsonTime `json:"created_at"`
}

func (obj materialApi_) Add(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	title, _ := c.GetPostForm("title")
	if title == "" {
		msg = "请输入标题"
		return
	}
	remark, _ := c.GetPostForm("remark")

	uuid, _ := c.GetPostForm("uuid")
	f, err := c.FormFile("file")
	if err != nil {
		if strings.Contains(err.Error(), "no such file") {
			msg = "未获取到上传文件"
			if uuid == "" {
				msg = "请选择上传文件"
				return
			} else { //只更新标题和备注
				var material model.Material
				if err := material.GetByUuid(uuid); err != nil {
					msg = "查询记录失败"
					logger.Error(err)
					return
				} else {
					if err := material.SetInfo(title, remark); err != nil {
						msg = "更新标题备注失败 err:" + err.Error()
						return
					} else {
						msg = "标题备注更新成功"
						code = 0
						return
					}
				}
			}
		} else {
			msg = "获取上传文件失败 err:" + err.Error()
			return
		}
	}
	ext := strings.ToLower(path.Ext(f.Filename))

	var material model.Material
	if uuid != "" {
		if err := material.GetByUuid(uuid); err != nil {
			msg = "查询记录失败"
			logger.Error(err)
			return
		}
		if material.Ext != ext {
			msg = "当前文件后缀(" + ext + ")必须与上一个文件后缀(" + material.Ext + ")一致"
			return
		}
	} else {
		uuid = utils.GetUUID()
		material = model.Material{
			Uuid:   uuid,
			Title:  title,
			Path:   "cpn/material/" + uuid + ext,
			Remark: remark,
			Ext:    ext,
			Status: 1}
		if err := material.Save(); err != nil {
			msg = "保存记录失败 err:" + err.Error()
			return
		}
	}

	filePath := path.Join(config.DiffusionFilePath, material.Path)

	//保存上传的程序文件
	if err := c.SaveUploadedFile(f, filePath); err != nil {
		msg = "文件保存失败 err:" + err.Error()
		logger.Error(msg, err)
		return
	}
	if err := material.SetUpdate(); err != nil {
		msg = "更新文件时间戳失败"
		logger.Error(msg, err)
		return
	}
	msg = "文件保存成功"
	code = 0
	return
}

func (obj materialApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			stack := make([]byte, 4096)
			runtime.Stack(stack, false)
			logger.Error("panicked:", e, "\nStack Trace:\n", string(stack))
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq materialListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Page <= 0 {
		oReq.Page = 1
	}
	if oReq.PageSize <= 0 || oReq.PageSize > 100 {
		oReq.PageSize = 100
	}

	var material model.Material
	var ary = make([]materialResp, 0)

	queryParm := make(map[string]interface{})

	//queryParm["user_id"] = claims.UserId

	if oReq.Uuid != "" {
		if err := material.GetByUuid(oReq.Uuid); err != nil {
			msg = "记录不存在"
			logger.Error(err)
			return
		}
		queryParm["id"] = material.ID
	}

	if total, err := material.List(&ary, queryParm, oReq.Page, oReq.PageSize); err != nil {
		msg = "查询失败"
		logger.Error(msg, err, utils.GetJsonFromStruct(queryParm))
		return
	} else {
		for i := 0; i < len(ary); i++ {
			ary[i].Url = fmt.Sprintf("https://img.chenyu.com")

			ary[i].Url = fmt.Sprintf("%s%s?t=%d", config.DiffusionDomain, ary[i].Path, ary[i].UpdatedAt.Unix())
			//https://img.cyuai.com/cpn/pod_static/77dfa23a411c4bbb9c6c039a464ffc78/1722570709604.jpg
		}
		result["materials"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj materialApi_) Del(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq materialReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Uuid == "" {
		msg = "参数错误"
		return
	}

	var material model.Material
	if err := material.GetByUuid(oReq.Uuid); err != nil {
		msg = "记录不存在"
		logger.Error(err)
		return
	}

	if strings.Contains(material.Path, "cpn/material") {
		filePath := path.Join(config.DiffusionFilePath, material.Path)
		if err := os.Remove(filePath); err != nil {
			msg = "删除文件失败 err:" + err.Error()
			logger.Error(err, filePath)
			return
		}
	}

	if err := material.Delete(); err != nil {
		msg = "删除失败"
		logger.Error(msg, err)
		return
	} else {
		code = 0
		msg = "删除成功"
		return
	}
}

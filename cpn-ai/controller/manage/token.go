package manage

import (
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"github.com/gin-gonic/gin"
	"net/http"
)

type tokenApi_ struct {
}

var TokenApi tokenApi_

type addTokenReq struct {
	Name      string `json:"name"`
	TokenUuid string `json:"token_uuid"`
}

type tokenResp struct {
	Uuid      string            `json:"uuid"`
	Name      string            `json:"name"`
	SecretKey string            `json:"secret_key"`
	Status    int               `json:"status"`
	StatusTxt string            `json:"status_txt"`
	CreatedAt jsontime.JsonTime `json:"created_at"`
}

func (obj tokenApi_) Add(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.J<PERSON>(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq addTokenReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	name := oReq.Name
	if len(name) > 20 {
		name = oReq.Name[:20]
	}

	sk := ""
	if skTmp, err := utils.GeneralSk(); err != nil {
		logger.Error(err)
	} else {
		if len(skTmp) < 5 {
			msg = "生成秘钥失败"
		}
		sk = skTmp
	}
	token := model.UserToken{
		Uuid:      utils.GetUUID(),
		UserId:    claims.UserId,
		Name:      name,
		SecretKey: sk,
		Status:    1,
	}

	if err := token.Save(); err != nil {
		logger.Error(err)
		msg = "创建失败"
		return
	} else {

		if aa, err := service.SkService.Add(token.SecretKey, token.Name, token.UserId); err != nil {
			logger.Error(err)
			msg = "添加出错"
			return
		} else {
			if aa.Code != 0 {
				msg = aa.Msg
				return
			}
		}

		statusTxt := "有效"
		if token.Status != 1 {
			statusTxt = "无效"
		}
		result["token"] = tokenResp{
			Uuid:      token.Uuid,
			Name:      token.Name,
			SecretKey: token.SecretKey,
			Status:    token.Status,
			StatusTxt: statusTxt,
			CreatedAt: jsontime.JsonTime(token.CreatedAt),
		}
	}
	msg = "创建成功"
	code = 0
}

func (obj tokenApi_) Modify(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var oReq addTokenReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	if oReq.Name == "" {
		msg = "请输入名称"
		return
	}

	var token model.UserToken
	if err := token.GetByUuid(oReq.TokenUuid); err != nil {
		logger.Error(err)
		msg = "参数错误"
	}
	if token.UserId != claims.UserId {
		logger.Error("无权限:", oReq, "---", token.UserId, "---", claims.UserId)
		msg = "无权限"
		return
	}

	name := oReq.Name
	if len(name) > 20 {
		name = oReq.Name[:20]
	}
	if err := token.SetName(oReq.Name); err != nil {
		logger.Error(err)
		msg = "修改失败"
		return
	} else {

		//if aa, err := service.SkService.Add(token.SecretKey); err != nil {
		//	logger.Error(err)
		//} else {
		//	if aa.Code == 200 {
		//		result["remote"] = true
		//	} else {
		//		logger.Error(utils.GetJsonFromStruct(aa))
		//	}
		//}

		statusTxt := "有效"
		if token.Status != 1 {
			statusTxt = "无效"
		}
		result["token"] = tokenResp{
			Uuid:      token.Uuid,
			Name:      token.Name,
			SecretKey: token.SecretKey,
			Status:    token.Status,
			StatusTxt: statusTxt,
			CreatedAt: jsontime.JsonTime(token.CreatedAt),
		}
	}
	msg = "修改成功"
	code = 0
}

func (obj tokenApi_) Del(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq addTokenReq

	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var token model.UserToken

	if err := token.GetByUuid(oReq.TokenUuid); err != nil {
		logger.Error(err)
		msg = "参数错误"
	}
	if token.UserId != claims.UserId {
		logger.Error("无权限:", oReq, "---", token.UserId, "---", claims.UserId)
		msg = "无权限"
		return
	}

	if aa, err := service.SkService.Delete(token.SecretKey); err != nil {
		logger.Error(err)
		msg = "删除出错"
		return
	} else {
		if aa.Code != 200 {
			msg = aa.Msg
			return
		}
	}

	if err := token.DeleteByUuid(oReq.TokenUuid); err != nil {
		logger.Error(err)
		msg = "删除失败"
		return
	} else {
		result["token_uuid"] = oReq.TokenUuid
	}
	msg = "删除成功"
	code = 0
}

func (obj tokenApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims == nil {
		msg = "请先登录"
		code = 2
		return
	}

	var token model.UserToken

	var ary = make([]tokenResp, 0)
	if total, err := token.List(&ary, claims.UserId, -1, 1, 100); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {
			if ary[i].Status == 1 {
				ary[i].StatusTxt = "有效"
			} else {
				ary[i].StatusTxt = "无效"
			}
		}
		result["tokens"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj tokenApi_) RemoteList(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	if skRes, err := service.SkService.List(); err != nil {
		logger.Error(err)
		msg = err.Error()
		return
	} else {
		if skRes.Code == 200 {
			result["sks"] = skRes.Data
			code = 0
		} else {
			msg = skRes.Msg
		}
	}
}

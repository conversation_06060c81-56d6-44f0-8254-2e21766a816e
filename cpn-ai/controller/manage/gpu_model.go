package manage

import (
	"bytes"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/structs"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"net/http/httptest"
	"strings"
)

type gpuModelApi_ struct {
}

var GpuModelApi gpuModelApi_

type gpuModelReq struct {
	gpuModelId uint `json:"gpu_model_id"`
}

type gpuModelListReq struct {
	gpuModelUuid string `json:"gpu_model_uuid"`
	Status       int    `json:"status"`
	Page         int    `json:"page"`
	PageSize     int    `json:"page_size"`
}

type gpuModelResp struct {
	ID        uint              `json:"id"`
	Uuid      string            `json:"uuid"`
	Title     string            `json:"title"`
	GpuName   string            `json:"gpu_name"`
	Desc      string            `json:"desc"`
	MemoryG   int               `json:"memory_g"`
	MemoryM   int               `json:"memory_m"`
	Price     string            `json:"price"`
	Remark    string            `json:"remark"`
	Status    int               `json:"status"`
	StatusTxt string            `json:"status_txt"`
	CreatedAt jsontime.JsonTime `json:"created_at"`
	TotalGpus int               `json:"total_gpus"`
	ValidGpus int               `json:"valid_gpus"`
	FreeGpus  int               `json:"free_gpus"`
	FreeTxt   string            `json:"free_txt"`
}

func (obj gpuModelApi_) List(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	mStats := make(map[uint]structs.GpuStats)
	virtualIds := make([]uint, 0)
	isValidVirtual := func(podIds string, podId uint) bool {
		if podIds == "" {
			return true
		}
		tmp := fmt.Sprintf("|%d|", podId)
		return strings.Contains(podIds, tmp)
	}

	var virtual model.Virtual
	aryVirtual := make([]model.Virtual, 0)
	if _, err := virtual.ListStats(&aryVirtual, virtualIds, 1, 1000); err != nil {
		logger.Error(err)
	} else {
		for _, tmpVirtual := range aryVirtual {
			if !isValidVirtual(tmpVirtual.PodIds, 0) {
				continue
			}
			//if config.Env == enums.EnvEnum.ONLINE {
			//	if tmpVirtual.NodeId != 2 {
			//		continue
			//	}
			//}
			//if config.Env == enums.EnvEnum.PRODUCTION {
			//	if tmpVirtual.NodeId == 2 {
			//		continue
			//	}
			//}

			if tmpStat, ok := mStats[tmpVirtual.GpuModelId]; ok {
				tmpStat.FreeGpus += tmpVirtual.FreeGpus
				tmpStat.TotalGpus += tmpVirtual.TotalGpus
				mStats[tmpVirtual.GpuModelId] = tmpStat
			} else {
				mStats[tmpVirtual.GpuModelId] = structs.GpuStats{GpuModelId: tmpVirtual.GpuModelId, FreeGpus: tmpVirtual.FreeGpus, TotalGpus: tmpVirtual.TotalGpus}
			}
		}
	}

	var aryStatsTotal = make([]structs.GpuStats, 0)
	if err := virtual.StatsTotal(&aryStatsTotal); err != nil {
		logger.Error(err)
	}

	var oReq gpuModelListReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}
	var gpuModel model.GpuModel
	var ary = make([]gpuModelResp, 0)

	if total, err := gpuModel.List(&ary, uint(0), "", oReq.Status, 1, 100); err != nil {
		msg = "查询失败"
		logger.Error(msg, err)
		return
	} else {
		for i := 0; i < len(ary); i++ {
			if ary[i].Status == 1 {
				ary[i].StatusTxt = "已上架"
			} else {
				ary[i].StatusTxt = "未上架"
			}
			ary[i].FreeTxt = "已售罄"
			if tmpStat, ok := mStats[ary[i].ID]; ok {
				ary[i].FreeGpus = tmpStat.FreeGpus
				ary[i].ValidGpus = tmpStat.TotalGpus
				//充足 少量 紧张 无
				if ary[i].FreeGpus <= 0 {
					ary[i].FreeTxt = "已售罄"
				} else if ary[i].FreeGpus < 5 {
					ary[i].FreeTxt = "紧张"
				} else if ary[i].FreeGpus < 10 {
					ary[i].FreeTxt = "少量"
				} else {
					ary[i].FreeTxt = "充足"
				}
			}

			for j := 0; j < len(aryStatsTotal); j++ {
				if ary[i].ID == aryStatsTotal[j].GpuModelId {
					ary[i].TotalGpus = aryStatsTotal[j].TotalGpus
				}
			}
		}
		result["items"] = ary
		result["total"] = total
	}
	msg = ""
	code = 0
}

func (obj gpuModelApi_) Test() {
	// 创建 Gin 测试环境
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	// 创建模拟 HTTP 请求
	body := bytes.NewBufferString(`{}`)
	req, _ := http.NewRequest("POST", "/gpu_model/list", body)
	req.Header.Set("Content-Type", "application/json")
	ctx.Request = req

	claims := &middleware.CenterClaims{
		UserId:       5,
		Mobile:       "13067943279",
		Username:     "",
		PermOperates: []string{"all"},
	}
	ctx.Set("center_claims", claims)

	obj.List(ctx)

	// 打印返回结果
	println(w.Body.String())

}

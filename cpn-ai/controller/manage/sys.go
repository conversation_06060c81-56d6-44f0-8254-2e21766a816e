package manage

import (
	"cpn-ai/common"
	"cpn-ai/common/jsontime"
	"cpn-ai/common/logger"
	"cpn-ai/common/utils"
	"cpn-ai/controller/master"
	"cpn-ai/enums"
	"cpn-ai/middleware"
	"cpn-ai/model"
	"cpn-ai/service"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"net/http"
	"time"
)

type _sys struct {
}

var SysApi _sys

type sysStatsReq struct {
	UserId uint `json:"user_id"`
}

type sysIgnoreAlarmReq struct {
	Key     string `json:"key"`
	Minutes int    `json:"minutes"`
}

func (obj _sys) IgnoreAlarm(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.J<PERSON><PERSON>(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq sysIgnoreAlarmReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	if oReq.Key == "" {
		msg = "key 为空"
		return
	}
	redisKey := enums.RedisKeyEnum.AlarmKey + oReq.Key
	if err := common.RedisSet(redisKey, time.Now().Format("2006-01-02 15:04:05"), time.Minute*time.Duration(oReq.Minutes)); err != nil {
		msg = err.Error()
		return
	}
	msg = "设置成功"
	result["redis_key"] = redisKey
	result["ip"] = utils.GetLocalIP()
	code = 0
}

func (obj _sys) Info(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}

		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})

	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	//var oReq virtualReq
	//if err := c.ShouldBindJSON(&oReq); err != nil {
	//	msg = "参数解析失败"
	//	logger.Error(msg, err)
	//	return
	//}

	result["task_running_count"] = service.TaskRunningCount

	tmpMap := make(map[string]interface{})
	service.TaskRunning.Range(func(key, value interface{}) bool {
		tmpMap[key.(string)] = value
		return true // 继续遍历
	})
	result["task_running"] = tmpMap
	result["task_start"] = service.TaskStart.Format(jsontime.TimeFormat)

	count := 0
	master.ReportApi.Md5PodImageId.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	result["Md5PodImageId"] = count
	code = 0
}

func (obj _sys) Stats(c *gin.Context) {
	code := 1
	msg := ""
	result := make(map[string]interface{})
	defer func() {
		if e := recover(); e != nil {
			logger.Error("奔溃:", e)
			code = -2
			msg = "执行失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	}()

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		msg = "权限不足"
		return
	}

	var oReq sysStatsReq
	if err := c.ShouldBindJSON(&oReq); err != nil {
		msg = "参数解析失败"
		logger.Error(msg, err)
		return
	}

	var user model.User
	if totalSize, err := user.StatsPrivateSize(oReq.UserId); err != nil {
		msg = "统计个人存储失败"
		logger.Error(msg, err)
		return
	} else {
		result["private_storage_size"] = totalSize
	}

	if totalSize, err := user.StatsPrivateSizeOutLimit(oReq.UserId); err != nil {
		msg = "统计个人扣费存储失败"
		logger.Error(msg, err)
		return
	} else {
		result["private_storage_size_billing"] = totalSize
		d1024 := decimal.NewFromInt(1024)
		decimalSizeG := decimal.NewFromInt(totalSize).Div(d1024).Div(d1024).Div(d1024)
		amount := decimalSizeG.Mul(common.CloudStoreHourPrice)
		result["private_storage_size_amount"] = amount
	}

	var podImage model.PodImage
	if totalSize, err := podImage.StatsSize(oReq.UserId, enums.ImageTypeEnum.Private); err != nil {
		msg = "统计个人镜像失败"
		logger.Error(msg, err)
		return
	} else {
		result["private_image_size"] = totalSize
	}

	if totalSize, err := user.StatsPrivateImageSizeOutLimit(oReq.UserId); err != nil {
		msg = "统计个人已计费镜像失败"
		logger.Error(msg, err)
		return
	} else {
		result["private_image_size_billing"] = totalSize
		d1024 := decimal.NewFromInt(1024)
		decimalSizeG := decimal.NewFromInt(totalSize).Div(d1024).Div(d1024).Div(d1024)
		amount := decimalSizeG.Mul(common.ImageStoreHourPrice)
		result["private_image_size_amount"] = amount
	}

	if totalSize, err := podImage.StatsSize(oReq.UserId, enums.ImageTypeEnum.Public); err != nil {
		msg = "统计公共镜像失败"
		logger.Error(msg, err)
		return
	} else {
		result["public_image_size"] = totalSize
	}

	if totalSize, err := podImage.StatsSize(0, enums.ImageTypeEnum.Base); err != nil {
		msg = "统计基础镜像失败"
		logger.Error(msg, err)
		return
	} else {
		result["base_image_size"] = totalSize
	}

	result["online_user_count"] = middleware.CountAccessToken()
	code = 0
	msg = "统计完成"

}

package service

import (
	"sheys-ai/utils/logger"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/regions"
	tmt "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tmt/v20180321"
)

type tencentCloud_ struct {
	client *tmt.Client
}

func (o *tencentCloud_) initClient() {
	secretId := "AKIDQaiY8SG2X0QdcMr0uYx7fRlriJir2Ln3"
	secretKey := "H1SfbOwLcfUUIuIQersoY8VjqUu6xlT5"
	credential := common.NewCredential(secretId, secretKey)
	client, err := tmt.NewClient(credential, regions.Beijing, profile.NewClientProfile())
	if err != nil {
		logger.Error(err)
		return
	}
	o.client = client
}

func (o *tencentCloud_) TextTranslate(sourceText string) string {

	if o.client == nil {
		o.initClient()
	}

	request := tmt.NewTextTranslateRequest()
	//txt := "hello，你是个厉害的人"
	//request.SourceText = &txt
	request.SourceText = &sourceText

	source := "auto"
	request.Source = &source

	target := "en"
	request.Target = &target

	projectId := int64(0)
	request.ProjectId = &projectId

	response, err := o.client.TextTranslate(request)
	if err != nil {
		logger.Error(err, response)
		return ""
	}
	return *response.Response.TargetText
}

var TencentCloud tencentCloud_

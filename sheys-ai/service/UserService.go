package service

import (
	"errors"
	"sheys-ai/model"
	"sheys-ai/utils/logger"
	"sheys-ai/utils/tools"
)

type userService_ struct {
}

func (service *userService_) SetUserInvitationCode(userId uint) error {
	var user model.User
	if err := user.GetByID(userId); err != nil {
		return errors.New("查找用户信息失败")
	}
	if user.InvitationCode != "" {
		return nil
	}

	for i := 0; i < 10; i++ {
		invitationCode := tools.GetInvitationCode()
		exists, err := user.ExistsInvitationCode(invitationCode)
		if err != nil {
			logger.Error(err)
			return err
		}
		if exists == false {
			if er := user.SetInvitationCode(invitationCode); er != nil {
				logger.Error(err)
				return err
			} else {
				return nil
			}
		}
	}
	return errors.New("邀请码多次尝试设置失败")
}

var UserService userService_

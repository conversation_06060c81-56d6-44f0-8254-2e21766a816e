package manage

import (
	"net/http"
	"sheys-ai/middleware"
	"sheys-ai/model"
	"sheys-ai/utils/errmsg"
	"sheys-ai/utils/logger"

	"github.com/gin-gonic/gin"
)

type ImageReq struct {
	Folder   uint `json:"folder"`
	Page     int  `json:"page"`
	PageSize int  `json:"page_size"`
}

type ImageFilterReq struct {
	FolderName string `json:"foldername"`
	UserId     uint   `json:"user_id"`
	Filename   string `json:"filename"`
	Username   string `json:"username"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type folderListQueryReq struct {
	UserId     uint   `json:"user_id"`
	Start      int64  `json:"start"`
	End        int64  `json:"end"`
	FolderName string `json:"folder_name"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

func FolderList(c *gin.Context) {
	var code int
	var req folderListQueryReq

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		code = errmsg.FAIL
		errmsg.Abort(c, code, "数据获取失败")
		return
	}
	var folder model.ImageFolder
	folders, total, err := folder.GetListByUser(req.UserId, req.FolderName, req.Start, req.End, "", req.Page, req.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "无法获取用户文件夹")
		return
	}
	result := make(map[string]interface{})
	result["total"] = total
	result["folders"] = folders
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

func ImageList(c *gin.Context) {
	var req ImageReq
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var folder model.ImageFolder
	if err := folder.GetByID(req.Folder); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "找不到该路径")
		return
	}
	var retouchImage model.RetouchImage
	arr, total, err := retouchImage.GetListByFolder(folder.ID, req.Page, req.PageSize)
	result := make(map[string]interface{})
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取图片列表数据出错")
		return
	}
	result["folders"] = arr
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}

}

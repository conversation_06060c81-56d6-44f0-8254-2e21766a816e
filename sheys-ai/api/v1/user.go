package v1

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"net/http"
	"os"
	"path"
	"sheys-ai/enums"
	"sheys-ai/middleware"
	"sheys-ai/model"
	"sheys-ai/service"
	"sheys-ai/utils/config"
	"sheys-ai/utils/errmsg"
	"sheys-ai/utils/logger"
	"sheys-ai/utils/myimg"
	"sheys-ai/utils/myredis"
	"sheys-ai/utils/tools"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/shopspring/decimal"
)

type UserInfoResp struct {
	UserId         uint            `json:"user_id"`
	Username       string          `json:"username"`
	AvatarUrl      string          `json:"avatar_url"`
	Mobile         string          `json:"mobile"`
	InvitationCode string          `json:"invitation_code"`
	Balance        decimal.Decimal `json:"balance"`
	AccountLevel   int             `json:"account_level"`
	CreatedAt      time.Time       `json:"created_at"`
}

type userApi_ struct {
}

type resetUsernameReq struct {
	Username string `json:"username"`
}

type resetMobileReq struct {
	Mobile  string `json:"mobile"`
	SmsCode string `json:"sms_code"`
}

type resetPasswordReq struct {
	Mobile   string `json:"mobile"`
	Password string `json:"password"`
	SmsCode  string `json:"sms_code"`
}

type signSubUserReq struct {
	Username string `json:"username"`
	Mobile   string `json:"mobile"`
	SmsCode  string `json:"sms_code"`
}

type delSubUserReq struct {
	SubId uint `json:"sub_id"`
}

type userStatisticReq struct {
	UserId uint `json:"user_id"`
}

type subWorkReq struct {
	UserId   uint  `json:"user_id"`
	Start    int64 `json:"start"`
	End      int64 `json:"end"`
	Page     int   `json:"page"`
	PageSize int   `json:"page_size"`
}

type balanceReq struct {
	OrderNo string `json:"order_no"`
}

type balanceListReq struct {
	End      int64 `json:"end"`
	Start    int64 `json:"start"`
	User     uint  `json:"user"`
	Page     int   `json:"page"`
	PageSize int   `json:"page_size"`
}

func verifySmsCode(mobile string, smsCode string, c *gin.Context) bool {

	redisKey := enums.RedisKeyEnum.SmsModifyPassword + mobile
	testCount := myredis.Get(redisKey + ":testcount")
	if testCount == "" {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请10分钟后重试")
		return false
	}
	iTestCount, _ := strconv.Atoi(testCount)
	if iTestCount > 10 {
		errmsg.Abort(c, errmsg.FAIL, "验证码尝试次数过多，请10分钟后重试")
		return false
	}
	iTestCount = iTestCount + 1
	if err := myredis.Set(redisKey+":testcount", strconv.Itoa(iTestCount), time.Minute*10); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "设置短信验证码尝试参数失败")
		return false
	}

	v := myredis.Get(redisKey)
	if v == "" {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请重试")
		return false
	}
	if v != smsCode {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return false
	}
	return true
}

func (obj userApi_) QueryBalance(c *gin.Context) {
	var code int
	var req balanceReq
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	userId := claims.UserId
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var balance model.Balance
	balance.GetByOrderNo(req.OrderNo)
	if balance.MasterId != userId && balance.UserId != userId {
		errmsg.Abort(c, errmsg.FAIL, "这不是你的订单")
		return
	}
	result := make(map[string]interface{})
	result["balance"] = balance
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}

}

func (obj userApi_) QueryBalanceList(c *gin.Context) {
	var code int
	var req balanceListReq
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	userId := claims.UserId
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var balance model.Balance
	if req.User > 0 {
		userId = req.User
	}
	arr, total, err := balance.GetList(userId, req.Start, req.End, req.Page, req.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "无法拉取明细")
		return
	}
	result := make(map[string]interface{})
	result["total"] = total
	result["data"] = arr
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}

}

func (obj userApi_) GetSubWorkList(c *gin.Context) {
	var code int
	var req subWorkReq
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	master := claims.UserId
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	if req.UserId != master {
		var subUser model.User
		subUser.GetByID(req.UserId)
		if subUser.MasterId != master {
			errmsg.Abort(c, errmsg.FAIL, "该账号不是您的子账号")
			return
		}
	}
	var folder model.ImageFolder
	folders, total, err := folder.GetListByUser(req.UserId, "", req.Start, req.End, "", req.Page, req.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "无法获取用户文件夹")
		return
	}
	result := make(map[string]interface{})
	result["total"] = total
	result["folders"] = folders
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}

}

func (obj userApi_) QueryStatistic(c *gin.Context) {
	var code int
	var req userStatisticReq
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	master := claims.UserId
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}
	var user model.User
	user.GetByID(req.UserId)
	if req.UserId != master && user.MasterId != master {
		errmsg.Abort(c, errmsg.FAIL, "该账号不是您的子账号")
		return
	} else {
		var images model.RetouchImage
		imgCount, err := images.GetCountByUser(req.UserId)
		if err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "出图数量查询错误")
			return
		}
		var balance model.Balance
		totalConsume, err2 := balance.GetTotalConsumeByUser(req.UserId)
		if err2 != nil {
			logger.Error(err2)
			errmsg.Abort(c, errmsg.FAIL, "消费金额查询错误")
			return
		}
		result := make(map[string]interface{})
		result["img_count"] = imgCount
		result["total_consume"] = totalConsume
		if code == errmsg.SUCCESS {
			c.JSON(http.StatusOK, gin.H{
				"code":   code,
				"msg":    "",
				"result": result,
			})
		}
	}

}

// 查询子账号列表
func (obj userApi_) GetSubUsers(c *gin.Context) {
	var code int
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var user model.User
	user.GetByID(claims.UserId)
	userList := make([]model.User, 0)
	userList = append(userList, user)
	if user.MasterId == 0 {
		var subUser model.User
		arr, err := subUser.GetSubUsers(user.ID)
		if err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "获取子账号失败")
			return
		}
		userList = append(userList, arr...)
	}
	users := make([]UserInfoResp, 0)
	for _, user := range userList {
		users = append(users, UserInfoResp{
			UserId:         user.ID,
			Username:       user.Username,
			AvatarUrl:      user.Avatar,
			Mobile:         user.Mobile,
			InvitationCode: user.InvitationCode,
			CreatedAt:      user.CreatedAt,
		})
	}
	result := make(map[string]interface{})
	result["sub_user_list"] = users
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}

}

func (obj userApi_) DelSubUser(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}
	var req delSubUserReq
	mid := claims.UserId
	err := c.ShouldBindJSON(&req)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}
	var subUser model.User
	subUser.GetByID(req.SubId)
	if subUser.MasterId == mid {
		if err := subUser.Del(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "删除失败")
			return
		}

		if code == errmsg.SUCCESS {
			c.JSON(http.StatusOK, gin.H{
				"code": code,
				"msg":  "删除成功",
			})
		}
	} else {
		code = errmsg.FAIL
		msg = "账号下没有该子账号"
		errmsg.Abort(c, code, msg)
		return
	}

}

// 添加子账号
func (obj userApi_) SignSubUser(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	var req signSubUserReq
	mid := claims.UserId
	err := c.ShouldBindJSON(&req)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}
	var user model.User
	if err := user.GetByID(mid); err != nil {
		code = errmsg.FAIL
		msg = "主账号信息获取失败"
		errmsg.Abort(c, code, msg)
		return
	}
	redisKey := enums.RedisKeyEnum.SmsLogin + req.Mobile
	testCount := myredis.Get(redisKey + ":testcount")
	if testCount == "" {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请10分钟后重试")
		return
	}
	iTestCount, _ := strconv.Atoi(testCount)
	if iTestCount > 10 {
		errmsg.Abort(c, errmsg.FAIL, "验证码尝试次数过多，请10分钟后重试")
		return
	}
	iTestCount = iTestCount + 1
	if err := myredis.Set(redisKey+":testcount", strconv.Itoa(iTestCount), time.Minute*10); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "设置短信验证码尝试参数失败")
		return
	}

	v := myredis.Get(redisKey)
	if v == "" {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请重试")
		return
	}
	if v != req.SmsCode {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	var sUser model.User
	if err := sUser.GetByMobile(req.Mobile); err != nil {
		logger.Error(err)
		msg = "次级账号查询失败"
		errmsg.Abort(c, code, msg)
		return
	}
	var subUser model.User
	if sUser.ID != 0 {
		sUser.MasterId = mid
		if err := sUser.Save(); err != nil {
			logger.Error(err)
			msg = "次级账号绑定失败"
			errmsg.Abort(c, code, msg)
			return
		}
		subUser = sUser

	} else {
		subUser = model.User{
			Username:  req.Username,
			MasterId:  mid,
			Role:      enums.RoleEnum.Customer,
			Mobile:    req.Mobile,
			PhoneInfo: "{}",
		}

		if err := subUser.Save(); err != nil {
			code = errmsg.FAIL
			logger.Error(err)
			msg = "次级账号生成失败"
			errmsg.Abort(c, code, msg)
			return
		}
	}

	result := make(map[string]interface{})
	result["sub_user"] = subUser

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj userApi_) GetUserInfo(c *gin.Context) {
	var code int
	var msg string

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	var user model.User
	if err := user.GetByID(claims.UserId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	avatarUrl := ""
	if user.Avatar != "" {
		avatarUrl = config.DiffusionDomain + service.ImgService.GetSmallImagePath(user.Avatar)
	}
	mid := claims.UserId
	accountLevel := 0
	if user.MasterId != 0 {
		mid = user.MasterId
		accountLevel = 1
	}

	mobile := user.Mobile
	var balance model.Balance
	if err := balance.GetLastEffectiveBalance(mid); err != nil {
		logger.Error(err)
		fmt.Println("无充值记录")
		balance.AfterOccurred = decimal.NewFromInt(0)
	}

	resp := UserInfoResp{
		UserId:         user.ID,
		Username:       user.Username,
		AvatarUrl:      avatarUrl,
		Mobile:         tools.FormatMobileStar(mobile),
		InvitationCode: user.InvitationCode,
		Balance:        balance.AfterOccurred,
		AccountLevel:   accountLevel,
		CreatedAt:      user.CreatedAt,
	}

	result := make(map[string]interface{})
	result["user"] = resp

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj userApi_) UploadHeadImg(c *gin.Context) {
	var code int

	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	userId := claims.UserId

	f, err := c.FormFile("file")
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
		return
	}

	var user model.User
	if err := user.GetByID(userId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	//********/user-cd/cd6fa037da4e59ee94b8db4fc87c163e.base64
	//********/out-cd/cd6fa037da4e59ee94b8db4fc87c163e.base64
	//https://aigc.cyuai.com/output/********/out-cd/cd6fa037da4e59ee94b8db4fc87c163e.base64

	oMd5Str := fmt.Sprintf("%d,%s", user.ID, time.Now().Format("2006-01-02 15:04:05.000"))
	has := md5.Sum([]byte(oMd5Str))
	md5Str := hex.EncodeToString(has[:])
	if len(md5Str) != 32 {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "头像图片名称生成失败")
		return
	}

	ext := path.Ext(f.Filename) // 输出 .html

	filename := md5Str + strings.ToLower(ext)

	pre2 := md5Str
	pre2 = pre2[0:1]

	//filepath := config.TempImgFilePath + filename
	//relativePath := fmt.Sprintf("%s/user-%s/%s", time.Now().Format("20060102"), pre2, filename)
	relativePath := fmt.Sprintf("%s/head/", time.Now().Format("20060102"))
	relativePathFile := relativePath + filename

	dirPath := config.DiffusionFilePath + relativePath
	filepath := config.DiffusionFilePath + relativePathFile

	// tempImg := model.TempImg{
	// 	UserId:    userId,
	// 	OrigWhere: 5,
	// 	Md5:       md5Str,
	// 	Path:      relativePathFile,
	// 	State:     0,
	// }
	// if err := tempImg.Save(); err != nil {
	// 	logger.Error(err)
	// 	errmsg.Abort(c, errmsg.FAIL, "图片数据生成失败")
	// 	return
	// }

	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		// mkdir 创建目录，mkdirAll 可创建多层级目录
		if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
			logger.Error(err, dirPath)
			errmsg.Abort(c, errmsg.FAIL, "创建路径失败")
			return
		}
	}

	if err := c.SaveUploadedFile(f, filepath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}
	// if err := tempImg.SetState(enums.TempImgStateEnum.Uploaded); err != nil {
	// 	logger.Error(err)
	// }

	img, _, err := myimg.FileToImg(filepath)
	small := myimg.ResizeImg(156, 156, img, true)
	smallFilePath := service.ImgService.GetSmallImagePath(filepath)
	if err := myimg.ImgToFile(small, smallFilePath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "缩略图保存失败")
	}

	if err := user.SetAvatar(relativePathFile); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "头像上传失败，请重试")
		return
	}

	result := make(map[string]interface{})
	result["avatar_url"] = config.DiffusionDomain + service.ImgService.GetSmallImagePath(relativePathFile)

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "头像上传成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "头像上传失败")
	}
}

func (obj userApi_) ChangeUsername(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	var oReq resetUsernameReq

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if len(oReq.Username) < 2 {
		errmsg.Abort(c, errmsg.FAIL, "用户名格式不正确")
		return
	}

	userId := claims.UserId
	var user model.User
	if err := user.GetByID(userId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	if exist, err := user.ExistsUsername(oReq.Username); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "验证用户名失败")
		return
	} else {
		if exist {
			errmsg.Abort(c, errmsg.FAIL, "该用户名已经存在")
			return
		} else {
			if er := user.SetUsername(oReq.Username); er != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "更新用户名失败")
			} else {
				result := make(map[string]interface{})
				result["username"] = oReq.Username
				c.JSON(http.StatusOK, gin.H{
					"code":    code,
					"message": "用户名更改成功",
					"msg":     "用户名更改成功",
					"result":  result,
				})
			}
		}
	}

}

func (obj userApi_) ChangeMobile(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	var oReq resetMobileReq

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if len(oReq.Mobile) != 11 {
		errmsg.Abort(c, errmsg.FAIL, "手机号码格式不正确")
		return
	}
	if len(oReq.SmsCode) < 4 {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	userId := claims.UserId
	var user model.User
	if err := user.GetByID(userId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	if user.Mobile == oReq.Mobile {
		errmsg.Abort(c, errmsg.FAIL, "手机号码未变更")
		return
	}

	redisKey := enums.RedisKeyEnum.SmsModifyPassword + oReq.Mobile
	testCount := myredis.Get(redisKey + ":testcount")
	if testCount == "" {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请10分钟后重试")
		return
	}
	iTestCount, _ := strconv.Atoi(testCount)
	if iTestCount > 10 {
		errmsg.Abort(c, errmsg.FAIL, "验证码尝试次数过多，请10分钟后重试")
		return
	}
	iTestCount = iTestCount + 1
	if err := myredis.Set(redisKey+":testcount", strconv.Itoa(iTestCount), time.Minute*10); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "设置短信验证码尝试参数失败")
		return
	}

	v := myredis.Get(redisKey)
	if v == "" {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请重试")
		return
	}
	if v != oReq.SmsCode {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	if exist, err := user.ExistsMobile(oReq.Mobile); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "查询手机号码出错")
		return
	} else {
		if exist {
			errmsg.Abort(c, errmsg.FAIL, "手机号码已经存在")
			return
		} else {
			if er := user.SetMobile(oReq.Mobile); er != nil {
				logger.Error(err)
				errmsg.Abort(c, errmsg.FAIL, "更新手机号码失败")
			} else {
				result := make(map[string]interface{})
				result["mobile"] = oReq.Mobile
				c.JSON(http.StatusOK, gin.H{
					"code":    code,
					"message": "号码绑定成功",
					"msg":     "号码绑定成功",
					"result":  result,
				})
				if err2 := myredis.Del(enums.RedisKeyEnum.SmsModifyPassword + user.Mobile); err2 != nil {
					logger.Error(err2)
				}
			}
		}
	}

}

func (obj userApi_) ChangePasswordByMobile(c *gin.Context) {
	var oReq resetPasswordReq

	err := c.ShouldBindBodyWith(&oReq, binding.JSON)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "获取数据失败")
		return
	}

	if oReq.Mobile == "" {
		errmsg.Abort(c, errmsg.FAIL, "请输入手机号码")
		return
	}
	var user model.User
	if err := user.GetByMobile(oReq.Mobile); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询数据失败")
		return
	}
	if user.ID == 0 {
		errmsg.Abort(c, errmsg.FAIL, "数据不存在")
		return
	}

	claims, _ := middleware.GetMyClaims(user.ID, "", "")

	c.Set("claims", &claims)
	obj.ChangePassword(c)
}

func (obj userApi_) ChangePassword(c *gin.Context) {
	var code int
	var msg string
	claims := c.Value("claims").(*middleware.MyClaims)
	if claims.UserId <= 0 {
		errmsg.Abort(c, errmsg.FAIL, "请先登录")
		return
	}

	var oReq resetPasswordReq
	err := c.ShouldBindBodyWith(&oReq, binding.JSON)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if len(oReq.SmsCode) != 4 {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	if len(oReq.Password) < 5 {
		errmsg.Abort(c, errmsg.FAIL, "密码不能小于5位")
		return
	}

	var user model.User
	if err := user.GetByID(claims.UserId); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "获取用户信息失败")
		return
	}

	redisKey := enums.RedisKeyEnum.SmsModifyPassword + user.Mobile
	testCount := myredis.Get(redisKey + ":testcount")
	if testCount == "" {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请10分钟后重试")
		return
	}
	iTestCount, _ := strconv.Atoi(testCount)
	if iTestCount > 10 {
		errmsg.Abort(c, errmsg.FAIL, "验证码尝试次数过多，请10分钟后重试")
		return
	}
	iTestCount = iTestCount + 1
	if err := myredis.Set(redisKey+":testcount", strconv.Itoa(iTestCount), time.Minute*10); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "设置短信验证码尝试参数失败")
		return
	}

	v := myredis.Get(redisKey)
	if len(v) == 0 {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请重试")
		return
	}
	if v != oReq.SmsCode {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	user.Password = oReq.Password
	if err := user.SetPassword(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "修改密码失败")
		return
	}

	//result := make(map[string]interface{})
	//result["username"] = oReq.Username

	c.JSON(http.StatusOK, gin.H{
		"code":    code,
		"message": "密码修改成功",
		"msg":     "密码修改成功",
	})

}

var UserApi userApi_

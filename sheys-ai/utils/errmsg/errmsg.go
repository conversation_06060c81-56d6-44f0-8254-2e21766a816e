package errmsg

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

const (
	SUCCESS = 0
	FAIL    = 1
)

func Success(c *gin.Context, msg string, result map[string]interface{}) {

	c.JSON(http.StatusOK, gin.H{
		"code":   SUCCESS,
		"result": result,
		"msg":    msg,
	})

	c.Abort()
}

func Abort(c *gin.Context, code int, msg string) {
	if code == SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"result": nil,
			"msg":    msg,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"result": nil,
			"msg":    msg,
		})
	}

	c.Abort()
}

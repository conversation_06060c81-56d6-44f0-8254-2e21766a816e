package enums

import "reflect"

type aigeRedisKeyEnum_ struct {
	RetouchIdList,
	RetouchPush, RetouchPop,
	UpScalingIn, UpScalingOut, UpScalingState string
}

func (c aigeRedisKeyEnum_) Get(id string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if typeVo.Field(i).Name == id {
			return vo.Field(i).Interface().(string)
		}
	}
	return ""
}

var AigcRedisKeyEnum = aigeRedisKeyEnum_{
	RetouchPush:    "aigc-worker:list:control-net-in",
	RetouchPop:     "aigc-worker:list:control-net-out:sheys",
	RetouchIdList:  "aigc-worker:list:design-id",
	UpScalingIn:    "aigc-worker:list:up-scaling-in",
	UpScalingOut:   "aigc-worker:list:up-scaling-out:sheys",
	UpScalingState: "aigc-worker:list:up-scaling-state",
}

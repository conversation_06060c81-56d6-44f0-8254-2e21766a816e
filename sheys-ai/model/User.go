package model

import (
	"errors"
	"fmt"
	"log"
	"sheys-ai/enums"
	"sheys-ai/utils/logger"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
)

type User struct {
	gorm.Model
	Username       string `gorm:"type:varchar(20);not null " json:"username" validate:"required,min=4,max=20" label:"用户名"`
	MasterId       uint   `gorm:"type:int;not null;default:0;comment:主账号id，主账号为0" json:"master_id"`
	Level          int    `json:"level" gorm:"type:tinyint;not null;default:0;comment:会员等级1铂金会员，2黄金会员"`
	Mobile         string `gorm:"type:varchar(20);not null;unique " json:"mobile" validate:"required,min=4,max=12" label:"手机号码"`
	Password       string `gorm:"type:varchar(500);not null;" json:"password" validate:"required,min=6,max=20" label:"密码"`
	Coin           int    `gorm:"type:int;not null;default:0;comment:钱币" json:"coin" validate:"required,gte=0" label:"钱币"`
	FrozenCoin     int    `gorm:"type:int;not null;default:0;comment:冻结资金" json:"frozen_coin" validate:"required,gte=0" label:"冻结钱币"`
	Role           int    `gorm:"type:int;not null;default:1;comment:权限" json:"role" validate:"required,gte=0" label:"角色码"`
	Avatar         string `gorm:"type:varchar(100);not null;default:'';comment:头像链接" json:"avatar"`
	Unionid        string `gorm:"type:varchar(50);comment:微信开放平台的唯一标识符" json:"unionid"`
	Openid         string `gorm:"type:varchar(50);comment:应用中用户唯一标识" json:"openid"`
	PhoneInfo      string `gorm:"type:json;comment:微信获取手机号码返回信息"json:"phone_info" `
	InvitationCode string `gorm:"type:varchar(50);not null;default:'';comment:邀请码" json:"invitation_code"`
	InvitedCode    string `gorm:"type:varchar(50);not null;default:'';comment:被邀请码" json:"invited_code"`
	InvitedUserId  uint   `gorm:"type:bigint;not null;default:0;comment:被邀请用户ID" json:"invited_user_id"`
	Plat           string `gorm:"type:varchar(50);not null;default:'';comment:来源平台" json:"plat"`
	Version        optimisticlock.Version
}

func (User) TableName() string {
	return "T_User"
}

func (o *User) GetByID(id uint) error {
	return db.First(o, id).Error
}

func (o *User) Del() error {
	return db.Debug().Delete(o).Error
}

func (o *User) GetByMobile(mobile string) error {
	var total int64
	err := db.Where("mobile = ?", mobile).Find(o).Count(&total).Error
	if total > 1 {
		return errors.New("存在多条数据")
	}
	return err
}

func (o *User) GetByUsername(username string) error {
	var total int64
	err := db.Select("*").Where("username = ?", username).Find(o).Count(&total).Error
	if total > 1 {
		return errors.New("存在多条数据")
	}
	return err
}

func (o *User) ExistsUsername(username string) (bool, error) {
	var user User
	if err := db.First(&user, "username = ?", username).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) ExistsMobile(mobile string) (bool, error) {
	var user User
	if err := db.First(&user, "mobile = ?", mobile).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) ExistsInvitationCode(invitationCode string) (bool, error) {
	var user User
	if err := db.First(&user, "invitation_code = ?", invitationCode).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) GetByOpenid(openid string) error {
	var total int64
	err := db.Select("*").Where("openid = ?", openid).Find(o).Count(&total).Error
	if total > 1 {
		return errors.New("存在多条数据")
	}
	return err
}

func (o *User) GetIchiMoreList(lastId uint, pageSize int) ([]User, error) {
	var ary []User

	tx := *db.Debug().Model(o).Where("id>? and coin<?", lastId, 100).Order("id asc")
	tx.Limit(pageSize).Scan(&ary)
	return ary, tx.Error
}

func (o *User) Save() error {
	return db.Save(o).Error
}

func (o *User) ResetHandrawFree() error {
	return db.Debug().Unscoped().Model(&User{}).Omit("version").Where("handraw_free < ?", 10).UpdateColumns(map[string]interface{}{"handraw_free": 10}).Error
}

func (o *User) SetInvitationCode(invitationCode string) error {
	return db.Model(o).Unscoped().Updates(User{InvitationCode: invitationCode}).Error
}

func (o *User) SetMobile(mobile string) error {
	if o.Mobile == "" {
		return db.Debug().Unscoped().Model(o).Updates(User{Mobile: mobile}).Error
	} else {
		return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
			changeRecord := ChangeRecord{
				UserId:      o.ID,
				ChangeType:  enums.ChangeRecordType.UserMobile,
				DataId:      o.ID,
				OriginData:  fmt.Sprintf(`{"mobile":"%s"}`, o.Mobile),
				CurrentData: fmt.Sprintf(`{"mobile":"%s"}`, mobile),
			}

			if err := changeRecord.New(tx); err != nil {
				logger.Error(err)
				return err
			}

			if err := tx.Debug().Unscoped().Model(o).Updates(User{Mobile: mobile}).Error; err != nil {
				logger.Error(err)
				return err
			}
			return nil
		})
	}
}

func (o *User) SetPhoneInfo(phoneInfo string) error {
	return db.Debug().Unscoped().Model(o).Updates(User{PhoneInfo: phoneInfo}).Error
}

func (o *User) SetAvatar(avatar string) error {
	return db.Debug().Model(o).Unscoped().Updates(User{Avatar: avatar}).Error
}

func (o *User) SetUsername(username string) error {
	return db.Model(o).Unscoped().Updates(User{Username: username}).Error
}

func (o *User) SetPassword() error {
	if o.Password == "" {
		return errors.New("密码不能为空")
	}
	if len(o.Password) > 30 {
		return errors.New("该密码已加密过")
	}
	o.Password = ScryptPw(o.Password)
	return db.Debug().Unscoped().Model(o).Update("password", o.Password).Error
}

func (o *User) CheckPassword(pw string) error {
	return bcrypt.CompareHashAndPassword([]byte(o.Password), []byte(pw))
}

func (u *User) GetSubUsers(masterId uint) ([]User, error) {
	arr := make([]User, 0)
	tx := db.Debug().Model(u).Where("master_id = ?", masterId).Scan(&arr)
	return arr, tx.Error
}

func (u *User) GetUserList(username string, mobile string, orderBy string, page int, pageSize int) ([]User, int64, error) {
	arr := make([]User, 0)
	var total int64

	tx := db.Debug().Model(u).Where("username like ? and mobile like ?", "%"+username+"%", "%"+mobile+"%")
	if orderBy != "" {
		tx.Order(orderBy)
	} else {
		tx.Order("id desc")
	}

	if err = tx.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(&arr)
	return arr, total, tx.Error

}

// BeforeCreate 密码加密&权限控制
func (u *User) BeforeCreate(_ *gorm.DB) (err error) {
	u.Password = ScryptPw(u.Password)
	return nil
}

// ScryptPw 生成密码
func ScryptPw(password string) string {
	const cost = 10

	HashPw, err := bcrypt.GenerateFromPassword([]byte(password), cost)
	if err != nil {
		log.Println(err)
	}

	return string(HashPw)
}

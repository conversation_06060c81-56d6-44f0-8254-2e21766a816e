package model

import (
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type ConsumeProduct struct {
	gorm.Model
	BundleId      string          `json:"bundle_id" gorm:"type:varchar(50);not null;default:'';comment:应用唯一标识"`
	GroupName     string          `json:"group_name" gorm:"type:varchar(50);not null;default:'';comment:分组名称"`
	ShowTitle     string          `json:"show_title" gorm:"type:varchar(50);not null;default:'';comment:显示名称"`
	Description   string          `json:"description" gorm:"type:varchar(50);not null;default:'';comment:商品描述"`
	Price         decimal.Decimal `json:"price" gorm:"type:decimal(16,2);not null;default:0;comment:价格"`
	State         int             `json:"state" gorm:"type:tinyint;not null;default:0;comment:状态 0新创建 1生效中"`
	ConsumingTime int             `json:"consuming_time" gorm:"type:int;not null;default:0;comment:消耗时间（s）"`
}

func (ConsumeProduct) TableName() string {
	return "T_ConsumeProduct"
}

func (o *ConsumeProduct) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *ConsumeProduct) GetList() ([]ConsumeProduct, int64, error) {
	arr := make([]ConsumeProduct, 0)
	var total int64

	tx := db.Debug().Model(o).Where("state = 1")
	if err = tx.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	tx.Scan(&arr)
	return arr, total, tx.Error

}

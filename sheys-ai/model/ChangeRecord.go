package model

import "gorm.io/gorm"

type ChangeRecord struct {
	gorm.Model
	UserId      uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	ChangeType  int    `gorm:"type:int;not null;default:0;comment:更改类型" json:"change_type" `
	DataId      uint   `json:"data_id" gorm:"type:bigint;not null;default:0;comment:相关记录的ID"`
	OriginData  string `gorm:"type:json;comment:原始数据" json:"origin_data"`
	CurrentData string `gorm:"type:json;comment:当前数据" json:"current_data"`
}

func (ChangeRecord) TableName() string {
	return "T_ChangeRecord"
}

func (o *ChangeRecord) Save() error {
	return db.Save(o).Error
}

func (o *ChangeRecord) New(tx *gorm.DB) error {
	if err := tx.Save(o).Error; err != nil {
		return err
	}
	return nil
}

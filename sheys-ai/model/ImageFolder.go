package model

import (
	"time"

	"gorm.io/gorm"
)

type FolderResult struct {
	ID            uint   `json:"id"`
	FolderName    string `json:"folder_name"`
	User          uint   `json:"user"`
	State         int    `json:"state"`
	RetentionTime int64  `json:"retention_time"`
}

type ImageFolder struct {
	gorm.Model
	UserId     uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	MasterId   uint   `json:"master_id" gorm:"type:bigint;not null;default:0;comment:主账号ID"`
	FolderName string `json:"folder_name" gorm:"type:varchar(50);not null;default:'';comment:文件夹名字"`
	Uuid       string `json:"uuid" gorm:"type:varchar(50);not null;default:'';comment:唯一字符串"`
	FolderPath string `json:"folder_path" gorm:"type:varchar(100);not null;default:'';comment:文件夹路径"`
	State      int    `json:"state" gorm:"type:tinyint;not null;default:0;comment:修图状态,1上传中,2修图中,200已完成"`
	OrderNo    string `json:"order_no" gorm:"type:varchar(50);not null;default:'';comment:业务单号"`
}

func (ImageFolder) TableName() string {
	return "T_ImageFolder"
}

func (o *ImageFolder) Save() error {
	return db.Save(o).Error
}

func (o *ImageFolder) New(tx *gorm.DB) error {
	return tx.Save(o).Error
}

func (o *ImageFolder) GetByID(id uint) error {
	return db.First(o, id).Error
}

func (o *ImageFolder) GetByUuid(uuid string) error {
	return db.First(o, "uuid = ?", uuid).Error
}

func (o *ImageFolder) Del() error {
	return db.Debug().Delete(&o).Error
}

func (o *ImageFolder) GetListByUser(userId uint, folderName string, start int64, end int64, search string, page int, pageSize int) ([]FolderResult, int64, error) {
	folders := make([]ImageFolder, 0)
	arr := make([]FolderResult, 0)
	var total int64
	tx := db.Debug().Model(o)
	if userId > 0 {
		tx.Where("user_id=?", userId).Order("id desc")
	}
	if folderName != "" {
		tx.Where("folder_name like ?", "%"+folderName+"%")
	}
	if start > 0 && end > start {
		tx.Where("created_at > ? and created_at < ?", time.Unix(start/1000, 0), time.Unix(end/1000, 0))
	}
	if search != "" {
		tx.Where("folder_name like ?", "%"+search+"%")
	}
	tx.Order("id desc")
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return nil, 0, err
		}
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(&folders)
	for _, fd := range folders {
		var lastTime int64 = 3600 * 24 * 30
		//三十天自动删除
		if time.Now().Unix()-fd.CreatedAt.Unix() > lastTime {
			if err := fd.Del(); err != nil {
				return nil, 0, err
			}
		} else {
			arr = append(arr, FolderResult{
				ID:            fd.ID,
				FolderName:    fd.FolderName,
				State:         fd.State,
				User:          fd.UserId,
				RetentionTime: lastTime - (time.Now().Unix() - fd.CreatedAt.Unix()),
			})
		}
	}

	return arr, total, tx.Error
}

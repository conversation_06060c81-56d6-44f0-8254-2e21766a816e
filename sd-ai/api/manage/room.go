package manage

import (
	"center-ai/enums"
	"center-ai/middleware"
	"center-ai/model"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"center-ai/utils/myhttp"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"net/url"
)

type roomApi_ struct {
}

var RoomApi roomApi_

type roomAddReq struct {
	ID      uint   `json:"id"`
	EnterId uint   `json:"enter_id"`
	Monitor int    `json:"monitor"`
	Remark  string `json:"remark"`
}

type roomListReq struct {
	EnterId  uint   `json:"enter_id"`
	Title    string `json:"title"`
	Monitor  int    `json:"monitor"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type roomListItem struct {
	ID        uint           `json:"id"`
	EnterId   uint           `json:"enter_id"`
	RoomId    string         `json:"room_id"`
	RoomTitle string         `json:"room_title"`
	NickName  string         `json:"nick_name"`
	Avatar    string         `json:"avatar"`
	Cover     string         `json:"cover"`
	Remark    string         `json:"remark"`
	Monitor   int            `json:"monitor"`
	AckTime   model.JsonTime `json:"ack_time"`
}

type roomSearchReq struct {
	Kw string `json:"kw"`
}

func (obj roomApi_) Add(c *gin.Context) {
	var code int
	var msg string

	var oReq roomAddReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	o := model.DouyinRoom{
		EnterId: oReq.EnterId,
		Remark:  oReq.Remark,
		Monitor: oReq.Monitor,
	}

	msg = "添加成功"
	if oReq.ID > 0 {
		msg = "更新成功"
		if err := o.GetById(oReq.ID); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "风格获取失败")
			return
		}
		logger.Info("更新ID：", o.ID, "  更新前RoomId：", o.RoomId)

		o.Remark = oReq.Remark
		o.Monitor = oReq.Monitor
	} else {
		var oo model.DouyinRoom
		if err := oo.GetByEnterId(oReq.EnterId); err != nil {
			if err == gorm.ErrRecordNotFound {
			} else {
				logger.Error(err)
			}
		}
		if oo.ID > 0 {
			logger.Error("EnterId已经存在", oo)
			errmsg.Abort(c, errmsg.FAIL, "EnterId已经存在")
			return
		}
	}

	if err := o.Save(); err != nil || o.ID == 0 {
		logger.Error("数据保存失败", err, o.ID)
		code = errmsg.FAIL
		msg = "数据保存失败"
		errmsg.Abort(c, code, msg)
		return
	}

	result := make(map[string]interface{})
	result["room_id"] = o.ID

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj roomApi_) List(c *gin.Context) {
	var code int
	var oReq roomListReq
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	var room model.DouyinRoom

	var arr = make([]roomListItem, 0)
	total, err := room.GetList(&arr, oReq.EnterId, oReq.Title, oReq.Monitor, oReq.Page, oReq.PageSize)
	if err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
		return
	}

	result := make(map[string]interface{})
	result["items"] = arr
	result["total"] = total
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

func (obj roomApi_) Search(c *gin.Context) {
	var code int
	var oReq roomSearchReq
	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}
	if err := c.ShouldBindJSON(&oReq); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "数据获取失败")
		return
	}

	rawUrl := fmt.Sprintf("https://www.douyin.com/search/%s", url.PathEscape(oReq.Kw))
	rawUrl = "https://v.douyin.com/ier39rRP/"
	params := url.Values{}
	//params.Add("aid", "6383")
	resText, err := myhttp.Get(rawUrl, params)
	if err != nil {
		logger.Error(err)
	}
	logger.Info(resText)

	var arr = make([]roomListItem, 0)

	result := make(map[string]interface{})
	result["items"] = arr
	result["total"] = 0
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "",
			"result": result,
		})
	}
}

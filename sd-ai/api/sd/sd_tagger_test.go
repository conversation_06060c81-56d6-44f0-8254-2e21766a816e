package sd

import (
	"center-ai/service"
	"center-ai/utils/logger"
	"center-ai/utils/tools"
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"strings"
	"testing"
)

type TaggerCaption struct {
	Tag    map[string]float64 `json:"tag"`
	Rating map[string]float64 `json:"rating"`
}

func isGeneral(caption TaggerCaption) bool { //一般（正常）的
	if val, ok := caption.Rating["general"]; ok {
		if val >= 0.6 {
			return true
		}
	}
	return false
}

func isExplicit(caption TaggerCaption) bool { //露骨（色情）的
	if val, ok := caption.Rating["explicit"]; ok {
		if val >= 0.6 {
			return true
		}
	}
	return false
}

func isQuestionable(caption TaggerCaption) bool { //可疑（有争议）的
	if val, ok := caption.Rating["questionable"]; ok {
		if val >= 0.6 {
			return true
		}
	}
	return false
}

func isSensitive(caption TaggerCaption) bool { //敏感内容的
	if val, ok := caption.Rating["sensitive"]; ok {
		if val >= 0.6 {
			return true
		}
	}
	return false
}

func isLineArt(caption TaggerCaption) bool {

	//greyscale灰度的,  monochrome单色  lineart
	num := 0
	if val, ok := caption.Tag["greyscale"]; ok {
		if val >= 0.3 {
			num += 1
		}
	}
	if val, ok := caption.Tag["monochrome"]; ok {
		if val >= 0.3 {
			num += 1
		}
	}
	if val, ok := caption.Tag["lineart"]; ok {
		if val >= 0.3 {
			return true
		}
	}
	if num == 2 {
		return true
	}

	return false
}

func getTags(caption TaggerCaption) string {
	aryTag := make([]string, 0)
	for key, value := range caption.Tag {
		if value >= 0.3 {
			aryTag = append(aryTag, key)
		}
	}
	return strings.Join(aryTag, ",")
}

func testImage() string {
	// 1. 读取图片文件
	imagePath := "/Users/<USER>/Downloads/0180925975979ca8012193a3e2555b.j.jpg" //线稿
	imagePath = "/Users/<USER>/Downloads/709664c39d82db12.jpgl.jpg"             //实景
	imagePath = "/Users/<USER>/Downloads/u2376702100,692645279fm253fmtaut.jpg"  //白膜
	imageFile, err := os.Open(imagePath)
	if err != nil {
		fmt.Println("Error opening image file:", err)
		return ""
	}
	defer imageFile.Close()

	imageData, err := io.ReadAll(imageFile)
	if err != nil {
		fmt.Println("Error reading image file:", err)
		return ""
	}

	// 2. 转换为 Base64 编码
	base64String := base64.StdEncoding.EncodeToString(imageData)

	// 打印 Base64 编码的字符串
	//fmt.Println("Base64 encoded image:", base64String)
	//base64String = "data:image/jpeg;base64," + base64String
	return base64String
}

func Test(t *testing.T) {

	parameters := make(map[string]interface{})
	parameters["image"] = testImage()
	parameters["model"] = "wd14-vit.v2"
	parameters["threshold"] = 0.3
	oReq := service.SdInput{
		SdServer:   "https://96c68c4c160447f0823061a9e52fe48388.hz01.suanyun.cn/",
		Parameters: tools.GetJsonFromStruct(parameters),
	}

	ss, err := service.SdService.SdTagger(oReq)
	if err != nil {
		logger.Error(err, "   ", oReq.SdServer)
		return
	} else {
		mm := tools.GetMapFromJson(ss)
		fmt.Println(mm)
		if _, ok := mm["caption"]; ok {
			mmCaption := mm["caption"].(map[string]interface{})
			strCaptioin := tools.GetJsonFromStruct(mmCaption)
			var caption TaggerCaption
			if err := tools.GetStructFromJson(&caption, strCaptioin); err != nil {
				logger.Error(err)
			}
			fmt.Println("caption:", caption)

			fmt.Println("isGeneral:", isGeneral(caption))

			fmt.Println("isLineArt:", isLineArt(caption))
			fmt.Println("tags:", getTags(caption))

		}
		logger.Info("sssssssss:", ss)
		return
	}
}

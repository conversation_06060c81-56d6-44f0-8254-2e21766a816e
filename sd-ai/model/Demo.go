package model

import (
	"errors"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
)

type Demo struct {
	gorm.Model
	RoleCode   string `json:"role_code" gorm:"type:varchar(20);not null"`
	RoleName   string `json:"role_name" gorm:"type:varchar(20);not null"`
	Permission string `json:"permission" gorm:"type:json;';comment:权限数组"`
	Remark     string `json:"remark" gorm:"type:varchar(50);not null;default:'';comment:备注" `
	Version    optimisticlock.Version
}

func (Demo) TableName() string {
	return "T_Demo"
}

func (o *Demo) GetById(id uint) error {
	return db.First(o, id).Error
}

//调用方式
//var role model.Role
//var arr = make([]roleListItem, 0)
//total, err := role.GetList(&arr, req.UserId, req.Mobile, req.Page, req.PageSize)
//if err != nil {
//logger.Error(err)
//errmsg.Abort(c, errmsg.FAIL, "数据查询失败")
//return
//}
func (o *Demo) GetList(dest interface{}, projectId uint, roleName string, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o)
	if projectId > 0 {
		tx.Where("project_id=?", projectId)
	}
	if roleName != "" {
		tx.Where("role_name like ?", roleName)
	}
	if page == 1 {
		if err = tx.Count(&total).Error; err != nil {
			return 0, err
		}
	}
	tx.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (o *Demo) Save() error {
	return db.Save(o).Error
}

func (o *Demo) SetUpscalePath(id uint, level int, path string) error {
	if level == 1 {
		return db.Model(o).Where("id = ?", id).Updates(map[string]interface{}{"path1": path, "scale_at": nil}).Error
	} else if level == 2 {
		return db.Model(o).Where("id = ?", id).Updates(map[string]interface{}{"path2": path, "scale_at": nil}).Error
	} else {
		return errors.New("未找到level对应字段")
	}
}

func (o *Demo) DelDigital() error {
	return db.Model(o).Omit("version").UpdateColumns(map[string]interface{}{"digital_id": 0, "digital_uuid": ""}).Error
}

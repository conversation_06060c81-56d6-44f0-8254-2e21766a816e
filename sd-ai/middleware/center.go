package middleware

import (
	"bytes"
	"center-ai/enums"
	"center-ai/utils/config"
	"center-ai/utils/errmsg"
	"center-ai/utils/logger"
	"encoding/json"
	"errors"
	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"io/ioutil"
	"net/http"
	"strings"
	"unsafe"
)

type CenterClaims struct {
	UserId       uint     `json:"user_id"`
	Mobile       string   `json:"mobile"`
	Username     string   `json:"username"`
	PermOperates []string `json:"perm_operates"`
	jwt.StandardClaims
}

// JwtToken jwt中间件
func JwtTokenCenter() gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := SetPermissionOperates(c); err != nil {
			logger.Error(err)
			return
		}
		c.Next()
	}
}

// CreateToken 生成token
func (j *JWT) CreateCenterToken(claims CenterClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.JwtKey)
}

// ParserToken 解析token
func (j *JWT) ParserCenterToken(tokenString string) (*CenterClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &CenterClaims{}, func(token *jwt.Token) (interface{}, error) {
		return j.JwtKey, nil
	})

	if err != nil {
		logger.Error(err, "   tokenString:===", tokenString, "===")
		if ve, ok := err.(*jwt.ValidationError); ok {
			if ve.Errors&jwt.ValidationErrorMalformed != 0 {
				return nil, TokenMalformed
			} else if ve.Errors&jwt.ValidationErrorExpired != 0 {
				// Token is expired
				return nil, TokenExpired
			} else if ve.Errors&jwt.ValidationErrorNotValidYet != 0 {
				return nil, TokenNotValidYet
			} else {
				return nil, TokenInvalid
			}
		}
	}

	if token != nil {
		if claims, ok := token.Claims.(*CenterClaims); ok && token.Valid {
			return claims, nil
		}
		logger.Error(TokenInvalid, token)
		return nil, TokenInvalid
	}
	logger.Error(TokenInvalid, token)
	return nil, TokenInvalid
}

func (claims *CenterClaims) PermissionEnough(operate string) bool {
	if operate == "" {
		return false
	}
	if claims.PermOperates == nil {
		return false
	}
	for _, tmp := range claims.PermOperates {
		if tmp == enums.PermissionOperateEnum.All {
			return true
		}
		if tmp == operate {
			return true
		}
	}
	return false
}

func SetPermissionOperates(c *gin.Context) error {
	path := c.Request.URL.Path
	path = strings.Replace(path, "/api/", "", -1)
	path = strings.Replace(path, "/api_test/", "", -1)
	path = strings.Replace(path, "/api_online/", "", -1)
	if strings.Contains(path, "/permission/verify") {
		tokenHeader := c.Request.Header.Get("Authorization")
		logger.Info(path, " verify流程 tokenHeader:", tokenHeader)
		if tokenHeader == "" {
			c.JSON(http.StatusOK, gin.H{
				"code": errmsg.TokenInvalid,
				"msg":  "Token不存在，请重新登录",
			})
			c.Abort()
			return errors.New("Token不存在，请重新登录")
		}

		checkToken := strings.Split(tokenHeader, " ")
		token := checkToken[0]
		if len(checkToken) > 1 {
			token = checkToken[1]
		}

		j := NewJWT()
		// 解析token
		claims, err := j.ParserCenterToken(token)
		if err != nil {
			if err == TokenExpired {
				c.JSON(http.StatusOK, gin.H{
					"code": errmsg.TokenInvalid,
					"msg":  "token授权已过期,请重新登录",
					"data": nil,
				})
				c.Abort()
				return errors.New("token授权已过期,请重新登录")
			}
			// 其他错误
			c.JSON(http.StatusOK, gin.H{
				"code": errmsg.TokenInvalid,
				"msg":  err.Error(),
			})
			c.Abort()
			return err
		} else {
			c.Set("center_claims", claims)
		}
		return nil
	} else {
		logger.Info(path, " 开始获取权限")
		centerClaims := getPermissionOperates(path, c.Request.Header.Get("Authorization"))
		logger.Info(path, "  aryPermission:", centerClaims)
		c.Set("center_claims", centerClaims)
	}
	return nil
}

func getPermissionOperates(routePath string, token string) *CenterClaims {

	// 创建一个临时结构体，并进行初始化
	response := struct {
		Code         int          `json:"code"`
		Msg          string       `json:"msg"`
		CenterClaims CenterClaims `json:"center_claims"`
	}{}
	song := make(map[string]string)
	song["route_path"] = routePath
	song["bundle"] = config.Bundle
	bytesData, _ := json.Marshal(song)

	if config.CenterServer == "" {
		logger.Error("config.CenterServer 为空")
		return &response.CenterClaims
	}

	// 创建请求对象
	verifyUrl := config.CenterServer + "v1/permission/verify"
	req, err := http.NewRequest("POST", verifyUrl, bytes.NewBuffer(bytesData))
	if err != nil {
		logger.Error(err)
		return &response.CenterClaims
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	req.Header.Set("Authorization", token) // 设置其他自定义的请求头

	// 发送请求
	client := http.DefaultClient
	res, err := client.Do(req)
	if err != nil {
		logger.Error(err)
		return &response.CenterClaims
	}

	defer res.Body.Close()

	content, err := ioutil.ReadAll(res.Body)
	if err != nil {
		logger.Error(err)
		return &response.CenterClaims
	}
	str := *(*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	logger.Info("CenterStr:", str)
	// 解析JSON字符串到临时结构体
	if err := json.Unmarshal([]byte(str), &response); err != nil {
		logger.Error(err)
	}
	return &response.CenterClaims
}

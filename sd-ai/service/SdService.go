package service

import (
	"bytes"
	"center-ai/enums"
	"center-ai/model"
	"center-ai/utils/config"
	"center-ai/utils/logger"
	"center-ai/utils/myimg"
	"center-ai/utils/myredis"
	"center-ai/utils/tools"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unsafe"
)

type sd_ struct {
	SdServer   []string
	SdCurIndex int
	SdPause    bool
}

var SdService sd_

const (
	SdServer  = "http://*************:7887/"
	SdServer1 = "http://**************:7860/"
)

type SdInput struct {
	CustomApp    string                 `json:"custom_app"`
	CustomData   string                 `json:"custom_data"`
	CustomMd5    string                 `json:"custom_md5"`
	CustomPath   string                 `json:"custom_path"`
	Sdapi        string                 `json:"sdapi"`
	SdAutoNumber int64                  `json:"sd_auto_number"`
	SdServer     string                 `json:"sd_server"`
	SdRunServer  string                 `json:"sd_run_server"`
	Parameters   string                 `json:"parameters"`
	ImagesPath   map[string]string      `json:"images_path"`
	TraceId      string                 `json:"trace_id"`
	JumpTicket   string                 `json:"jump_ticket"`
	CreateAt     int64                  `json:"create_at"`
	Options      map[string]interface{} `json:"options"`
	TryCount     int                    `json:"try_count"` //失败后尝试次数
}

type SdOutput struct {
	CustomApp    string  `json:"custom_app"`
	CustomData   string  `json:"custom_data"`
	CustomMd5    string  `json:"custom_md5"`
	CustomPath   string  `json:"custom_path"`
	Sdapi        string  `json:"sdapi"`
	SdServer     string  `json:"sd_server"`
	Progress     float64 `json:"progress"`
	Result       string  `json:"result"`
	TraceId      string  `json:"trace_id"`
	CreateAt     int64   `json:"create_at"`
	ExecuteAt    int64   `json:"execute_at"`
	ExecuteTimes int64   `json:"execute_times"`
	ErrorInfo    string  `json:"error_info"`
}

type Txt2Img struct {
	EnableHr          bool     `json:"enable_hr"`
	DenoisingStrength float64  `json:"denoising_strength"`
	FirstphaseWidth   int      `json:"firstphase_width"`
	FirstphaseHeight  int      `json:"firstphase_height"`
	HrScale           int      `json:"hr_scale"`
	HrUpscaler        string   `json:"hr_upscaler"`
	HrSecondPassSteps int      `json:"hr_second_pass_steps"`
	HrResizeX         int      `json:"hr_resize_x"`
	HrResizeY         int      `json:"hr_resize_y"`
	HrSamplerName     string   `json:"hr_sampler_name"`
	HrPrompt          string   `json:"hr_prompt"`
	HrNegativePrompt  string   `json:"hr_negative_prompt"`
	Prompt            string   `json:"prompt"`
	Styles            []string `json:"styles"`
	Seed              int      `json:"seed"`
	Subseed           int      `json:"subseed"`
	SubseedStrength   int      `json:"subseed_strength"`
	SeedResizeFromH   int      `json:"seed_resize_from_h"`
	SeedResizeFromW   int      `json:"seed_resize_from_w"`
	SamplerName       string   `json:"sampler_name"`
	BatchSize         int      `json:"batch_size"`
	NIter             int      `json:"n_iter"`
	Steps             int      `json:"steps"`
	CfgScale          int      `json:"cfg_scale"`
	Width             int      `json:"width"`
	Height            int      `json:"height"`
	RestoreFaces      bool     `json:"restore_faces"`
	Tiling            bool     `json:"tiling"`
	DoNotSaveSamples  bool     `json:"do_not_save_samples"`
	DoNotSaveGrid     bool     `json:"do_not_save_grid"`
	NegativePrompt    string   `json:"negative_prompt"`
	Eta               int      `json:"eta"`
	SMinUncond        int      `json:"s_min_uncond"`
	SChurn            int      `json:"s_churn"`
	STmax             int      `json:"s_tmax"`
	STmin             int      `json:"s_tmin"`
	SNoise            int      `json:"s_noise"`
	OverrideSettings  struct {
	} `json:"override_settings"`
	OverrideSettingsRestoreAfterwards bool          `json:"override_settings_restore_afterwards"`
	ScriptArgs                        []interface{} `json:"script_args"`
	SamplerIndex                      string        `json:"sampler_index"`
	ScriptName                        string        `json:"script_name"`
	SendImages                        bool          `json:"send_images"`
	SaveImages                        bool          `json:"save_images"`
	AlwaysonScripts                   struct {
	} `json:"alwayson_scripts"`
}

type OverrideSetting struct {
	SdModelCheckpoint string `json:"sd_model_checkpoint"`
}

type ControlnetUnit struct {
	InputImage    string `json:"input_image"`
	Mask          string `json:"mask"`
	Module        string `json:"module"`
	Model         string `json:"model"`
	Weight        int    `json:"weight"`
	ResizeMode    int    `json:"resize_mode"`
	Lowvram       string `json:"lowvram"`
	ProcessorRes  int    `json:"processor_res"`
	ThresholdA    int    `json:"threshold_a"`
	ThresholdB    int    `json:"threshold_b"`
	Guidance      int    `json:"guidance"`
	GuidanceStart int    `json:"guidance_start"`
	GuidanceEnd   int    `json:"guidance_end"`
	Guessmode     string `json:"guessmode"`
	PixelPerfect  string `json:"pixel_perfect"`
	ControlMode   int    `json:"control_mode"`
}
type SdServerItem struct {
	ApiBaseUrl         string  `json:"api_base_url"`         //
	CurTaskId          string  `json:"cur_task_id"`          //当前任务ID
	HeartbeatUnixMilli int64   `json:"heartbeat_unix_milli"` //心跳包
	LastIdleUnixMilli  int64   `json:"last_idle_unix_milli"` //最后空闲时间(秒)
	ExtractedUnixMilli int64   `json:"extracted_unix_milli"` //被提取时间
	ExecuteAt          int64   `json:"execute_at"`
	CompletedAt        int64   `json:"completed_at"`
	ExecuteTimes       int64   `json:"execute_times"`
	ExecuteCount       uint    `json:"execute_count"` //总执行次数
	State              int     `json:"state"`
	ThreadId           string  `json:"thread_id"` //线程ID
	ErrCount           int     `json:"err_count"` //连续出错次数
	Progress           float64 `json:"progress"`
	Pause              bool    `json:"pause"` //是否暂停
}

func (d *sd_) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("SdService奔溃:", e)
		}
	}()
	logger.Info("SdService.Run 开始循环")
	if d.SdServer == nil {
		if err := d.InitSdServer(); err != nil {
			logger.Error("初始化Sd服务列表失败", err)
			return
		}
	}

	for i := 0; i < len(d.SdServer); i++ {
		go d.RunServer(d.SdServer[i])
	}
	for {
		time.Sleep(time.Second * 15)
		for _, key := range d.SdServer {
			sdServerItem := d.GetSdServerByKey(key)
			if sdServerItem == nil {
				logger.Error("sdServerItem==nil", key)
				continue
			}
			spn := time.Now().UnixMilli() - sdServerItem.HeartbeatUnixMilli
			if spn > 1000*120 {
				logger.Error("进程有问题 ", key, " spn:", spn/1000, "  HeartbeatUnixMilli:", sdServerItem.HeartbeatUnixMilli)
			}
		}
		//logger.Info("检测进程心跳完成，延迟15秒")
		//time.Sleep(time.Second * 15)
	}
}

func (d *sd_) InitSdServer() error {
	ary := make([]string, 0)
	if config.Env == enums.EnvEnum.ONLINE || config.Env == enums.EnvEnum.PRODUCTION {
		if config.Env == enums.EnvEnum.PRODUCTION {
			ary = append(ary, "http://10.20.101.132:7860/") //ary = append(ary, "http://192.168.200.98:7860/")
			ary = append(ary, "http://10.20.101.132:7861/") //ary = append(ary, "http://192.168.200.98:7861/")

			ary = append(ary, "http://10.20.101.131:7860/") //ary = append(ary, "http://192.168.200.99:7860/")
			ary = append(ary, "http://10.20.101.131:7861/") //ary = append(ary, "http://192.168.200.99:7861/")
		} else {

			//ary = append(ary, "http://192.168.200.98:7860/")
			//ary = append(ary, "http://192.168.200.98:7861/")
			ary = append(ary, "http://10.20.101.132:7862/") //ary = append(ary, "http://192.168.200.98:7862/")
			ary = append(ary, "http://10.20.101.132:7863/") //ary = append(ary, "http://192.168.200.98:7863/")
			//ary = append(ary, "http://192.168.200.98:7864/")
			//ary = append(ary, "http://192.168.200.98:7865/")
			//ary = append(ary, "http://192.168.200.98:7866/")
			//ary = append(ary, "http://192.168.200.98:7867/")

			//ary = append(ary, "http://192.168.200.99:7860/")
			//ary = append(ary, "http://192.168.200.99:7861/")
			ary = append(ary, "http://10.20.101.131:7862/") //ary = append(ary, "http://192.168.200.99:7862/")
			ary = append(ary, "http://10.20.101.131:7863/") //ary = append(ary, "http://192.168.200.99:7863/")

			//ary = append(ary, "http://192.168.200.99:7864/")
			//ary = append(ary, "http://192.168.200.99:7865/")
			//ary = append(ary, "http://192.168.200.99:7866/")
			//ary = append(ary, "http://192.168.200.99:7867/")

			//ary = append(ary, "http://***************:7860/")
			//ary = append(ary, "http://***************:7861/")
			//ary = append(ary, "http://***************:7862/")
			//ary = append(ary, "http://***************:7863/")
			//ary = append(ary, "http://***************:7864/")
			//ary = append(ary, "http://***************:7865/")
			//ary = append(ary, "http://***************:7866/")
			//ary = append(ary, "http://***************:7867/")

			//ary = append(ary, "http://***************:10088/")
		}
		//ary = append(ary, "http://**************:7860/")
	} else {
		ary = append(ary, "http://*************:7887/")
		//ary = append(ary, "http://*************:7860/") 对应96的IP
	}

	d.SdServer = ary
	for _, key := range ary {
		sdServerItem := SdServerItem{
			ApiBaseUrl:        key,
			LastIdleUnixMilli: time.Now().UnixMilli(),
			State:             enums.SdServerStateEnum.Idle,
		}
		sdServerItemPoint := d.GetSdServerByKey(key)
		if sdServerItemPoint == nil {
			if err := d.SaveSdServerByKey(sdServerItem); err != nil {
				logger.Error(err)
			}
		} else {
			sdServerItem.HeartbeatUnixMilli = 0
			if err := d.SaveSdServerByKey(sdServerItem); err != nil {
				logger.Error(err)
			}
		}
	}
	return nil
}

func (d *sd_) RunServer(sdServerKey string) error {
	defer func() {
		if e := recover(); e != nil {
			logger.Error(e, "RunServer奔溃:", sdServerKey)
		}
	}()
	logger.Info("启动线程", sdServerKey)
	sdServerItemPoint := d.GetSdServerByKey(sdServerKey)
	if sdServerItemPoint == nil {
		logger.Error("sdServerItemPoint==nil", sdServerKey)
		return errors.New("sdServerItemPoint==nil")
	}
	sdServerItem := *sdServerItemPoint
	if sdServerItem.ThreadId == "" {
		sdServerItem.ThreadId = tools.GetUuid()
	}
	sdServerItem.HeartbeatUnixMilli = time.Now().UnixMilli()
	sdServerItem.ExtractedUnixMilli = time.Now().UnixMilli()
	sdServerItem.State = enums.SdServerStateEnum.Extracted
	if err := d.SaveSdServerByKey(sdServerItem); err != nil {
		logger.Error("保存SdServerItem到redis失败", err, sdServerItem.ApiBaseUrl)
	}

	for {
		if tmp, err := d.ChangeSdServerHeartbeat(sdServerKey); err != nil {
			logger.Error(err)
		} else {
			sdServerItem = *tmp
		}

		if d.SdPause { //系统暂停标记，延迟10秒
			time.Sleep(time.Second * 10)
			continue
		}

		taskValue := d.GetTarget(sdServerKey)

		if sdServerKey == "http://***************:10088/" && taskValue != "" {
			logger.Info("该机器已经下架 taskValue:", taskValue)
			continue
		}

		if sdServerItem.State == enums.SdServerStateEnum.Pause { //单任务暂停中 延迟15秒
			time.Sleep(time.Second * 15)
			continue
		}
		if taskValue == "" {

			if sdServerItem.Pause { //单任务暂停中 延迟15秒
				time.Sleep(time.Second * 15)
				continue
			}

			if value, err := d.PopTask(); err != nil {
				logger.Error(err, value)
				continue
			} else {
				taskValue = value
			}

			if sdServerKey == "http://***************:10088/" && taskValue != "" {
				logger.Info("该机器已经下架 taskValue:", taskValue)
				continue
			}

		}

		if taskValue == "" {
			continue
		}
		var sdInput SdInput
		if err := tools.GetStructFromJson(&sdInput, taskValue); err != nil {
			logger.Error(sdInput.TraceId, err, taskValue)
			continue
		}
		if sdInput.SdServer != "" && sdInput.SdServer != sdServerItem.ApiBaseUrl {
			sdInput.SdRunServer = sdServerItem.ApiBaseUrl
			if err := d.PushTarget(sdInput, taskValue); err != nil {
				logger.Error(sdInput.TraceId, err)
			}
			continue
		}
		if sdInput.SdServer == "" {
			sdInput.SdServer = sdServerItem.ApiBaseUrl
		}

		curTaskId := sdInput.CustomMd5
		if err := d.ChangeSdServerDrawing(sdServerKey, curTaskId); err != nil {
			logger.Error(err)
		}

		logger.Info(sdInput.TraceId, " 开始请求Sd绘图json:", taskValue)
		go func() {
			d.DoProgress(curTaskId, sdInput)
		}()
		if err := d.Handle(sdInput); err != nil {
			logger.Error(sdInput.TraceId, err)
		}
	}
}

func (d *sd_) Handle(sdInput SdInput) error {
	if sdInput.SdAutoNumber > 0 {
		str := myredis.Get(enums.AigcRedisKeyEnum.SdTaskLastCompleteNumber)
		if str == "" {
			str = "0"
		}
		if num, err := strconv.ParseInt(str, 10, 64); err != nil {
			logger.Error(err, "  str:", str)
		} else {
			if sdInput.SdAutoNumber > num {
				if err = myredis.Set(enums.AigcRedisKeyEnum.SdTaskLastCompleteNumber, sdInput.SdAutoNumber, -1); err != nil {
					logger.Error(err, "  sdInput.SdAutoNumber:", sdInput.SdAutoNumber)
				}
			}
		}
	}

	parameters := sdInput.Parameters
	if sdInput.ImagesPath != nil {
		for key, val := range sdInput.ImagesPath {
			absolutePath := config.DiffusionFilePath + val
			if b, _ := tools.PathFileExists(absolutePath); b {
				base64, err := myimg.FileToBase64(absolutePath)
				if err != nil {
					logger.Error(err)
				}
				replaceKey := "{==={" + key + "}===}"
				//logger.Info("替换ImagesPath parameters:", parameters)
				logger.Info("替换ImagesPath replaceKey:", replaceKey)
				parameters = strings.Replace(parameters, replaceKey, base64, -1)
				if strings.Contains(key, "&") {
					replaceKey = strings.Replace(replaceKey, "&", "\\u0026", -1)
					logger.Info("替换ImagesPath replaceKey:", replaceKey)
					parameters = strings.Replace(parameters, replaceKey, base64, -1)
				}
			}
		}
	}

	relativePath := sdInput.CustomPath
	if !strings.HasPrefix(relativePath, sdInput.CustomApp+"/") {
		relativePath = sdInput.CustomApp + "/" + sdInput.CustomPath
	}
	absolutePath := config.DiffusionFilePath + relativePath
	ext := path.Ext(absolutePath)
	sdInputInfoPath := strings.Replace(absolutePath, ext, "_parameters.txt", -1)
	sdInputPath := strings.Replace(absolutePath, ext, "_sdinput.txt", -1)
	directory := filepath.Dir(absolutePath) // 获取目录路径
	// 创建目录，存在则不创建，不存在则创建
	if err := os.MkdirAll(directory, 0755); err != nil {
		logger.Error(err)
		return err
	}
	if err := ioutil.WriteFile(sdInputInfoPath, []byte(sdInput.Parameters), 0644); err != nil {
		logger.Error(err)
	}
	if err := ioutil.WriteFile(sdInputPath, []byte(tools.GetJsonFromStruct(sdInput)), 0644); err != nil {
		logger.Error(err)
	}

	sdInput.Parameters = parameters
	if strings.Contains(sdInput.Parameters, "{==={") || strings.Contains(sdInput.Parameters, "}===}") {
		logger.Error(sdInput.TraceId, sdInput.SdServer, "{==={存在未替换的字符串}===}")
	}

	switch sdInput.Sdapi {
	case enums.SdApiEnum.Txt2img:
		logger.Info(sdInput.TraceId, sdInput.SdServer, " 调用txt2img接口")
		if err := d.Txt2img(sdInput); err != nil {
			logger.Error(sdInput.TraceId, err)
		}
	case enums.SdApiEnum.Img2img:
		logger.Info(sdInput.TraceId, sdInput.SdServer, " 调用img2img接口")
		if err := d.Img2img(sdInput); err != nil {
			logger.Error(sdInput.TraceId, err)
		}
	case enums.SdApiEnum.UpscaleImg:
		logger.Info(sdInput.TraceId, sdInput.SdServer, " 调用upscale接口")
		if err := d.UpscaleImg(sdInput); err != nil {
			logger.Error(sdInput.TraceId, err)
		}
	}
	return nil
}

func (d *sd_) GetSdServerByKey(key string) *SdServerItem {
	jsonStr, err := myredis.HGet(enums.AigcRedisKeyEnum.SdApiServer, key)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if jsonStr == "" {
		logger.Error(key, "不存在")
		return nil
	}
	sdServerItem := SdServerItem{}
	if err := tools.GetStructFromJson(&sdServerItem, jsonStr); err != nil {
		logger.Error(err)
		return nil
	}
	return &sdServerItem
}
func (d *sd_) SaveSdServerByKey(sdServerItem SdServerItem) error {
	key := sdServerItem.ApiBaseUrl
	json := tools.GetJsonFromStruct(sdServerItem)
	if json == "" {
		logger.Error("json 为空")
		return errors.New("json为空")
	}
	_, err := myredis.HSet(enums.AigcRedisKeyEnum.SdApiServer, key, json)
	if err != nil {
		return err
	}
	return nil
}
func (d *sd_) ChangeSdServerProgress(key string, progress float64) error {
	lockKey := enums.AigcRedisKeyEnum.SdTaskLockKey + ":" + key
	defer myredis.UnLock(lockKey)
	if myredis.Lock(lockKey, 3*1000) == true {
		sdServerItem := d.GetSdServerByKey(key)
		if sdServerItem == nil {
			logger.Error("未找到相应的服务：", key)
			return fmt.Errorf("未找到相应的服务")
		}
		sdServerItem.Progress = progress
		if err := d.SaveSdServerByKey(*sdServerItem); err != nil {
			logger.Error(err)
		}
		return nil
	} else {
		logger.Error(lockKey, " 获取锁失败")
		return fmt.Errorf("被其他线程占用")
	}

}
func (d *sd_) ChangeSdServerState(key string, state int) error {
	lockKey := enums.AigcRedisKeyEnum.SdTaskLockKey + ":" + key
	defer myredis.UnLock(lockKey)
	if myredis.Lock(lockKey, 3*1000) == true {
		sdServerItem := d.GetSdServerByKey(key)
		if sdServerItem == nil {
			logger.Error("未找到相应的服务：", key)
			return fmt.Errorf("未找到相应的服务")
		}
		sdServerItem.State = state
		if err := d.SaveSdServerByKey(*sdServerItem); err != nil {
			logger.Error(err)
		}
		return nil
	} else {
		logger.Error(lockKey, " 获取锁失败")
		return fmt.Errorf("被其他线程占用")
	}

}
func (d *sd_) ChangeSdServerPause(key string, pause bool) error {
	lockKey := enums.AigcRedisKeyEnum.SdTaskLockKey + ":" + key
	defer myredis.UnLock(lockKey)
	if myredis.Lock(lockKey, 3*1000) == true {
		sdServerItem := d.GetSdServerByKey(key)
		if sdServerItem == nil {
			logger.Error("未找到相应的服务：", key)
			return fmt.Errorf("未找到相应的服务")
		}
		sdServerItem.Pause = pause
		if err := d.SaveSdServerByKey(*sdServerItem); err != nil {
			logger.Error(err)
		}
		return nil
	} else {
		logger.Error(lockKey, " 获取锁失败")
		return fmt.Errorf("被其他线程占用")
	}
}
func (d *sd_) ChangeSdServerDrawing(key string, taskId string) error {

	lockKey := enums.AigcRedisKeyEnum.SdTaskLockKey + ":" + key
	defer myredis.UnLock(lockKey)
	if myredis.Lock(lockKey, 3*1000) == true {
		sdServerItem := d.GetSdServerByKey(key)
		if sdServerItem == nil {
			logger.Error("未找到相应的服务：", key)
			return fmt.Errorf("未找到相应的服务")
		}

		sdServerItem.CurTaskId = taskId
		sdServerItem.ExecuteAt = time.Now().UnixMilli()
		sdServerItem.State = enums.SdServerStateEnum.Drawing

		if err := d.SaveSdServerByKey(*sdServerItem); err != nil {
			logger.Error(err)
			return err
		}
		return nil
	} else {
		logger.Error(lockKey, " 获取锁失败")
		return fmt.Errorf("被其他线程占用")
	}
}
func (d *sd_) ChangeSdServerComplete(key string, s string) error {

	lockKey := enums.AigcRedisKeyEnum.SdTaskLockKey + ":" + key
	defer myredis.UnLock(lockKey)
	if myredis.Lock(lockKey, 3*1000) == true {
		sdServerItem := d.GetSdServerByKey(key)
		if sdServerItem == nil {
			logger.Error("未找到相应的服务：", key)
			return fmt.Errorf("未找到相应的服务")
		}
		sdServerItem.CompletedAt = time.Now().UnixMilli()
		sdServerItem.LastIdleUnixMilli = time.Now().UnixMilli()
		if sdServerItem.State != enums.SdServerStateEnum.Pause {
			sdServerItem.State = enums.SdServerStateEnum.Idle
		}
		if s == "" {
			sdServerItem.ErrCount += 1
			if progress, err := d.Progress(key); err != nil || progress == "" {
				sdServerItem.State = enums.SdServerStateEnum.HasError
				sdServerItem.Pause = true
			}
		} else {
			sdServerItem.ErrCount = 0
		}
		sdServerItem.CurTaskId = ""
		if err := d.SaveSdServerByKey(*sdServerItem); err != nil {
			logger.Error(err)
			return err
		}
		return nil
	} else {
		logger.Error(lockKey, " 获取锁失败")
		return fmt.Errorf("被其他线程占用")
	}
}
func (d *sd_) ChangeSdServerHeartbeat(key string) (*SdServerItem, error) {
	lockKey := enums.AigcRedisKeyEnum.SdTaskLockKey + ":" + key
	defer myredis.UnLock(lockKey)
	if myredis.Lock(lockKey, 3*1000) == true {
		sdServerItem := d.GetSdServerByKey(key)
		if sdServerItem == nil {
			logger.Error("未找到相应的服务：", key)
			return sdServerItem, fmt.Errorf("未找到相应的服务")
		}
		sdServerItem.HeartbeatUnixMilli = time.Now().UnixMilli()
		if err := d.SaveSdServerByKey(*sdServerItem); err != nil {
			logger.Error(err)
		}
		return sdServerItem, nil
	} else {
		logger.Error(lockKey, " 获取锁失败")
		return nil, fmt.Errorf("被其他线程占用")
	}
}
func (d *sd_) GetSdServers() map[string]string {
	//defer func() {
	//	myredis.UnLock(enums.AigcRedisKeyEnum.SdApiLockKey)
	//}()
	//if myredis.Lock(enums.AigcRedisKeyEnum.SdApiLockKey, 5000) {
	//
	//}
	m, err := myredis.HGetAll(enums.AigcRedisKeyEnum.SdApiServer)
	if err != nil {
		logger.Error(err)
	}

	return m
}

func (d *sd_) PopSdService() (string, error) {
	//idx, _ = myredis.Incr(enums.RedisKeyEnum.AutoSerialNumber)
	//if idx <= 0 {
	//	return "", errors.New("获取自增需要失败")
	//}
	value, err := myredis.BRPop(enums.AigcRedisKeyEnum.SdApiServerQueue)
	return value, err
}
func (d *sd_) PushSdService(value string) (int64, error) {
	i, err := myredis.LPush(enums.AigcRedisKeyEnum.SdApiServerQueue, value)
	return i, err
}
func (d *sd_) ExistsSdService(value string) bool {
	values, err := myredis.LRange(enums.AigcRedisKeyEnum.SdApiServerQueue, 0, -1)
	if err != nil {
		logger.Error(err)
		return false
	}
	for _, v := range values {
		if v == value {
			return true
		}
	}
	return false
}

func (d *sd_) PopTask() (string, error) {
	value, err := myredis.BRPop(enums.AigcRedisKeyEnum.SdTaskPush)
	return value, err
}

func (d *sd_) Push(input SdInput) int64 {
	input.CreateAt = time.Now().UnixMilli()

	json := tools.GetJsonFromStruct(input)
	curSize := int64(0)
	if input.JumpTicket != "" { //插队的不设置序号
		size, err := myredis.RPush(enums.AigcRedisKeyEnum.SdTaskPush, json)
		if err != nil {
			logger.Error(size, err)
			return size
		} else {
			curSize = size
		}
	} else {
		if idx, err := myredis.Incr(enums.AigcRedisKeyEnum.SdTaskAutoNumber); err != nil {
			if idx, err = myredis.Incr(enums.AigcRedisKeyEnum.SdTaskAutoNumber); err != nil {
				logger.Error(err)
			} else {
				input.SdAutoNumber = idx
			}
		} else {
			input.SdAutoNumber = idx
		}
		size, err := myredis.LPush(enums.AigcRedisKeyEnum.SdTaskPush, json)
		if err != nil {
			logger.Error(size, err)
			return size
		} else {
			curSize = size
		}
	}

	ss := fmt.Sprintf(`{"progress":-2,"progress_txt":"排队中","sd_auto_number":%d,"t":"%s"}`, input.SdAutoNumber, time.Now().Format(model.TimeFormat))
	if _, err1 := myredis.HSet(enums.AigcRedisKeyEnum.SdTaskProgress+":"+input.CustomApp, input.CustomMd5, ss); err1 != nil {
		logger.Error(input.CustomMd5, "  ", err1, ss)
	}
	return curSize
}

func (d *sd_) PushTarget(sdInput SdInput, value string) error {

	jsonStr, err := myredis.HGet(enums.AigcRedisKeyEnum.SdTaskPushTarget, sdInput.SdServer)
	if err != nil {
		logger.Error(err)
		return err
	}
	ary := make([]string, 0)
	if jsonStr == "" {
		ary = append(ary, value)
	} else {
		if err := tools.GetStructFromJson(&ary, jsonStr); err != nil {
			logger.Error(err)
			return err
		} else {
			ary = append(ary, value)
		}
	}

	json := tools.GetJsonFromStruct(ary)
	_, err = myredis.HSet(enums.AigcRedisKeyEnum.SdTaskPushTarget, sdInput.SdServer, json)

	ss := fmt.Sprintf(`{"progress":-1,"sd_server":"%s","progress_txt":"指定队列中","t":"%s"}`, sdInput.SdServer, time.Now().Format(model.TimeFormat))
	if _, err1 := myredis.HSet(enums.AigcRedisKeyEnum.SdTaskProgress+":"+sdInput.CustomApp, sdInput.CustomMd5, ss); err1 != nil {
		logger.Error(sdInput.CustomMd5, "  ", err1, ss)
	}

	return err
}

func (d *sd_) GetTarget(sdServer string) string {
	jsonStr, err := myredis.HGet(enums.AigcRedisKeyEnum.SdTaskPushTarget, sdServer)
	if err != nil {
		logger.Error(err)
		return ""
	}
	if jsonStr == "" {
		return ""
	}
	ary := make([]string, 0)
	if err := tools.GetStructFromJson(&ary, jsonStr); err != nil {
		logger.Error(err)
		return ""
	}
	if len(ary) > 0 {
		tmp := ary[0]
		if len(ary) > 1 {
			ary = ary[1:]
			jsonStr = tools.GetJsonFromStruct(ary)
			if _, err := myredis.HSet(enums.AigcRedisKeyEnum.SdTaskPushTarget, sdServer, jsonStr); err != nil {
				logger.Error(err)
			}
		} else {
			if _, err := myredis.HDel(enums.AigcRedisKeyEnum.SdTaskPushTarget, sdServer); err != nil {
				logger.Error(err)
			}
		}
		return tmp
	}
	return ""
}

func (d *sd_) GetTargets(sdServer string) ([]string, error) {
	ary := make([]string, 0)
	jsonStr, err := myredis.HGet(enums.AigcRedisKeyEnum.SdTaskPushTarget, sdServer)
	if err != nil {
		logger.Error(err)
		return ary, err
	}
	if jsonStr == "" {
		return ary, err
	}
	if err := tools.GetStructFromJson(&ary, jsonStr); err != nil {
		logger.Error(err)
		return ary, err
	}
	return ary, nil
}

func (d *sd_) PushOut(sdOutput SdOutput) int64 {
	if oldValue, err := myredis.HGet(enums.AigcRedisKeyEnum.SdTaskProgress+":"+sdOutput.CustomApp, sdOutput.CustomMd5); err != nil {
		logger.Error(err)
	} else if oldValue != "" {
		m := tools.GetMapFromJson(oldValue)
		if val, ok := m["progress"]; ok {
			progress := val.(float64)
			sdOutput.Progress = progress
		}
	}

	json := tools.GetJsonFromStruct(sdOutput)
	mm := tools.GetMapFromJson(json)
	mm["t"] = time.Now().Format(model.TimeFormat)
	if _, err1 := myredis.HSet(enums.AigcRedisKeyEnum.SdTaskProgress+":"+sdOutput.CustomApp, sdOutput.CustomMd5, tools.GetJsonFromMap(mm)); err1 != nil {
		logger.Error(err1)
	}
	size, err := myredis.LPush(enums.AigcRedisKeyEnum.SdTaskPop+":"+sdOutput.CustomApp, json)
	if err != nil {
		logger.Error(size, err)
		return size
	}
	return size
}

func (d *sd_) PushJson(json string) (int64, error) {
	size, err := myredis.LPush(enums.AigcRedisKeyEnum.SdTaskPush, json)
	if err != nil {
		logger.Error(err)
		return size, err
	}
	return size, err
}

func (d *sd_) Txt2img(sdInput SdInput) error {
	url := sdInput.SdServer + "sdapi/v1/txt2img"

	s := ""
	executeAt := time.Now().UnixMilli()
	logger.Info(sdInput.TraceId, sdInput.SdServer, "开始请求文生图链接:", url)
	if ss, err := Post(url, sdInput.Parameters); err != nil {
		logger.Error(sdInput.TraceId, sdInput.SdServer, " 文生图链接请求失败", err)
		//return err
	} else {
		s = ss
	}
	executeTimes := time.Now().UnixMilli() - executeAt
	logger.Info(sdInput.TraceId, " ", sdInput.SdServer, " 文生图链接请求完成:", url, " 耗时", executeTimes, "毫秒")
	if err := d.OnCompleted(sdInput, s, executeAt, executeTimes); err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func (d *sd_) Img2img(sdInput SdInput) error {
	//url := SdServer + "sdapi/v1/img2img"
	url := sdInput.SdServer + "sdapi/v1/img2img"
	s := ""
	executeAt := time.Now().UnixMilli()
	logger.Info(sdInput.TraceId, sdInput.SdServer, "开始请求图生图链接:", url)
	if ss, err := Post(url, sdInput.Parameters); err != nil {
		logger.Error(sdInput.TraceId, sdInput.SdServer, " 图生图链接请求失败", err)
		//return err
	} else {
		s = ss
	}
	executeTimes := time.Now().UnixMilli() - executeAt
	logger.Info(sdInput.TraceId, sdInput.SdServer, " 图生图链接请求完成:", url, " 耗时", executeTimes, "毫秒")

	if err := d.OnCompleted(sdInput, s, executeAt, executeTimes); err != nil {
		logger.Error(sdInput.TraceId, err)
		return err
	}
	return nil
}

func (d *sd_) UpscaleImg(sdInput SdInput) error {
	//url := SdServer + "sdapi/v1/img2img"
	url := sdInput.SdServer + "sdapi/v1/extra-single-image"
	s := ""
	executeAt := time.Now().UnixMilli()
	logger.Info(sdInput.TraceId, sdInput.SdServer, "开始请求图片放大接口链接:", url)
	if ss, err := Post(url, sdInput.Parameters); err != nil {
		logger.Error(sdInput.TraceId, sdInput.SdServer, " 图片放大链接请求失败", err)
		//return err
	} else {
		s = ss
	}
	executeTimes := time.Now().UnixMilli() - executeAt
	logger.Info(sdInput.TraceId, sdInput.SdServer, " 图片放大链接请求完成:", url, " 耗时", executeTimes, "毫秒")

	if err := d.OnUpscalCompleted(sdInput, s, executeAt, executeTimes); err != nil {
		logger.Error(sdInput.TraceId, err)
		return err
	}
	return nil
}

func (d *sd_) SdTagger(sdInput SdInput) (string, error) {

	url := sdInput.SdServer + "tagger/v1/interrogate"
	logger.Info("Progress SdServer:", url)
	if ss, err := Post(url, sdInput.Parameters); err != nil {
		logger.Error(err)
		return "", err
	} else {
		//logger.Info(ss)
		return ss, nil
	}
}

func (d *sd_) SdModels(sdServer string) (string, error) {
	url := sdServer + "sdapi/v1/sd-models"
	logger.Info("Progress SdServer", url)
	if ss, err := Get(url); err != nil {
		logger.Error(err)
		return "", err
	} else {
		//logger.Info(ss)
		return ss, nil
	}
}
func (d *sd_) SdVaes(sdServer string) (string, error) {
	url := sdServer + "sdapi/v1/sd-vae"
	logger.Info("Progress SdServer", url)
	if ss, err := Get(url); err != nil {
		logger.Error(err)
		return "", err
	} else {
		//logger.Info(ss)
		return ss, nil
	}
}

func (d *sd_) Api(sdServer string, apiName string) (string, error) {
	//url := sdServer + "sdapi/v1/sd-vae"
	if strings.HasPrefix(apiName, "/") {
		apiName = apiName[1:]
	}
	url := sdServer + apiName
	logger.Info("Progress SdServer", url)
	if ss, err := Get(url); err != nil {
		logger.Error(err)
		return "", err
	} else {
		logger.Info(ss)
		return ss, nil
	}
}

func (d *sd_) PostApi(sdServer string, apiName string) (string, error) {
	//url := sdServer + "sdapi/v1/sd-vae"
	if strings.HasPrefix(apiName, "/") {
		apiName = apiName[1:]
	}
	url := sdServer + apiName
	logger.Info("Progress SdServer", url)
	if ss, err := Post(url, ""); err != nil {
		logger.Error(err)
		return "", err
	} else {
		logger.Info(ss)
		return ss, nil
	}
}

func (d *sd_) Progress(sdServer string) (string, error) {
	//http://*************:7887/internal/progress
	//parameters := fmt.Sprintf(`{"id_task": "%s","id_live_preview": -1}`, idTask)
	url := sdServer + "sdapi/v1/progress?skip_current_image=true"
	//logger.Info("Progress SdServer", url)
	if ss, err := Get(url); err != nil {
		logger.Error(err)
		return "", err
	} else {
		//logger.Info(ss)
		return ss, nil
	}
}

func (d *sd_) DoProgress(idTask string, sdInput SdInput) {
	logger.Info("开始进度轮询", idTask, "    ", sdInput.SdServer)
	for i := 0; i < 300; i++ {
		sdServerItem := d.GetSdServerByKey(sdInput.SdServer)
		if sdServerItem == nil {
			logger.Info("未找到Sd服务，退出进度轮询", idTask, "    ", sdInput.SdServer)
			break
		}
		if sdServerItem.CurTaskId != idTask {
			logger.Info("任务ID已经变更，退出进度轮询", idTask, "    ", sdInput.SdServer)
			break
		}

		ss, err := d.Progress(sdInput.SdServer)
		if err != nil || ss == "" {
			logger.Error(err, idTask, "   ss:", ss)
		} else {
			logger.Info(sdInput.CustomMd5, "    ", ss)
			oldProgress := float64(-1)
			if oldValue, err := myredis.HGet(enums.AigcRedisKeyEnum.SdTaskProgress+":"+sdInput.CustomApp, sdInput.CustomMd5); err != nil {
				logger.Error(err)
			} else if oldValue != "" {
				m := tools.GetMapFromJson(oldValue)
				if val, ok := m["progress"]; ok {
					oldProgress = val.(float64)
				}
			}
			mm := tools.GetMapFromJson(ss)
			if val, ok := mm["progress"]; ok {
				if val.(float64) > oldProgress {
					mm["t"] = time.Now().Format(model.TimeFormat)
					if _, err1 := myredis.HSet(enums.AigcRedisKeyEnum.SdTaskProgress+":"+sdInput.CustomApp, sdInput.CustomMd5, tools.GetJsonFromMap(mm)); err1 != nil {
						logger.Error(err1, idTask)
					}
				} else if val.(float64) < oldProgress {
					logger.Info("任务进度已重置，退出进度轮询", val, "    ", oldProgress, "   ", idTask, "    ", sdInput.SdServer)
					break
				}
				if er := d.ChangeSdServerProgress(sdInput.SdServer, val.(float64)); er != nil {
					logger.Error(er)
				}
			}
		}
		time.Sleep(time.Second * 2)
	}
	logger.Info("进度轮询结束", idTask, "    ", sdInput.SdServer)
}

func (d *sd_) OnCompleted(sdInput SdInput, s string, executeAt int64, executeTimes int64) error {
	logger.Info(sdInput.TraceId, sdInput.SdServer, " OnCompleted start")
	if err := d.ChangeSdServerComplete(sdInput.SdServer, s); err != nil {
		logger.Error(err)
	}
	relativePath := sdInput.CustomPath
	if !strings.HasPrefix(relativePath, sdInput.CustomApp+"/") {
		relativePath = sdInput.CustomApp + "/" + sdInput.CustomPath
	}
	absolutePath := config.DiffusionFilePath + relativePath

	ext := path.Ext(absolutePath)

	infoFullPath := strings.Replace(absolutePath, ext, ".txt", -1)
	if err := ioutil.WriteFile(infoFullPath, []byte(s), 0644); err != nil {
		logger.Error(sdInput.TraceId, sdInput.SdServer, err)
	}
	infoPath := strings.Replace(absolutePath, ext, ".txt", -1)
	outInfoPath := strings.Replace(absolutePath, ext, "_out.txt", -1)

	directory := filepath.Dir(absolutePath) // 获取目录路径
	// 创建目录，存在则不创建，不存在则创建
	if err := os.MkdirAll(directory, 0755); err != nil {
		logger.Error(sdInput.TraceId, sdInput.SdServer, err)
		return err
	}
	if s == "" {
		err := errors.New("绘图请求返回为空")
		logger.Error(sdInput.TraceId, sdInput.SdServer, "error:", err, s, "  ", sdInput.CustomMd5)

		sdOutput := SdOutput{
			CustomApp:    sdInput.CustomApp,
			CustomData:   sdInput.CustomData,
			CustomMd5:    sdInput.CustomMd5,
			CustomPath:   sdInput.CustomPath,
			Sdapi:        sdInput.Sdapi,
			SdServer:     sdInput.SdServer,
			Result:       "",
			TraceId:      sdInput.TraceId,
			CreateAt:     sdInput.CreateAt,
			ExecuteAt:    executeAt,
			ExecuteTimes: executeTimes,
			ErrorInfo:    `{"err":"绘图请求返回为空"}`,
		}
		if size := d.PushOut(sdOutput); size == 0 {
			logger.Error(sdInput.TraceId, sdInput.SdServer, "放入返回队列失败", sdInput.TraceId)
			return errors.New("放入返回队列失败")
		}
		return err
	}

	result := struct {
		ImagePath  string                 `json:"image_path"`
		Images     []string               `json:"images"`
		Parameters map[string]interface{} `json:"parameters"`
		Info       string                 `json:"info"`
	}{}

	//{"error":"HTTPException","detail":"Init image not found","body":"","errors":""}
	if err := tools.GetStructFromJson(&result, s); err != nil {
		logger.Error(sdInput.TraceId, sdInput.SdServer, "error:", s, "  ", sdInput.CustomMd5)
		return err
	}
	if result.Images == nil {
		err := errors.New("Images==nil")
		logger.Error(sdInput.TraceId, sdInput.SdServer, "error:", err, s, "  ", sdInput.CustomMd5)

		if err := ioutil.WriteFile(infoPath, []byte(s), 0644); err != nil {
			logger.Error(sdInput.TraceId, sdInput.SdServer, err)
		}
		sdOutput := SdOutput{
			CustomApp:    sdInput.CustomApp,
			CustomData:   sdInput.CustomData,
			CustomMd5:    sdInput.CustomMd5,
			CustomPath:   sdInput.CustomPath,
			Sdapi:        sdInput.Sdapi,
			SdServer:     sdInput.SdServer,
			Result:       "",
			TraceId:      sdInput.TraceId,
			CreateAt:     sdInput.CreateAt,
			ExecuteAt:    executeAt,
			ExecuteTimes: executeTimes,
			ErrorInfo:    s,
		}
		if size := d.PushOut(sdOutput); size == 0 {
			logger.Error(sdInput.TraceId, sdInput.SdServer, "放入返回队列失败", sdInput.TraceId)
			return errors.New("放入返回队列失败")
		}
		if err := ioutil.WriteFile(outInfoPath, []byte(tools.GetJsonFromStruct(sdOutput)), 0644); err != nil {
			logger.Error(sdInput.TraceId, sdInput.SdServer, err)
		}
		return err
	}

	imagePaths := make([]string, 0)
	for idx, val := range result.Images {
		if idx == 0 {
			if err := myimg.Base64ToFile(val, absolutePath); err != nil {
				logger.Error(err)
				return err
			} else {
				imagePaths = append(imagePaths, relativePath)
			}
		} else {
			absolutePathLast := strings.Replace(absolutePath, ".png", fmt.Sprintf("_%d.png", idx), -1)
			if err := myimg.Base64ToFile(val, absolutePathLast); err != nil {
				logger.Error(sdInput.TraceId, sdInput.SdServer, err)
				return err
			}
		}
	}
	result.ImagePath = relativePath
	result.Images = imagePaths

	outResult := make(map[string]interface{})
	outResult["out_image_path"] = relativePath

	//logger.Info("result.Info:", result.Info)
	mapInfo := tools.GetMapFromJson(result.Info)
	if val, ok := mapInfo["sd_model_hash"]; ok {
		sd_model_hash := val.(string)
		sd_model_name := ""

		if _, ok1 := mapInfo["sd_model_name"]; ok1 {
			sd_model_name = mapInfo["sd_model_name"].(string)
		} else {
			sd_model_name = GetModelName(sd_model_hash, result.Info)
		}

		if sd_model_name != "" {
			if strings.Contains(sdInput.Parameters, sd_model_name) == false {
				errInfo := fmt.Sprintf("%s:主模型未匹配,最终出图模型为:%s [%s]", sdInput.CustomMd5, sd_model_name, sd_model_hash)
				outResult["err_info"] = errInfo
			}
		} else {
			errInfo := fmt.Sprintf("%s:主模型名称未匹配到,最终出图模型为:%s [%s]", sdInput.CustomMd5, sd_model_name, sd_model_hash)
			outResult["err_info"] = errInfo
		}
		outResult["sd_model_name"] = sd_model_name
		outResult["sd_model_hash"] = sd_model_hash
	}
	//logger.Info("result.Info:check end")

	if sdInput.Options != nil {
		if _, ok := sdInput.Options["need_info"]; ok {
			outResult["info"] = result.Info
		}
		if _, ok := sdInput.Options["need_parameters"]; ok {
			outResult["parameters"] = result.Parameters
		}
	}

	if err := ioutil.WriteFile(infoPath, []byte(tools.GetJsonFromStruct(result)), 0644); err != nil {
		logger.Error(sdInput.TraceId, sdInput.SdServer, err)
	}

	sdOutput := SdOutput{
		CustomApp:    sdInput.CustomApp,
		CustomData:   sdInput.CustomData,
		CustomMd5:    sdInput.CustomMd5,
		CustomPath:   sdInput.CustomPath,
		Sdapi:        sdInput.Sdapi,
		SdServer:     sdInput.SdServer,
		Result:       tools.GetJsonFromStruct(outResult),
		TraceId:      sdInput.TraceId,
		CreateAt:     sdInput.CreateAt,
		ExecuteAt:    executeAt,
		ExecuteTimes: executeTimes,
	}
	if size := d.PushOut(sdOutput); size == 0 {
		logger.Error(sdInput.TraceId, sdInput.SdServer, "放入返回队列失败", sdInput.TraceId)
		return errors.New("放入返回队列失败")
	}

	if err := ioutil.WriteFile(outInfoPath, []byte(tools.GetJsonFromStruct(sdOutput)), 0644); err != nil {
		logger.Error(sdInput.TraceId, sdInput.SdServer, err)
	}
	logger.Info(sdInput.TraceId, sdInput.SdServer, " OnCompleted end")
	return nil
}

func (d *sd_) OnUpscalCompleted(sdInput SdInput, s string, executeAt int64, executeTimes int64) error {
	logger.Info(sdInput.TraceId, sdInput.SdServer, " OnCompleted start")
	if err := d.ChangeSdServerComplete(sdInput.SdServer, s); err != nil {
		logger.Error(err)
	}

	relativePath := sdInput.CustomPath
	if !strings.HasPrefix(relativePath, sdInput.CustomApp+"/") {
		relativePath = sdInput.CustomApp + "/" + sdInput.CustomPath
	}
	absolutePath := config.DiffusionFilePath + relativePath

	ext := path.Ext(absolutePath)
	infoPath := strings.Replace(absolutePath, ext, ".txt", -1)
	outInfoPath := strings.Replace(absolutePath, ext, "_out.txt", -1)

	directory := filepath.Dir(absolutePath) // 获取目录路径
	// 创建目录，存在则不创建，不存在则创建
	if err := os.MkdirAll(directory, 0755); err != nil {
		logger.Error(sdInput.TraceId, sdInput.SdServer, err)
		return err
	}

	if s == "" {
		err := errors.New("绘图请求返回为空")
		logger.Error(sdInput.TraceId, sdInput.SdServer, "error:", err, s, "  ", sdInput.CustomMd5)

		sdOutput := SdOutput{
			CustomApp:    sdInput.CustomApp,
			CustomData:   sdInput.CustomData,
			CustomMd5:    sdInput.CustomMd5,
			CustomPath:   sdInput.CustomPath,
			Sdapi:        sdInput.Sdapi,
			SdServer:     sdInput.SdServer,
			Result:       "",
			TraceId:      sdInput.TraceId,
			CreateAt:     sdInput.CreateAt,
			ExecuteAt:    executeAt,
			ExecuteTimes: executeTimes,
			ErrorInfo:    `{"err":"绘图请求返回为空"}`,
		}
		if size := d.PushOut(sdOutput); size == 0 {
			logger.Error(sdInput.TraceId, sdInput.SdServer, "放入返回队列失败", sdInput.TraceId)
			return errors.New("放入返回队列失败")
		}
		if err := ioutil.WriteFile(outInfoPath, []byte(tools.GetJsonFromStruct(sdOutput)), 0644); err != nil {
			logger.Error(sdInput.TraceId, sdInput.SdServer, err)
		}
		return err
	}

	result := struct {
		Image    string `json:"image"`
		HtmlInfo string `json:"html_info"`
	}{}

	//{"error":"HTTPException","detail":"Init image not found","body":"","errors":""}
	if err := tools.GetStructFromJson(&result, s); err != nil {
		logger.Error(sdInput.TraceId, sdInput.SdServer, "error:", s, "  ", sdInput.CustomMd5)
		return err
	}
	if result.Image == "" {
		err := errors.New("result.Image为空")
		logger.Error(sdInput.TraceId, sdInput.SdServer, "error:", err, s, "  ", sdInput.CustomMd5)

		if err := ioutil.WriteFile(infoPath, []byte(s), 0644); err != nil {
			logger.Error(sdInput.TraceId, sdInput.SdServer, err)
		}
		sdOutput := SdOutput{
			CustomApp:    sdInput.CustomApp,
			CustomData:   sdInput.CustomData,
			CustomMd5:    sdInput.CustomMd5,
			CustomPath:   sdInput.CustomPath,
			Sdapi:        sdInput.Sdapi,
			SdServer:     sdInput.SdServer,
			Result:       "",
			TraceId:      sdInput.TraceId,
			CreateAt:     sdInput.CreateAt,
			ExecuteAt:    executeAt,
			ExecuteTimes: executeTimes,
			ErrorInfo:    s,
		}
		if size := d.PushOut(sdOutput); size == 0 {
			logger.Error(sdInput.TraceId, sdInput.SdServer, "放入返回队列失败", sdInput.TraceId)
			return errors.New("放入返回队列失败")
		}
		if err := ioutil.WriteFile(outInfoPath, []byte(tools.GetJsonFromStruct(sdOutput)), 0644); err != nil {
			logger.Error(sdInput.TraceId, sdInput.SdServer, err)
		}
		return err
	}

	if err := myimg.Base64ToFile(result.Image, absolutePath); err != nil {
		logger.Error(err)
		return err
	}

	outResult := make(map[string]interface{})
	outResult["out_image_path"] = relativePath

	if sdInput.Options != nil {
		if _, ok := sdInput.Options["need_info"]; ok {
			outResult["info"] = result.HtmlInfo
		}
	}

	result.Image = ""
	if err := ioutil.WriteFile(infoPath, []byte(tools.GetJsonFromStruct(result)), 0644); err != nil {
		logger.Error(sdInput.TraceId, sdInput.SdServer, err)
	}

	sdOutput := SdOutput{
		CustomApp:    sdInput.CustomApp,
		CustomData:   sdInput.CustomData,
		CustomMd5:    sdInput.CustomMd5,
		CustomPath:   sdInput.CustomPath,
		Sdapi:        sdInput.Sdapi,
		SdServer:     sdInput.SdServer,
		Result:       tools.GetJsonFromStruct(outResult),
		TraceId:      sdInput.TraceId,
		CreateAt:     sdInput.CreateAt,
		ExecuteAt:    executeAt,
		ExecuteTimes: executeTimes,
	}
	if size := d.PushOut(sdOutput); size == 0 {
		logger.Error(sdInput.TraceId, sdInput.SdServer, "放入返回队列失败", sdInput.TraceId)
		return errors.New("放入返回队列失败")
	}
	logger.Info(sdInput.TraceId, sdInput.SdServer, " OnUpscalCompleted end")
	if err := ioutil.WriteFile(outInfoPath, []byte(tools.GetJsonFromStruct(sdOutput)), 0644); err != nil {
		logger.Error(sdInput.TraceId, sdInput.SdServer, err)
	}
	return nil
}

func InitTxt2Img() Txt2Img {

	return Txt2Img{
		CfgScale:          7,
		HrScale:           2,
		Steps:             30,
		Subseed:           -1,
		DenoisingStrength: 0.78,
	}
}
func InitControlnetUnits() ControlnetUnit {
	return ControlnetUnit{
		Module:        "inpaint_only",                         //预处理器
		Model:         "control_v11p_sd15_inpaint [ebff9138]", //模型
		Weight:        1,                                      //控制权重 1-2之间
		ResizeMode:    0,                                      //缩放模式
		ProcessorRes:  512,
		ThresholdA:    100,
		ThresholdB:    100,
		Guidance:      1,
		GuidanceStart: 0, //引导介入时机
		GuidanceEnd:   1, //引导终止时机
		Guessmode:     "false",
		PixelPerfect:  "false",
		ControlMode:   0, //控制类型?
	}
}

func Post(url string, payload string) (string, error) {
	authorization := "Basic eXhqOnl4ajEyMw=="
	req, err := http.NewRequest("POST", url, bytes.NewBuffer([]byte(payload)))
	if err != nil {
		logger.Error("NewRequest error:", err)
		return "", err
	}
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	req.Header.Set("Authorization", authorization)

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Error("Post Fatal error:", err)
		return "", err
	}

	defer res.Body.Close()

	content, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Error("Fatal error ", err)
		return "", err
	}

	str := (*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	return *str, nil
}

func Get(url string) (string, error) {
	authorization := "Basic eXhqOnl4ajEyMw=="
	payload := ""
	req, err := http.NewRequest("GET", url, bytes.NewBuffer([]byte(payload)))
	if err != nil {
		logger.Error("NewRequest error:", err)
		return "", err
	}
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
	req.Header.Set("Authorization", authorization)

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Error("Post Fatal error:", err)
		return "", err
	}

	defer res.Body.Close()

	content, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Error("Fatal error ", err)
		return "", err
	}

	str := (*string)(unsafe.Pointer(&content)) //转化为string,优化内存
	return *str, nil
}

func GetModelName(modelHash string, input string) string {
	if modelHash == "" {
		return ""
	}
	// 创建正则表达式匹配模式
	re := regexp.MustCompile(fmt.Sprintf(`Model hash: %s, Model: (.+?),`, modelHash))

	// 查找匹配的字符串
	matches := re.FindAllStringSubmatch(input, -1)
	ary := make([]string, 0)
	for _, match := range matches {
		if len(match) >= 2 {
			ary = append(ary, match[1])
			return match[1]
		}
	}
	return ""
}

package file_ctx

import (
	"context"
	"cpn-file-ai/common/logger"
	redis_client "cpn-file-ai/common/redis-client"
	"cpn-file-ai/enums"
	"fmt"
	"log"
	"strconv"
	"sync"
	"time"
)

const (
	RedisSubChannelStopDownload = "StopDownload"
)

type FileDownloadCtx struct {
	ctx  map[string]*Ctx
	lock sync.Mutex
}

type Ctx struct {
	cancelFunc func()
	ctx        context.Context
}

var FileDownloadCtxInstance FileDownloadCtx

func (f *FileDownloadCtx) Init() {
	f.ctx = make(map[string]*Ctx)
	f.lock = sync.Mutex{}

	go func() {
		ctx := context.Background()
		sub := redis_client.RedisSub(ctx, RedisSubChannelStopDownload)
		// 确保订阅生效
		_, err := sub.Receive(ctx)
		if err != nil {
			log.Fatal(err)
		}

		// 接收消息的通道
		ch := sub.Channel()

		for msg := range ch {
			logger.Info(fmt.Sprintf("收到用户中断下载消息：Task id:%s", msg.Payload))
			redisKey := enums.RedisKeyEnum.FileDownloadKey + msg.Payload
			redis_client.RedisSet(redisKey, strconv.Itoa(enums.ModelDownloadStatusCanceled), 60*time.Second)
			f.Cancel(msg.Payload)
		}

	}()
}

func (f *FileDownloadCtx) Save(key string, ctx context.Context, cancelFunc func()) {
	f.lock.Lock()
	defer f.lock.Unlock()
	f.ctx[key] = &Ctx{
		cancelFunc: cancelFunc,
		ctx:        ctx,
	}
}

func (f *FileDownloadCtx) Del(key string) {
	f.lock.Lock()
	defer f.lock.Unlock()
	delete(f.ctx, key)
}

func (f *FileDownloadCtx) Get(key string) context.Context {
	f.lock.Lock()
	defer f.lock.Unlock()
	return f.ctx[key].ctx
}

func (f *FileDownloadCtx) Cancel(key string) {
	f.lock.Lock()
	defer f.lock.Unlock()
	if ctx, ok := f.ctx[key]; ok {
		if ctx != nil {
			ctx.cancelFunc()
		}
	}
}

func (f *FileDownloadCtx) Done(key string) {
	f.lock.Lock()
	defer f.lock.Unlock()
	delete(f.ctx, key)
}

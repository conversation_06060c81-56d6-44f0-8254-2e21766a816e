package logger

import (
	"fmt"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"time"
)

var errorLogger *zap.SugaredLogger

var levelMap = map[string]zapcore.Level{

	"debug": zapcore.DebugLevel,

	"info": zapcore.InfoLevel,

	"warn": zapcore.WarnLevel,

	"error": zapcore.ErrorLevel,

	"dpanic": zapcore.DPanicLevel,

	"panic": zapcore.PanicLevel,

	"fatal": zapcore.FatalLevel,
}

func getLoggerLevel(lvl string) zapcore.Level {

	if level, ok := levelMap[lvl]; ok {

		return level

	}

	return zapcore.InfoLevel

}

func iSO8601TimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	encodeTimeLayout(t, "2006-01-02 15:04:05.000", enc)
}
func encodeTimeLayout(t time.Time, layout string, enc zapcore.PrimitiveArrayEncoder) {
	type appendTimeEncoder interface {
		AppendTimeLayout(time.Time, string)
	}

	if enc, ok := enc.(appendTimeEncoder); ok {
		enc.AppendTimeLayout(t, layout)
		return
	}
	enc.AppendString(t.Format(layout))
}

func init() {

	logDir := "./log"
	fileName := logDir + "/zap.log"

	// Create log directory if it doesn't exist
	if err := os.MkdirAll(logDir, 0755); err != nil {
		// Just print the error and continue
		fmt.Println("failed to create log directory:", err)
	}

	level := getLoggerLevel("info")

	syncWriter := zapcore.AddSync(&lumberjack.Logger{

		Filename: fileName, //日志文件存放目录，如果文件夹不存在会自动创建

		MaxSize: 1, //1 << 30=1G //文件大小限制,单位MB

		MaxBackups: 1024, //最大保留日志文件数量

		MaxAge: 30, //日志文件保留天数

		LocalTime: true,

		Compress: false,
	})
	consoleWriter := zapcore.Lock(os.Stdout)

	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	multiCore := zapcore.NewTee(
		zapcore.NewCore(zapcore.NewConsoleEncoder(encoderConfig), consoleWriter, level),
		zapcore.NewCore(zapcore.NewConsoleEncoder(encoderConfig), syncWriter, level),
	)

	logger := zap.New(multiCore, zap.AddCaller(), zap.AddCallerSkip(1))
	errorLogger = logger.Sugar()
}

func Debug(args ...interface{}) {

	errorLogger.Debug(args...)

}

func Debugf(template string, args ...interface{}) {

	errorLogger.Debugf(template, args...)

}

func Info(args ...interface{}) {
	errorLogger.Info(args...)
}

func InfoSpace(args ...interface{}) {
	newArg := make([]interface{}, 0)
	// 遍历原始参数，在每个元素之间插入空格
	for i, arg := range args {
		newArg = append(newArg, arg)
		// 在每个元素后插入空格，除了最后一个元素
		if i < len(args)-1 {
			newArg = append(newArg, " ")
		}
	}
	errorLogger.Info(newArg...)
}

//func Info(args ...interface{}) {
//	// 创建一个新的字符串切片，用于存储参数和空格
//	var newArgs []interface{}
//
//	// 遍历参数
//	for _, arg := range args {
//		// 将参数添加到新切片中
//		newArgs = append(newArgs, arg)
//		// 添加空格到新切片中
//		newArgs = append(newArgs, " ")
//	}
//
//	// 将新切片传递给errorLogger.Error方法
//	errorLogger.Info(newArgs...)
//}

func Infof(template string, args ...interface{}) {

	errorLogger.Infof(template, args...)

}

func Warn(args ...interface{}) {

	errorLogger.Warn(args...)

}

func Warnf(template string, args ...interface{}) {

	errorLogger.Warnf(template, args...)

}

func Error(args ...interface{}) {

	errorLogger.Error(args...)

}

//func Error(args ...interface{}) {
//	// 创建一个新的字符串切片，用于存储参数和空格
//	var newArgs []interface{}
//
//	// 遍历参数
//	for _, arg := range args {
//		// 将参数添加到新切片中
//		newArgs = append(newArgs, arg)
//		// 添加空格到新切片中
//		newArgs = append(newArgs, " ")
//	}
//
//	// 将新切片传递给errorLogger.Error方法
//	errorLogger.Error(newArgs...)
//}

func Errorf(template string, args ...interface{}) {

	errorLogger.Errorf(template, args...)

}

func DPanic(args ...interface{}) {

	errorLogger.DPanic(args...)

}

func DPanicf(template string, args ...interface{}) {

	errorLogger.DPanicf(template, args...)

}

func Panic(args ...interface{}) {

	errorLogger.Panic(args...)

}

func Panicf(template string, args ...interface{}) {

	errorLogger.Panicf(template, args...)

}

func Fatal(args ...interface{}) {
	errorLogger.Fatal(args...)

}

func Fatalf(template string, args ...interface{}) {

	errorLogger.Fatalf(template, args...)

}

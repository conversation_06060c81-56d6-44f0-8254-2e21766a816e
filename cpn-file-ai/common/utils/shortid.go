package utils

import (
	"crypto/md5"
	"encoding/hex"
	"github.com/google/uuid"
	"math/big"
	"math/rand"
	"strings"
)

const base36Digits = "abcdefghijklmnopqrstuvwxyz0123456789"

func DecimalToBase36(n int64) string {
	if n == 0 {
		return string(base36Digits[0])
	}
	base := big.NewInt(36)
	result := ""
	zero := big.NewInt(0)
	bigN := big.NewInt(n)

	for bigN.Cmp(zero) > 0 {
		quo := new(big.Int)
		quo.Mod(bigN, base)
		result = string(base36Digits[quo.Int64()]) + result
		bigN.Quo(bigN, base)
	}
	return result
}

func Base36ToDecimal(s string) int64 {
	n := int64(0)
	base := big.NewInt(36)
	exp := new(big.Int)

	for i := len(s) - 1; i >= 0; i-- {
		exp.SetInt64(int64(len(s) - 1 - i))
		digitValue := int64(strings.IndexByte(base36Digits, s[i]))
		tmp := new(big.Int).Exp(base, exp, nil)
		tmp.Mul(tmp, big.NewInt(digitValue))
		n += tmp.Int64()
	}
	return n
}

func EncodeAdvance(num int64, outLenth int) string {
	return encode(num, outLenth)
}

func Encode(num int64) string {
	return encode(num, 18)
}

func encode(num int64, outLenth int) string {
	numStr := DecimalToBase36(num)
	if outLenth < len(numStr)+3 {
		return ""
	}
	hash := md5.Sum([]byte(numStr))
	hashStr := hex.EncodeToString(hash[:]) // 将哈希值转换为十六进制字符串

	firstNumStr := string(hashStr[0])
	secondNumStr := string(hashStr[1])

	firstNum := Base36ToDecimal(firstNumStr)
	secondNum := Base36ToDecimal(secondNumStr)

	numStrLen := int64(len(numStr))
	randNum := firstNum + secondNum
	randNum = randNum % 36
	thirdNum := randNum + numStrLen
	thirdNum = thirdNum % 36
	thirdNumStr := DecimalToBase36(thirdNum)

	firstCode := firstNumStr + secondNumStr + thirdNumStr + numStr

	firstCodeLen := len(firstCode)
	needLen := outLenth - firstCodeLen
	lastCode := hashStr[firstCodeLen : firstCodeLen+needLen]
	lastCode = firstCode + lastCode
	return lastCode
}

func encodeRand(num int64, outLenth int) string {
	numStr := DecimalToBase36(num)
	if outLenth < len(numStr)+3 {
		return ""
	}

	numStrLen := int64(len(numStr))
	firstNum := int64(rand.Intn(36))
	secondNum := int64(rand.Intn(36))
	randNum := firstNum + secondNum
	randNum = randNum % 36
	thirdNum := randNum + numStrLen
	thirdNum = thirdNum % 36
	firstNumStr := DecimalToBase36(firstNum)
	secondNumStr := DecimalToBase36(secondNum)
	thirdNumStr := DecimalToBase36(thirdNum)

	firstCode := firstNumStr + secondNumStr + thirdNumStr + numStr
	code := uuid.New().String()
	code = strings.Replace(code, "-", "", -1)
	needLen := outLenth - len(firstCode)
	lastCode := code[:needLen]
	lastCode = firstCode + lastCode
	return lastCode
}

func Decode(str string) int64 {
	firstNumStr := string(str[0])
	secondNumStr := string(str[1])
	thirdNumStr := string(str[2])

	firstNum := Base36ToDecimal(firstNumStr)
	secondNum := Base36ToDecimal(secondNumStr)
	thirdNum := Base36ToDecimal(thirdNumStr)

	randNum := firstNum + secondNum
	randNum = randNum % 36
	numStrLen := int64(0)
	if thirdNum > randNum {
		numStrLen = thirdNum - randNum
	} else {
		numStrLen = 36 - randNum + thirdNum
	}
	numStr := str[3 : 3+numStrLen]
	num := Base36ToDecimal(numStr)
	return num
}

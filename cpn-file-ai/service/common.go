package service

import (
	"cpn-file-ai/common/logger"
	"cpn-file-ai/config"
	"errors"
	"os"
	"strings"
)

func CheckPrivateStorage() error {
	if len(config.PrivateStorage) < 10 {
		logger.Error("用户存储基础路径为空", config.PrivateStorage)
		return errors.New("用户存储基础路径为空")
	}
	if strings.HasSuffix(config.PrivateStorage, "/") == false {
		logger.Error("用户存储基础路径结束符不正确", config.PrivateStorage)
		return errors.New("用户存储基础路径结束符不正确")
	}
	if _, err := os.Stat(config.PrivateStorage); os.IsNotExist(err) {
		logger.Error("用户存储基础路径不存在", err)
		return err
	}

	return nil
}

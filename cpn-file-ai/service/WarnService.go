package service

import (
	"cpn-file-ai/common/logger"
	"cpn-file-ai/common/utils"
	"cpn-file-ai/config"
	"cpn-file-ai/enums"
	"cpn-file-ai/model"
	"errors"
	"github.com/shopspring/decimal"
	"net/url"
	"strings"
	"time"
)

type warn_ struct {
}

var WarnService warn_

// messageCode 预先定义的消息码
// method 1微信  2短信
// regularUnix 定时发送时间戳(单位：秒)
// sendParm 发送参数，发送模板需要替换的参数，根据messageCode进行配置
// send  send值为s时，马上发送，否则由任务队列发送

func (obj *warn_) SendMessage(messageCode string, method int, userId uint, regularUnix int64, sendParm map[string]string, send string) (string, error) {
	var user model.User
	if err := user.GetById(userId); err != nil {
		logger.Error(err)
		return "查询用户信息失败", err
	}
	if method == 1 && user.AccountId == 0 {
		err := errors.New("该用户还未绑定微信")
		logger.Error(err, " userId:", userId)
		return err.Error(), err
	}

	//CenterServer = http://127.0.0.1:5101/api/
	//postUrl := "http://192.168.200.4:5101/api/v1/message/add"
	postUrl, _ := url.JoinPath(config.CenterServer, "v1/message/add")
	if config.Env == enums.EnvEnum.ONLINE {
		postUrl = strings.Replace(postUrl, ":5101/", ":5102/", -1)
	}

	postData := make(map[string]interface{})
	postData["Authorization"] = "ccd4fa7fa34b4d96702c165b3a4b6154"
	postData["message_code"] = messageCode
	postData["method"] = method
	postData["project_id"] = 8
	postData["project_user_id"] = userId
	postData["account_id"] = user.AccountId
	postData["regular_unix"] = regularUnix
	postData["send_parm"] = sendParm
	postData["send"] = send
	if ginH, err := PostNodeForGin(0, postUrl, postData); err != nil {
		logger.Error(err, " ginH:", ginH)
		return "请求发送失败", err
	} else {
		if tmpH, err := ResultGinH(ginH); err != nil {
			logger.Error("解析出错 ginH:", utils.GetJsonFromStruct(ginH))
			return "解析请求失败", err
		} else {
			if tmpH.Code == 0 {
				return "", nil
			} else {
				return tmpH.Msg, errors.New(tmpH.Msg)
			}
		}
	}
}

// send s 马上发送
func (obj *warn_) LowAmount(userId uint, amount decimal.Decimal, t time.Time) (string, error) {
	sendParm := map[string]string{
		"amount": amount.Round(2).String(),
		"time":   t.Format("2006-01-02 15:04:05"),
		"const":  "余额即将不足",
	}
	return obj.SendMessage("LowAmount", 1, userId, 0, sendParm, "")
}

// 余额即将不足，实例即将关闭  这个文字不能发送
func (obj *warn_) LowAmountAndWillShutdownInstance(userId uint, amount decimal.Decimal, t time.Time) (string, error) {
	sendParm := map[string]string{
		"amount": amount.Round(2).String(),
		"time":   t.Format("2006-01-02 15:04:05"),
		"const":  "余额即将不足，实例即将关闭",
	}
	return obj.SendMessage("LowAmount", 1, userId, 0, sendParm, "")
}

func (obj *warn_) DebtAndDestroyStorage(userId uint) (string, error) {
	//sendParm := map[string]string{
	//	"amount": amount.Round(2).String(),
	//	"time":   t.Format("2006-01-02 15:04:05"),
	//	"const":  "已欠费，个人镜像和云存储7天后销毁",
	//}
	//return obj.SendMessage("DebtAndDestroyStorage", 1, userId, 0, sendParm, "")
	//if amount.LessThan(decimal.Zero) || t.Equal(time.Now()) {
	//
	//}

	reason := "账号已欠费，个人数据将在欠费7天后销毁"
	sendParm := map[string]string{
		"name":   "个人数据销毁预警",
		"time":   time.Now().Format("2006-01-02 15:04:05"),
		"reason": reason,
	}

	return obj.SendMessage("StorageWarn", 1, userId, 0, sendParm, "")
}

func (obj *warn_) RechargeAmountSuccess(userId uint, rechargeAmount decimal.Decimal, t time.Time) (string, error) {

	var user model.User
	if err := user.GetById(userId); err != nil {
		logger.Error(err)
		return "获取用户信息失败", err
	}
	totalAmount := user.Amount
	var card model.Card
	if leaveAmount, err := card.CardValidAmount(userId, 0); err != nil {
		logger.Error("CardValidAmount   ", "err:", err, " leaveAmount:", leaveAmount.String(), " userId:", userId, "   podId:", 0)
		return "统计算力卡有效余额失败", err
	} else {
		totalAmount = totalAmount.Add(leaveAmount)
	}
	sendParm := map[string]string{
		"character_string": utils.FormatMobileStar(user.Mobile),
		"recharge_amount":  rechargeAmount.Round(2).String(),
		"amount":           totalAmount.Round(2).String(),
		"time":             t.Format("2006-01-02 15:04:05"),
		"thing":            "余额充值",
	}
	return obj.SendMessage("RechargeSuccess", 1, userId, 0, sendParm, "")
}

func (obj *warn_) RechargeBuyCardSuccess(userId uint, rechargeAmount decimal.Decimal, t time.Time) (string, error) {

	var user model.User
	if err := user.GetById(userId); err != nil {
		logger.Error(err)
		return "获取用户信息失败", err
	}
	totalAmount := user.Amount
	var card model.Card
	if leaveAmount, err := card.CardValidAmount(userId, 0); err != nil {
		logger.Error("CardValidAmount   ", "err:", err, " leaveAmount:", leaveAmount.String(), " userId:", userId, "   podId:", 0)
		return "统计算力卡有效余额失败", err
	} else {
		totalAmount = totalAmount.Add(leaveAmount)
	}
	sendParm := map[string]string{
		"character_string": utils.FormatMobileStar(user.Mobile),
		"recharge_amount":  rechargeAmount.Round(2).String(),
		"amount":           totalAmount.Round(2).String(),
		"time":             t.Format("2006-01-02 15:04:05"),
		"thing":            "购买算力卡",
	}
	return obj.SendMessage("RechargeSuccess", 1, userId, 0, sendParm, "")
}

func (obj *warn_) InstanceRunning(userId uint, instanceUuid string, reason string) (string, error) {
	sendParm := map[string]string{
		"name":   "实例ID",
		"id":     instanceUuid,
		"time":   time.Now().Format("2006-01-02 15:04:05"),
		"reason": reason,
	}
	return obj.SendMessage("InstanceRunning", 1, userId, 0, sendParm, "")
}

//func (obj *warn_) PrivateImageStore(userId uint, reason string) (string, error) {
//	sendParm := map[string]string{
//		"name":   "个人镜像存储",
//		"id":     "我的POD镜像",
//		"time":   time.Now().Format("2006-01-02 15:04:05"),
//		"reason": reason,
//	}
//	return obj.SendMessage("InstanceRunning", 1, userId, 0, sendParm, "")
//}

func (obj *warn_) PrivateImageStore(userId uint, beforeSize int64, afterSize int64) (string, error) {

	beforeSizeG := float64(beforeSize) / float64(1024) / float64(1024) / float64(1024)
	afterSizeG := float64(afterSize) / float64(1024) / float64(1024) / float64(1024)
	if userId == 2 {
		logger.Info("镜像存储整点扣费 userId:", userId, " PrivateSize:", beforeSize, " sumSizeTmp:", afterSize, "   beforeSizeG：", beforeSizeG, "  afterSizeG:", afterSizeG)
	}
	needReminder := false
	reason := "存储已超过"
	if beforeSizeG < 50 && afterSizeG >= 50 {
		needReminder = true
		reason += "50G"
	} else if beforeSizeG < 100 && afterSizeG >= 100 {
		needReminder = true
		reason += "100G"
	} else if beforeSizeG < 500 && afterSizeG >= 500 {
		needReminder = true
		reason += "500G"
	}

	if needReminder {
		reason += "，计费中"
		sendParm := map[string]string{
			"name":   "个人镜像存储",
			"time":   time.Now().Format("2006-01-02 15:04:05"),
			"reason": reason,
		}
		go func() {
			if msg, err := obj.SendMessage("StorageWarn", 1, userId, 0, sendParm, ""); err != nil {
				logger.Error(msg, err)
			}
		}()
	}
	return "无需发送", nil
}

func (obj *warn_) PrivateCloudStore(userId uint, beforeSize int64, afterSize int64) (string, error) {

	beforeSizeG := float64(beforeSize) / float64(1024) / float64(1024) / float64(1024)
	afterSizeG := float64(afterSize) / float64(1024) / float64(1024) / float64(1024)
	if userId == 2 {
		logger.Info("云存储整点扣费 userId:", userId, " PrivateSize:", beforeSize, " sumSizeTmp:", afterSize, "   beforeSizeG：", beforeSizeG, "  afterSizeG:", afterSizeG)
	}
	needReminder := false
	reason := "存储已超过"
	if beforeSizeG < 50 && afterSizeG >= 50 {
		needReminder = true
		reason += "50G"
	} else if beforeSizeG < 100 && afterSizeG >= 100 {
		needReminder = true
		reason += "100G"
	} else if beforeSizeG < 500 && afterSizeG >= 500 {
		needReminder = true
		reason += "500G"
	}

	if needReminder {
		reason += "，计费中"
		sendParm := map[string]string{
			"name":   "个人云存储",
			"time":   time.Now().Format("2006-01-02 15:04:05"),
			"reason": reason,
		}
		go func() {
			if msg, err := obj.SendMessage("StorageWarn", 1, userId, 0, sendParm, ""); err != nil {
				logger.Error(msg, err)
			}
		}()
		return "已发送", nil
	}
	return "无需发送", nil
}

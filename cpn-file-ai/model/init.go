package model

import (
	"context"
	"cpn-file-ai/common/constants"
	"cpn-file-ai/common/sysLogger"
	"cpn-file-ai/common/utils"
	"cpn-file-ai/config"
	"cpn-file-ai/internal/ccm"
	"os"
	"strings"
	"time"

	"github.com/allegro/bigcache/v3"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

var DB *gorm.DB
var SQLitePath = "cpn-ai.db"

var cache *bigcache.BigCache // 内存缓存

func chooseDB() (*gorm.DB, error) {
	SQL_DSN := os.Getenv("SQL_DSN")
	if SQL_DSN == "" {
		SQL_DSN = config.SQL_DSN
	}

	if SQL_DSN != "" {
		dsn := SQL_DSN
		if strings.HasPrefix(dsn, "postgres://") {
			// Use PostgreSQL
			sysLogger.SysLog("using PostgreSQL as database")
			return gorm.Open(postgres.New(postgres.Config{
				DSN:                  dsn,
				PreferSimpleProtocol: true, // disables implicit prepared statement usage
			}), &gorm.Config{
				PrepareStmt: true, // precompile SQL
			})
		}
		// Use MySQL
		sysLogger.SysLog("using MySQL as database")
		return gorm.Open(mysql.Open(dsn), &gorm.Config{
			PrepareStmt: true, // precompile SQL
		})
	}
	// Use SQLite
	sysLogger.SysLog("SQL_DSN not set, using SQLite as database")
	return gorm.Open(sqlite.Open(SQLitePath), &gorm.Config{
		PrepareStmt: true, // precompile SQL
	})
}

func InitDB() (err error) {
	{
		//配置缓存自动清理
		configCache := bigcache.Config{
			Shards:             64,
			LifeWindow:         60 * time.Second, // 缓存生存时间
			CleanWindow:        1 * time.Minute,  // 每分钟清理一次过期条目
			MaxEntriesInWindow: 1000,             // 最大条目数
			MaxEntrySize:       500,              // 每个条目的最大字节数
			Verbose:            true,
		}
		ctx := context.Background()
		cache, err = bigcache.New(ctx, configCache)
		if err != nil {
			return err
		}
	}

	db, err := chooseDB()
	if err == nil {
		if constants.DebugEnabled {
			db = db.Debug()
		}
		DB, ccm.DB = db, db
		if sqlDB, err := DB.DB(); err != nil {
			return err
		} else {
			sqlDB.SetMaxIdleConns(utils.GetOrDefault("SQL_MAX_IDLE_CONNS", 100))
			sqlDB.SetMaxOpenConns(utils.GetOrDefault("SQL_MAX_OPEN_CONNS", 1000))
			sqlDB.SetConnMaxLifetime(time.Second * time.Duration(utils.GetOrDefault("SQL_MAX_LIFETIME", 60)))
		}
		return err
	} else {
		sysLogger.FatalLog(err)
	}
	return err
}

func CloseDB() error {
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}
	err = sqlDB.Close()
	return err
}
func GetTX(tx *gorm.DB) *gorm.DB {
	if tx == nil {
		return DB
	}
	return tx
}

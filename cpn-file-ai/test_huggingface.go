package main

import (
	"cpn-file-ai/service"
	"fmt"
	"log"
)

func main() {
	// 测试用例1: resolve链接
	testURL1 := "https://huggingface.co/deepseek-ai/DeepSeek-R1-0528/resolve/main/model-00001-of-000163.safetensors?download=true"
	
	// 测试用例2: blob链接
	testURL2 := "https://huggingface.co/deepseek-ai/DeepSeek-R1-0528/blob/main/model-00002-of-000163.safetensors"
	
	fileService := service.FileService
	
	fmt.Println("=== 测试 HuggingFace 单个文件下载功能 ===")
	
	// 测试第一个URL
	fmt.Printf("\n测试URL1: %s\n", testURL1)
	files1, modelName1, err1 := fileService.getHuggingFaceModelFile(testURL1)
	if err1 != nil {
		log.Printf("测试URL1失败: %v", err1)
	} else {
		fmt.Printf("模型名称: %s\n", modelName1)
		for _, file := range files1 {
			fmt.Printf("文件名: %s\n", file.FileName)
			fmt.Printf("SHA256: %s\n", file.Sha256)
			fmt.Printf("下载URL: %s\n", file.FileDownloadUrl)
			fmt.Printf("文件大小: %d KB\n", file.FileSize)
		}
	}
	
	// 测试第二个URL
	fmt.Printf("\n测试URL2: %s\n", testURL2)
	files2, modelName2, err2 := fileService.getHuggingFaceModelFile(testURL2)
	if err2 != nil {
		log.Printf("测试URL2失败: %v", err2)
	} else {
		fmt.Printf("模型名称: %s\n", modelName2)
		for _, file := range files2 {
			fmt.Printf("文件名: %s\n", file.FileName)
			fmt.Printf("SHA256: %s\n", file.Sha256)
			fmt.Printf("下载URL: %s\n", file.FileDownloadUrl)
			fmt.Printf("文件大小: %d KB\n", file.FileSize)
		}
	}
	
	fmt.Println("\n=== 测试完成 ===")
}

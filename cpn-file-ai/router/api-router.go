package router

import (
	"cpn-file-ai/controller"
	"cpn-file-ai/middleware"
	"encoding/gob"

	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
)

func SetApiRouter(router *gin.Engine) {
	// 使用 Cookie 作为会话存储
	gob.Register(&middleware.MyClaims{})

	router.Use(func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "POST, GET, OPTIONS")
		c.<PERSON>er("Access-Control-Allow-Headers", "Content-Type, AccessToken, X-CSRF-Token, Authorization, Token")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204) // OPTIONS 请求不需要进入后续处理，直接返回
			return
		}

		c.Next()
	})

	apiRouter := router.Group("/api")
	//apiRouter.Use(gzip.Gzip(gzip.DefaultCompression))
	apiRouter.Use(gzip.Gzip(gzip.DefaultCompression, gzip.WithExcludedPaths([]string{
		"/api/filedown", // 排除整个文件下载路径
	})))

	//apiRouter.Use(middleware.GlobalAPIRateLimit())
	{
		userAuth := apiRouter.Group("/")
		userAuth.Use(middleware.UserAuth())
		{
			userAuth.POST("files", controller.FilesApi.Files)
			userAuth.GET("filedown", controller.FilesApi.Files)
			userAuth.POST("files/compress", controller.FilesApi.FileCompress)
			userAuth.GET("files/compress_progress", controller.FilesApi.GetFileCompressProgress)
			userAuth.GET("files/compress_download", controller.FilesApi.DownloadCompressedFile)
			userAuth.POST("files/background_model_download", controller.FilesApi.DownloadModelBackground)
			userAuth.GET("files/download_tasks", controller.FilesApi.GetThirdModelDownloadTasks)
			userAuth.GET("files/download_progress", controller.FilesApi.GetThirdModelDownloadProgress)
			userAuth.POST("files/stop_download_task", controller.FilesApi.StopThirdModelDownloadTask)
			userAuth.POST("files/flash_upload_check", controller.FilesApi.FlashUploadCheck)
			userAuth.POST("files/flash_upload", controller.FilesApi.FlashUpload)
		}
	}
}

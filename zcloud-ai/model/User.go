package model

import (
	"errors"
	"fmt"
	"log"
	"strconv"
	"time"
	"zcloud-ai/enums"
	"zcloud-ai/utils/logger"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
	"gorm.io/plugin/optimisticlock"
)

type User struct {
	gorm.Model
	Username        string    `gorm:"type:varchar(20);not null " json:"username" validate:"required,min=4,max=20" label:"用户名"`
	Nickname        string    `json:"nickname" gorm:"type:varchar(50);not null;default:'';comment:昵称"`
	Mobile          string    `gorm:"type:varchar(20);not null " json:"mobile" validate:"required,min=4,max=12" label:"手机号码"`
	Password        string    `gorm:"type:varchar(500);not null;" json:"password" validate:"required,min=6,max=20" label:"密码"`
	Coin            int       `gorm:"type:int;not null;default:0;comment:钱币" json:"coin" validate:"required,gte=0" label:"钱币"`
	FrozenCoin      int       `gorm:"type:int;not null;default:0;comment:冻结资金" json:"frozen_coin" validate:"required,gte=0" label:"冻结钱币"`
	Role            int       `gorm:"type:int;not null;default:0;comment:权限" json:"role" validate:"required,gte=0" label:"角色码"`
	Avatar          string    `gorm:"type:varchar(100);not null;default:'';comment:头像链接" json:"avatar"`
	Unionid         string    `gorm:"type:varchar(50);comment:微信开放平台的唯一标识符" json:"unionid"`
	Openid          string    `gorm:"type:varchar(50);comment:应用中用户唯一标识" json:"openid"`
	PhoneInfo       string    `gorm:"type:json;comment:微信获取手机号码返回信息"json:"phone_info" `
	IchiMore        time.Time `gorm:"type:datetime;default:'1900-01-01';comment:每天补足幸运值的时间" json:"ichi_more"`
	DailySign       time.Time `gorm:"type:datetime;default:'1900-01-01';comment:签到领取幸运值的时间" json:"daily_sign"`
	DailyShare      time.Time `gorm:"type:datetime;default:'1900-01-01';comment:每日分享领取幸运值的时间" json:"daily_share"`
	ChildrenExpires time.Time `json:"children_expires" gorm:"type:datetime;default:'1900-01-01';comment:儿童版订阅到期日期"`
	HandrawFree     int       `gorm:"type:int;not null;default:10;comment:手绘每日免费次数" json:"handraw_free"`
	HandrawFreeDate time.Time `gorm:"type:datetime;default:'1900-01-01';comment:手绘每日免费次数重置日期" json:"handraw_free_date"`
	Platform        int       `gorm:"type:tinyint;comment:注册来源平台" json:"platform"`
	InvitationCode  string    `gorm:"type:varchar(50);not null;default:'';comment:邀请码" json:"invitation_code"`
	InvitedCode     string    `gorm:"type:varchar(50);not null;default:'';comment:被邀请码" json:"invited_code"`
	InvitedUserId   uint      `gorm:"type:bigint;not null;default:0;comment:被邀请用户ID" json:"invited_user_id"`
	Plat            string    `gorm:"type:varchar(50);not null;default:'';comment:来源平台" json:"plat"`
	UserSystem      string    `json:"user_system" gorm:"type:json;comment:用户系统信息"`
	Version         optimisticlock.Version
}

func (User) TableName() string {
	return "T_User"
}

func (o *User) GetByID(id uint) error {
	return db.First(o, id).Error
}

func (o *User) GetByMobile(mobile string) error {
	var total int64
	err := db.Where("mobile = ?", mobile).Find(o).Count(&total).Error
	if total > 1 {
		return errors.New("存在多条数据")
	}
	return err
}

func (o *User) GetByUsername(username string) error {
	var total int64
	err := db.Select("*").Where("username = ?", username).Find(o).Count(&total).Error
	if total > 1 {
		return errors.New("存在多条数据")
	}
	return err
}

func (o *User) ExistsUsername(username string) (bool, error) {
	var user User
	if err := db.First(&user, "username = ?", username).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) ExistsMobile(mobile string) (bool, error) {
	var user User
	if err := db.First(&user, "mobile = ?", mobile).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) ExistsInvitationCode(invitationCode string) (bool, error) {
	var user User
	if err := db.First(&user, "invitation_code = ?", invitationCode).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			//fmt.Println("Record not found")
			return false, nil
		} else {
			//fmt.Println("Other error:", err)
			return true, err
		}
	} else {
		//fmt.Println("Record found:", user)
		return true, nil
	}
}

func (o *User) GetByOpenid(openid string) error {
	var total int64
	err := db.Select("*").Where("openid = ?", openid).Find(o).Count(&total).Error
	if total > 1 {
		return errors.New("存在多条数据")
	}
	return err
}

func (o *User) GetIchiMoreList(lastId uint, pageSize int) ([]User, error) {
	var ary []User

	tx := *db.Debug().Model(o).Where("id>? and coin<?", lastId, 100).Order("id asc")
	tx.Limit(pageSize).Scan(&ary)
	return ary, tx.Error
}

func (o *User) Save() error {
	return db.Save(o).Error
}

//func (o *User) ResetHandrawFree() error {
//	return db.Debug().Unscoped().Model(&User{}).Omit("version").Where("handraw_free < ?", 3).UpdateColumns(map[string]interface{}{"handraw_free": 3}).Error
//}

func (o *User) SetHandrawFree() error {
	return db.Model(o).Omit("version").Updates(User{HandrawFree: 3, HandrawFreeDate: time.Now()}).Error
}

func (o *User) SetInvitationCode(invitationCode string) error {
	return db.Model(o).Omit("version").Updates(User{InvitationCode: invitationCode}).Error
}

func (o *User) SetNicknameAvatar(nickname string, avatar string) error {
	return db.Model(o).Omit("version").Updates(User{Nickname: nickname, Avatar: avatar}).Error
}

func (o *User) SetChildrenExpires(childrenExpires time.Time) error {
	return db.Model(o).Omit("version").Updates(User{ChildrenExpires: childrenExpires}).Error
}

func (o *User) SetMobile(mobile string) error {
	if o.Mobile == "" {
		return db.Debug().Model(o).Omit("version").Updates(User{Mobile: mobile}).Error
	} else {
		return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
			changeRecord := ChangeRecord{
				UserId:      o.ID,
				ChangeType:  enums.ChangeRecordType.UserMobile,
				DataId:      o.ID,
				OriginData:  fmt.Sprintf(`{"mobile":"%s"}`, o.Mobile),
				CurrentData: fmt.Sprintf(`{"mobile":"%s"}`, mobile),
			}

			if err := changeRecord.New(tx); err != nil {
				logger.Error(err)
				return err
			}

			if err := tx.Debug().Model(o).Updates(User{Mobile: mobile}).Error; err != nil {
				logger.Error(err)
				return err
			}
			return nil
		})
	}
}

func (o *User) SetPhoneInfo(phoneInfo string) error {
	return db.Debug().Omit("version").Model(o).Updates(User{PhoneInfo: phoneInfo}).Error
}

func (o *User) SetNickname(nickname string) error {
	return db.Debug().Model(o).Omit("version").Updates(User{Nickname: nickname}).Error
}

func (o *User) SetAvatar(avatar string) error {
	return db.Debug().Model(o).Omit("version").Updates(User{Avatar: avatar}).Error
}

func (o *User) SetUsername(username string) error {
	return db.Model(o).Omit("version").Updates(User{Username: username}).Error
}

func (o *User) SetPassword() error {
	if o.Password == "" {
		return errors.New("密码不能为空")
	}
	if len(o.Password) > 30 {
		return errors.New("该密码已加密过")
	}
	o.Password = ScryptPw(o.Password)
	return db.Debug().Omit("version").Model(o).Update("password", o.Password).Error
}

func (o *User) CheckPassword(pw string) error {
	return bcrypt.CompareHashAndPassword([]byte(o.Password), []byte(pw))
}

// BeforeCreate 密码加密&权限控制
func (u *User) BeforeCreate(_ *gorm.DB) (err error) {
	u.Password = ScryptPw(u.Password)
	return nil
}

//func (u *User) BeforeUpdate(_ *gorm.DB) (err error) {
//	//password := u.Password
//	//u.Password = ScryptPw(password)
//	//db.Debug().Save(u)
//	//db.Model(u).Statement.Dest.SetColumn("password", ScryptPw(u.Password))
//	//m := db.Statement.Dest.(map[string]interface{})
//	//m["password"] = ScryptPw(u.Password)
//	//db.Statement.Dest = m
//	//m := make(map[string]interface{})
//	//if db.Statement.Dest != nil {
//	//	m = db.Statement.Dest.(map[string]interface{})
//	//}
//	//m = make(map[string]interface{})
//	//m["password"] = ScryptPw(u.Password)
//	//db.Statement.Dest = m
//
//	//u.Password = ScryptPw(u.Password)
//	//db.Save(u)
//
//	return db.Debug().Model(u).Update("password", ScryptPw(u.Password)).Error
//
//}

// ScryptPw 生成密码
func ScryptPw(password string) string {
	const cost = 10

	HashPw, err := bcrypt.GenerateFromPassword([]byte(password), cost)
	if err != nil {
		log.Println(err)
	}

	return string(HashPw)
}

func (o *User) GetUserList(dest interface{}, userId uint, username string, mobile string, page int, pageSize int) (int64, error) {
	var total int64
	tx := db.Debug().Model(o)
	if userId > 0 {
		tx.Where("id=?", userId)
	}
	if len(username) > 0 {
		tx.Where("username=?", username)
	}
	if len(mobile) > 0 {
		tx.Where("mobile=?", mobile)
	}
	tx.Order("id desc")
	if err = tx.Count(&total).Error; err != nil {
		return 0, err
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error
}

func (u *User) GetUserList11(dest interface{}, username string, mobile string, invitor string, orderBy string, page int, pageSize int) (int64, error) {
	var total int64

	// tx := db.Debug().Model(u).Where("username like ? and mobile like ?", "%"+username+"%", "%"+mobile+"%")
	// tx := db.Debug().Table("T_User as u1").Select("u1.*, u2.mobile as invitor_mobile").Joins("LEFT JOIN T_User as u2 ON u1.invited_user_id = u2.id WHERE u1.username like ? and u1.mobile like ?", "%"+username+"%", "%"+mobile+"%")
	tx := db.Debug().Table("T_User as u1").Select("u1.*, u2.mobile as invitor_mobile").Joins("LEFT JOIN T_User as u2 ON u1.invited_user_id = u2.id")

	if username != "" {
		userid, err := strconv.Atoi(username)
		if err == nil {
			tx.Where("u1.id = ?", userid)
		} else {
			tx.Where("u1.username like ?", "%"+username+"%")
		}

	}
	tx.Where("u1.mobile like ?", "%"+mobile+"%")
	if invitor != "" {
		var invitorId uint
		if len(invitor) == 11 {
			var user User
			user.GetByMobile(invitor)
			invitorId = user.ID
		} else {
			ii, _ := strconv.ParseUint(invitor, 10, 64)
			invitorId = uint(ii)
		}
		tx.Where("u1.invited_user_id = ?", invitorId)
	}
	if orderBy != "" {
		tx.Order(orderBy)
	} else {
		tx.Order("id desc")
	}

	if err = tx.Count(&total).Error; err != nil {
		return 0, err
	}
	tx.Limit(pageSize).Offset((page - 1) * pageSize).Scan(dest)
	return total, tx.Error

}

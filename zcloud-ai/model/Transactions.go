package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"time"
	"zcloud-ai/enums"
	"zcloud-ai/utils/logger"
)

type transactions_ struct {
}

func (transactions *transactions_) NewAnime(anime *Anime) error {

	var user User
	if anime.PriceCoin > 0 {
		if err := db.First(&user, anime.UserId).Error; err != nil {
			return err
		}
		if user.Coin-user.FrozenCoin < anime.PriceCoin {
			return errors.New("余额不足")
		}
	}

	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if anime.PriceCoin > 0 {
			if err := tx.Debug().Model(&user).Updates(User{FrozenCoin: user.FrozenCoin + anime.PriceCoin}).Error; err != nil {
				return err
			}
		}
		if err := tx.Save(anime).Error; err != nil { // 返回任何错误都会回滚事务
			return err
		}
		return nil
	})
}

func (transactions *transactions_) HandrawCost(handraw *Handraw) error {

	var user User
	if err := db.First(&user, handraw.UserId).Error; err != nil {
		logger.Error(err)
		return err
	}

	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if handraw.PriceCoin > 0 {
			if user.Coin-user.FrozenCoin < handraw.PriceCoin {
				return errors.New("余额不足")
			}

			var balance CoinBalance

			orderNo, err := OrderNo.NewByOrderType(enums.OrderTypeEnum.Cost, 0)
			if err != nil {
				logger.Error(err)
				return err
			}
			if err := balance.GetBalanceObject(orderNo, handraw.UserId, enums.OrderTypeEnum.Cost, 0-handraw.PriceCoin, "绘图消费", fmt.Sprintf("手绘绘图Id:%d", handraw.ID), handraw.UserId, "用户ID"); err != nil {
				logger.Error(err)
				return err
			}
			if err := balance.NewCost(tx); err != nil {
				logger.Error(err)
				return err
			}
			if err := tx.Model(handraw).Updates(Handraw{OrderNo: orderNo}).Error; err != nil {
				return err
			}
		} else {
			after := user.HandrawFree - 1
			if after < 0 {
				logger.Error("免费次数已用完 ", user.ID)
				return errors.New("免费次数已用完")
			}
			if err := tx.Debug().Model(&user).Updates(map[string]interface{}{"handraw_free": after}).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

func (transactions *transactions_) DelDiffImg(diffImg *DiffImg) error {

	if diffImg.ID > 0 && diffImg.OrigWhere != enums.OrigWhereEnum.Anime {
		return errors.New("删除来源不匹配")
	}

	anime := Anime{}
	if err := anime.Get(diffImg.OrigId); err != nil {
		return errors.New("获取Anime数据失败")
	}

	if diffImg.OrigId != anime.ID {
		return errors.New("删除数据归属不匹配")
	}

	if diffImg.UserId != anime.UserId {
		return errors.New("删除数据所属不匹配")
	}

	imagesStr := ""
	if anime.State == enums.ModelStateEnum.Complete {
		var aryImages []string
		if err := json.Unmarshal([]byte(anime.OutputImages), &aryImages); err != nil {
			logger.Error(err)
			return err
		}
		aryImages[diffImg.BatchNum] = "-"
		by, err := json.Marshal(aryImages)
		if err != nil {
			logger.Error(err)
			return err
		}
		imagesStr = string(by)
		logger.Info("删除后新的imagesStr：", imagesStr)
	}

	/*
		aryDiff, err := diffImg.GetListByAnimeId(anime.UserId, anime.ID)
		if err != nil {
			logger.Error(err)
		}

		var aryImages = make([]string, 0)
		for _, item := range aryDiff {
			if item.ID == diffImg.ID || item.Path == "" {
				continue
			}
			aryImages = append(aryImages, item.Path)
		}
		by, err := json.Marshal(aryImages)
		if err != nil {
			logger.Error(err)
			return err
		}
		imagesStr := string(by)
		logger.Info("删除后新的imagesStr：", imagesStr)*/

	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if err := diffImg.Del(tx, diffImg.ID, anime.UserId); err != nil {
			logger.Error(err)
			return err
		}
		//
		//outputImages := anime.OutputImages
		//newOutputImages := strings.Replace(outputImages, diffImg.Path, "", -1)
		if imagesStr != "" {
			if err := tx.Debug().Model(anime).Updates(Anime{OutputImages: imagesStr}).Error; err != nil { // 返回任何错误都会回滚事务
				logger.Error(err)
				return err
			}
		}
		return nil
	})
}

func (transactions *transactions_) DelAnime(anime *Anime, txt2img *Txt2Img) error {
	if txt2img.ID > 0 && txt2img.OrigWhere != enums.OrigWhereEnum.Anime {
		return errors.New("删除来源不匹配")
	}
	if txt2img.ID > 0 && txt2img.OrigId != anime.ID {
		return errors.New("删除数据不匹配")
	}

	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if txt2img.ID > 0 {

			var diffImg DiffImg
			if err := diffImg.DelByOrig(tx, anime.UserId, 1, anime.ID); err != nil {
				logger.Error(err)
				return err
			}

			if err := tx.Debug().Delete(txt2img).Error; err != nil {
				logger.Error(err)
				return err
			}
		} else {
			logger.Error("txt2img.ID==0 animeID:", anime.ID)
		}
		if err := tx.Debug().Delete(anime).Error; err != nil {
			logger.Error()
			return err
		}
		return nil
	})
}

func (transactions *transactions_) AnimeToImg2txt(anime *Anime, balance *CoinBalance, txt2img *Txt2Img) error {

	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')

		if err := tx.Save(txt2img).Error; err != nil { // 返回任何错误都会回滚事务
			return err
		}
		if anime.PriceCoin > 0 {
			var user User
			if err := db.First(&user, balance.UserId).Error; err != nil || user.ID == 0 { // 返回任何错误都会回滚事务
				return err
			}
			if err := balance.checkBalance(); err != nil { // 返回任何错误都会回滚事务
				return err
			}

			if balance.OccurredAmount > 0 {
				return errors.New("金额错误")
			}

			after := user.Coin + balance.OccurredAmount
			if after < 0 {
				return errors.New("余额不足")
			}
			if err := balance.New(tx); err != nil {
				return err
			}
			if err := tx.Model(anime).Updates(Anime{State: enums.ModelStateEnum.Success, OrderNo: balance.OrderNo}).Error; err != nil { // 返回任何错误都会回滚事务
				return err
			}
		} else {
			if err := tx.Model(anime).Updates(Anime{State: enums.ModelStateEnum.Success}).Error; err != nil { // 返回任何错误都会回滚事务
				return err
			}
		}

		return nil
	})

	//生成 balance
	//生成 txt2img
	//更新 anime orderNo state
	//更新 user  coin
}

func (transactions *transactions_) ManageAddCoin(balance *CoinBalance) error {
	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (transactions *transactions_) IchiMoreAddCoin(balance *CoinBalance) error {
	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}

		var user User
		if err := db.First(&user, balance.UserId).Error; err != nil || user.ID == 0 {
			logger.Error(" orderNo:", balance.OrderNo, " balance.OccurredAmount:", balance.OccurredAmount, " userID:", user.ID)
			return err
		}

		if err := tx.Debug().Unscoped().Model(&user).Updates(User{IchiMore: time.Now()}).Error; err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (transactions *transactions_) DailySignAddCoin(balance *CoinBalance) error {
	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}

		var user User
		if err := db.First(&user, balance.UserId).Error; err != nil || user.ID == 0 {
			logger.Error(" orderNo:", balance.OrderNo, " balance.OccurredAmount:", balance.OccurredAmount, " userID:", user.ID)
			return err
		}

		if err := tx.Debug().Unscoped().Model(&user).Updates(User{DailySign: time.Now()}).Error; err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (transactions *transactions_) DailyShareAddCoin(balance *CoinBalance) error {
	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}

		var user User
		if err := db.First(&user, balance.UserId).Error; err != nil || user.ID == 0 {
			logger.Error(" orderNo:", balance.OrderNo, " balance.OccurredAmount:", balance.OccurredAmount, " userID:", user.ID)
			return err
		}

		if err := tx.Debug().Unscoped().Model(&user).Updates(User{DailyShare: time.Now()}).Error; err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (transactions *transactions_) InviteNewUserAddCoin(balance *CoinBalance, shareResult *ShareResult) error {
	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		var invitedUser User                       //新注册的用户
		invitedUserId := shareResult.VisitedUserId //新注册的用户ID
		invitedCode := shareResult.InvitationCode
		userID := shareResult.UserId //发起邀请的用户ID
		if err := db.First(&invitedUser, invitedUserId).Error; err != nil || invitedUser.ID == 0 {
			logger.Error(" orderNo:", balance.OrderNo, " balance.OccurredAmount:", balance.OccurredAmount, " invitedUserID:", invitedUser.ID)
			return err
		}

		if invitedUser.InvitedUserId > 0 || len(invitedUser.InvitedCode) == 6 {
			logger.Error("用户已经被邀请过，invitedUserId：", invitedUserId, "  InvitedCode:", invitedUser.InvitedCode)
			return errors.New("该用户已被邀请过")
		}

		if err := balance.New(tx); err != nil {
			logger.Error(err)
			return err
		}

		shareResult.OrderNo = balance.OrderNo
		if err := shareResult.New(tx); err != nil {
			logger.Error(err)
			return err
		}

		if err := tx.Debug().Unscoped().Model(&invitedUser).Updates(User{InvitedCode: invitedCode, InvitedUserId: userID}).Error; err != nil {
			logger.Error(err)
			return err
		}
		return nil
	})
}

func (transactions *transactions_) RechargeSuccess(recharge *Recharge) error {
	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')

		if recharge.State != 0 {
			logger.Error(recharge.ID, "state:", recharge.State)
			return errors.New("状态不正确")
		}

		balance := CoinBalance{
			UserId:         recharge.UserId,
			OperatorId:     recharge.UserId,
			OccurredAmount: recharge.CoinCharge,
		}
		if recharge.PayType == enums.PayTypeEnum.AppleIap {
			if err := recharge.SetAppleIapSuccess(tx, recharge.PayTradeId, recharge.PayTime); err != nil {
				return err
			}
		} else if recharge.PayType == enums.PayTypeEnum.AliPay {
			if err := recharge.SetAliPaySuccess(tx, recharge.PayTradeId, recharge.PayTime, recharge.PayCallbackJson); err != nil {
				return err
			}
		} else {
			if err := recharge.SetPaySuccess(tx, recharge.PayTime, recharge.PayCallbackJson); err != nil {
				return err
			}
		}

		if err := balance.GetBalanceObject(recharge.OutTradeNo, recharge.UserId, enums.OrderTypeEnum.RechargeBuy, recharge.CoinCharge, "充值购买", fmt.Sprintf("充值金额%s", recharge.AmountCharge), recharge.UserId, "User"); err != nil {
			return err
		}
		if err := balance.New(tx); err != nil || balance.ID <= 0 {
			if balance.ID <= 0 {
				return errors.New("id=0")
			}
			return err
		}
		return nil
	})
}

func (transactions *transactions_) NewBalance(balance *CoinBalance) error {
	return db.Transaction(func(tx *gorm.DB) error { // 在事务中执行一些 db 操作(从这里开始，应该使用 'tx' 而不是'db')
		if err := balance.New(tx); err != nil {
			return err
		}
		return nil
	})
}

var Transactions transactions_

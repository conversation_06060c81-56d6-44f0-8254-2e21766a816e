package model

import (
	"gorm.io/gorm"
)

type ShareResult struct {
	gorm.Model
	ShareId        uint   `json:"share_id" gorm:"type:bigint;not null;default:0;comment:分享的ID"`
	ShareType      int    `gorm:"type:int;not null;default:0;comment:分享类型" json:"share_type" `
	UserId         uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	InvitationCode string `json:"invitation_code" gorm:"type:varchar(50);not null;default:'';comment:邀请码" `
	VisitedUserId  uint   `json:"visited_user_id" gorm:"type:bigint;not null;default:0;comment:被邀请用户ID"`
	Coin           int    `json:"coin" gorm:"type:int;not null;default:0;comment:奖励金币" `
	OrderNo        string `json:"order_no" gorm:"type:varchar(50);comment:金币流水订单编号"`
	IsNewUser      bool   `json:"is_new_user" gorm:"type:tinyint;comment:1新用户"`
}

func (ShareResult) TableName() string {
	return "T_ShareResult"
}

func (o *ShareResult) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *ShareResult) GetByMd5(md5Str string) error {
	err := db.Where("md5=?", md5Str).First(o).Error
	return err
}

func (o *ShareResult) Save() error {
	return db.Save(o).Error
}

func (o *ShareResult) New(tx *gorm.DB) error {
	return tx.Save(o).Error
}

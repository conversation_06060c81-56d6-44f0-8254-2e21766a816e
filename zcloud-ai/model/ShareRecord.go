package model

import (
	"gorm.io/gorm"
)

type ShareRecord struct {
	gorm.Model
	UserId         uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	InvitationCode string `gorm:"type:varchar(50);not null;default:'';comment:邀请码" json:"invitation_code"`
	ShareUuid      string `gorm:"type:varchar(50);not null;default:'';comment:分享编号" json:"share_uuid"`
	ShareType      int    `gorm:"type:int;not null;default:0;comment:分享类型" json:"share_type" `
	SharePath      string `gorm:"type:varchar(150);not null;default:'';comment:分享路径" json:"share_path"`
	ShareOptions   string `gorm:"type:json;comment:分享路径参数" json:"share_options"`
	Coin           int    `gorm:"type:int;not null;default:0;comment:奖励金币" json:"coin"`
	VisitCount     int    `gorm:"type:int;not null;default:0;comment:访问次数" json:"visit_count" `
}

func (ShareRecord) TableName() string {
	return "T_ShareRecord"
}

func (o *ShareRecord) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *ShareRecord) GetByShareUuid(uuid string) error {
	err := db.Debug().Where("share_uuid=?", uuid).First(o).Error
	return err
}

func (o *ShareRecord) Save() error {
	return db.Save(o).Error
}

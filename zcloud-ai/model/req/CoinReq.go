package req

type CoinReq struct {
	UserId   uint   `json:"user_id"`
	Amount   int    `json:"amount"`
	Operator string `json:"operator"`
}

type CoinBalanceReq struct {
	ID        uint   `json:"id"`
	UserId    uint   `json:"user_id"`
	OrderType int    `json:"order_type"`
	OrderNo   string `json:"order_no"`
	Amount    int    `json:"amount"`
	Show      string `json:"show"`
	Remark    string `json:"remark"`
	Operator  string `json:"operator"`
}

type AnimePriceReq struct {
	StyleId    int    `json:"style_id"`
	ImgScale   string `json:"img_scale"`
	BatchSize  int    `json:"batch_size"`
	BatchCount int    `json:"batch_count"`
}

type UserCoinResp struct {
	UserId      uint `json:"user_id"`
	Coin        int  `json:"coin"`
	HandrawFree int  `json:"handraw_free"`
}

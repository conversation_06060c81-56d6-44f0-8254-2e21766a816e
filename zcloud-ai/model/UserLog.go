package model

import (
	"gorm.io/gorm"
)

type UserLog struct {
	gorm.Model
	UdId       string `json:"ud_id" gorm:"type:varchar(50);not null;default:'';comment:设备标识"`
	UserId     uint   `json:"user_id" gorm:"type:bigint;not null;default:0;comment:用户ID"`
	LogType    int    `json:"log_type" gorm:"type:int;not null;default:0;comment:日志类型"`
	LogJson    string `json:"log_json" gorm:"type:json;';comment:日志内容"`
	UserSystem string `json:"user_system" gorm:"type:json;comment:用户系统信息"`
}

func (UserLog) TableName() string {
	return "T_UserLog"
}

func (o *UserLog) GetByID(id uint) error {
	err := db.First(o, id).Error
	return err
}

func (o *UserLog) GetList(dest interface{}, state int, page int, pageSize int) error {
	tx := db.Debug().Model(o).Where("state=?", state).Order("order_index asc").Scan(dest)
	return tx.Error
}

func (o *UserLog) Save() error {
	return db.Save(o).Error
}

//func (o *UserLog) SetAddr(nation string, province string, city string, district string, addr string) error {
//	return db.Debug().Model(o).Updates(UserLog{Nation: nation, Province: province, City: city, District: district, Address: addr}).Error
//}

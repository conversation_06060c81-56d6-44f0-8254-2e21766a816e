package auth

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"
	"zcloud-ai/enums"
	"zcloud-ai/global"
	"zcloud-ai/middleware"
	"zcloud-ai/model"
	"zcloud-ai/utils/config"
	"zcloud-ai/utils/errmsg"
	"zcloud-ai/utils/logger"
	"zcloud-ai/utils/myimg"
)

type artStyleApi struct {
}

type artStyleReq struct {
	ID         uint   `json:"id"`
	Sex        int    `json:"sex"`
	Prompt     string `json:"prompt"`
	OrderIndex int    `json:"order_index"`
	Title      string `json:"title"`
	Remark     string `json:"remark"`
	MainBody   int    `json:"main_body"`
	Path       string `json:"path"`
	IsHot      int    `json:"is_hot"`
	IsDefault  int    `json:"is_default"`
	PriceCoin  int    `json:"price_coin"`
	ModelName  string `json:"model_name"`
	ModelHash  string `json:"model_hash"`
	ArtParm    string `json:"art_parm"`
	OtherParm  string `json:"other_parm"`
}

type artStyleActiveReq struct {
	ID uint `json:"id"`
}

type artStyleListReq struct {
	Sex   int `json:"sex"`
	State int `json:"state"`
}

type artStyleResp struct {
	ID         int             `json:"id"`
	Sex        int             `json:"sex"`
	Prompt     string          `json:"prompt"`
	RefImg     string          `json:"ref_img"`
	RefUrl     string          `json:"ref_url"`
	OrderIndex int             `json:"order_index"`
	Title      string          `json:"title"`
	Remark     string          `json:"remark"`
	MainBody   int             `json:"main_body"`
	IsHot      int             `json:"is_hot"`
	IsDefault  int             `json:"is_default"`
	PriceCoin  int             `json:"price_coin"`
	ModelName  string          `json:"model_name"`
	ModelHash  string          `json:"model_hash"`
	UpdatedAt  global.JsonTime `json:"updated_at"`
	CreatedAt  global.JsonTime `json:"created_at"`
	ArtParm    string          `json:"art_parm"`
	OtherParm  string          `json:"other_parm"`
	Path       string          `json:"path"`
	State      int             `json:"state"`
}

func (obj artStyleApi) Add(c *gin.Context) {
	var code int
	var msg string

	var oReq artStyleReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	o := model.ArtStyle{
		Sex:        oReq.Sex,
		Prompt:     oReq.Prompt,
		OrderIndex: oReq.OrderIndex,
		Title:      oReq.Title,
		Remark:     oReq.Remark,
		MainBody:   oReq.MainBody,
		Path:       oReq.Path,
		IsHot:      oReq.IsHot,
		IsDefault:  oReq.IsDefault,
		ModelName:  oReq.ModelName,
		ModelHash:  oReq.ModelHash,
		ArtParm:    oReq.ArtParm,
		OtherParm:  "{}",
		State:      0,
	}
	msg = "添加成功"
	if oReq.ID > 0 {
		msg = "更新成功"
		o.ID = oReq.ID
		if err := o.Get(); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "风格获取失败")
			return
		}
		logger.Info("ArtStyle更新备份 ID：", o.ID, "  更新前ArtParm：", o.ArtParm)
		o.Prompt = oReq.Prompt
		o.OrderIndex = oReq.OrderIndex
		o.Title = oReq.Title
		o.Remark = oReq.Remark
		o.MainBody = oReq.MainBody
		o.Path = oReq.Path
		o.IsHot = oReq.IsHot
		o.IsDefault = oReq.IsDefault
		o.PriceCoin = oReq.PriceCoin
		o.ModelName = oReq.ModelName
		o.ModelHash = oReq.ModelHash
		o.ArtParm = oReq.ArtParm
		if oReq.OtherParm != "" {
			o.OtherParm = oReq.OtherParm
		}
		//o.State = oReq.State
	}

	if err := o.Save(); err != nil || o.ID == 0 {
		logger.Error("数据保存失败", err, o.ID)
		code = errmsg.FAIL
		msg = "数据保存失败"
		errmsg.Abort(c, code, msg)
		return
	}

	var result artStyleResp
	if err := o.GetForResp(&result); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询数据出错")
		return
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj artStyleApi) Active(c *gin.Context) {
	var code int
	var msg string

	var oReq artStyleActiveReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Alert) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	o := model.ArtStyle{}
	o.ID = oReq.ID

	if err := o.Get(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "风格获取失败")
		return
	}
	if o.State == 0 {
		if err := o.SetState(1); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "风格状态设置失败")
			return
		}
	} else {
		if err := o.SetState(0); err != nil {
			logger.Error(err)
			errmsg.Abort(c, errmsg.FAIL, "风格状态设置失败")
			return
		}
	}

	msg = "状态设置成功"

	var result artStyleResp
	if err := o.GetForResp(&result); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询数据出错")
		return
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"message": msg,
			"msg":     msg,
			"result":  result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj artStyleApi) GetDetail(c *gin.Context) {
	var code int
	var msg string

	var oReq artStyleActiveReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	o := model.ArtStyle{}
	o.ID = oReq.ID

	if err := o.Get(); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "风格获取失败")
		return
	}

	var result artStyleResp
	if err := o.GetForResp(&result); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询数据出错")
		return
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    msg,
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}

}

func (obj artStyleApi) GetList(c *gin.Context) {
	var code int
	var msg string
	var oReq artStyleListReq

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Select) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	err := c.ShouldBindJSON(&oReq)
	if err != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	//oReq.Sex, _ = strconv.Atoi(c.DefaultQuery("sex", "0"))
	//oReq.State, _ = strconv.Atoi(c.DefaultQuery("state", "-1"))
	var ary []artStyleResp
	var o model.ArtStyle
	if err := o.GetListBySex(&ary, oReq.Sex, oReq.State, 1, 100); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询出错")
		return
	}

	//https://aigc.zcloudai.cn/aigc-static/artstyle/112.png
	for idx, val := range ary {
		//data[idx].RefUrl = "static/artstyle/" + val.StyleCode + ".png"
		if len(val.RefImg) > 0 {
			ary[idx].RefUrl = config.Domain + fmt.Sprintf("aigc-static/artstyle/%s?t=%d", val.RefImg, ary[idx].UpdatedAt.Unix())
		}
	}
	//"total":100,"success":true,"pageSize":20,"current":1
	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"total":    len(ary),
			"success":  true,
			"pageSize": 100,
			"current":  1,
			"data":     ary,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

func (obj artStyleApi) Upload(c *gin.Context) {
	var code int

	claims := c.Value("center_claims").(*middleware.CenterClaims)
	if !claims.PermissionEnough(enums.PermissionOperateEnum.Insert) {
		errmsg.Abort(c, errmsg.FAIL, "权限不足")
		return
	}

	valInt64, err := strconv.ParseInt(c.Query("id"), 10, 64)

	artStyle := model.ArtStyle{}
	artStyle.ID = uint(valInt64)
	if err := artStyle.Get(); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "获取风格数据失败")
		return
	}

	f, err := c.FormFile("file")
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "图片获取失败")
		return
	}

	ext := path.Ext(f.Filename) // 输出 .html

	filename := fmt.Sprintf("%d%s", artStyle.ID, strings.ToLower(ext))
	filepath := config.StaticFilePath + "artstyle/" + filename

	tmpFileName := "tmp" + filename
	tmpFilePath := config.StaticFilePath + "artstyle/" + tmpFileName

	if err := c.SaveUploadedFile(f, tmpFilePath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片保存失败")
		return
	}

	img, _, err := myimg.FileToImg(tmpFilePath)
	small := myimg.ResizeImg(350, 455, img, true)
	if err := myimg.ImgToFile(small, filepath); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "缩略图保存失败")
	}

	if err := artStyle.SetRefImg(filename); err != nil {
		logger.Error(err)
		errmsg.Abort(c, errmsg.FAIL, "图片名称更新失败")
		return
	}

	if err := os.Remove(tmpFilePath); err != nil {
		logger.Error(err, tmpFilePath)
	}

	result := make(map[string]interface{})

	result["id"] = artStyle.ID
	result["ref_img"] = filename
	result["ref_url"] = config.Domain + fmt.Sprintf("aigc-static/artstyle/%s?t=%d", artStyle.RefImg, artStyle.UpdatedAt.Unix())

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":   code,
			"msg":    "添加成功",
			"result": result,
		})
	} else {
		errmsg.Abort(c, errmsg.FAIL, "图片上传失败")
	}
}

var ArtStyleApi artStyleApi

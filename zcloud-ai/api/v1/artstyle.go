package v1

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"sort"
	"zcloud-ai/enums"
	"zcloud-ai/model"
	"zcloud-ai/model/req"
	"zcloud-ai/utils"
	"zcloud-ai/utils/config"
	"zcloud-ai/utils/errmsg"
	"zcloud-ai/utils/logger"
)

type artStyleApi struct {
}

func (obj artStyleApi) GetList(c *gin.Context) {
	var code int
	var msg string
	var userEnv model.UserEnv
	userEnvStr := c.Request.Header.Get("User-Env")
	logger.Info("userEnvStr:", userEnvStr)
	if userEnvStr != "" {
		if err := utils.GetStructFromJson(&userEnv, userEnvStr); err != nil {
			logger.Error(err)
		}
	}

	var o model.ArtStyle
	data, total, err := o.GetList(1, 1, 100)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "查询出错")
		return
	}

	//https://aigc.zcloudai.cn/aigc-static/artstyle/112.png
	for idx, val := range data {
		//data[idx].RefUrl = "static/artstyle/" + val.StyleCode + ".png"
		//data[idx].RefUrl = config.Domain + fmt.Sprintf("aigc-static/artstyle/%s.png?t=%d", val.StyleCode, data[idx].UpdatedAt.Unix())
		data[idx].RefUrl = config.Domain + fmt.Sprintf("aigc-static/artstyle/%s?t=%d", val.RefImg, data[idx].UpdatedAt.Unix())
	}
	aryBoy := make([]req.ArtStyleResp, 0)
	aryGirl := make([]req.ArtStyleResp, 0)
	aryOther := make([]req.ArtStyleResp, 0)
	aryHot := make([]req.ArtStyleResp, 0)
	for i := 0; i < len(data); i++ {

		if data[i].MainBody == enums.ArtMainBodyEnum.Module {
			if userEnv.AppVersionCode == 0 {
				continue
			}
		}

		if data[i].IsHot == 1 {
			aryHot = append(aryHot, data[i])
		} else if data[i].Sex == 1 {
			aryBoy = append(aryBoy, data[i])
		} else if data[i].Sex == 2 {
			aryGirl = append(aryGirl, data[i])
		} else if data[i].Sex == 3 {
			aryOther = append(aryOther, data[i])
		} else if data[i].Sex == 4 {
			//aryHot = append(aryHot, data[i])
		}
	}
	sort.SliceStable(aryHot, func(i, j int) bool {
		return aryHot[i].OrderIndex > aryHot[j].OrderIndex
	})

	result := make(map[string]interface{})
	result["girls"] = aryGirl
	result["boys"] = aryBoy
	result["others"] = aryOther
	result["hots"] = aryHot
	result["total"] = total

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"message": "",
			"msg":     "",
			"result":  result,
		})

	} else {
		errmsg.Abort(c, errmsg.FAIL, msg)
	}
}

var ArtStyleApi artStyleApi

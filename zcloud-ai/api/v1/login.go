package v1

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
	"strings"
	"time"
	"zcloud-ai/api/weixin"
	"zcloud-ai/enums"
	"zcloud-ai/middleware"
	"zcloud-ai/model"
	"zcloud-ai/model/req"
	"zcloud-ai/service"
	"zcloud-ai/utils"
	"zcloud-ai/utils/config"
	"zcloud-ai/utils/errmsg"
	"zcloud-ai/utils/logger"
	"zcloud-ai/utils/myredis"
)

// Login 前台登录
func Login(c *gin.Context) {
	var code int
	var msg string
	var token string

	var loginReq req.LoginReq
	var user model.User

	er := c.ShouldBindJSON(&loginReq)
	if er != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if len(loginReq.Username) < 2 {
		errmsg.Abort(c, errmsg.FAIL, "用户名不正确")
		return
	}

	if len(loginReq.Password) < 5 {
		errmsg.Abort(c, errmsg.FAIL, "密码不正确")
		return
	}

	if utils.IsMobile(loginReq.Username) {
		user.GetByMobile(loginReq.Username)
		if user.ID == 0 {
			errmsg.Abort(c, errmsg.FAIL, "用户不存在")
			return
		}
	} else {
		user.GetByUsername(loginReq.Username)
		if user.ID == 0 {
			errmsg.Abort(c, errmsg.FAIL, "用户不存在")
			return
		}
	}

	err := user.CheckPassword(loginReq.Password)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "密码错误")
		return
	}

	token, code, msg = setToken(user)

	////loginResp := req.LoginResp{
	////	UserId:   user.ID,
	////	Username: user.Username,
	////	Token:    token,
	////	Mobile:   utils.FormatMobileStar(user.Mobile),
	////}
	//
	//loginResp := req.LoginResp{
	//	UserId:         user.ID,
	//	Username:       user.Username,
	//	Mobile:         utils.FormatMobileStar(user.Mobile),
	//	InvitationCode: user.InvitationCode,
	//}

	avatarUrl := ""
	if user.Avatar != "" {
		avatarUrl = config.DiffusionDomain + service.ImgData.GetSmallImagePath(user.Avatar)
	}

	toDay := time.Now().Format("2006-01-02")
	ichiMoreDay := user.IchiMore.Format("2006-01-02")
	dailySignDay := user.DailySign.Format("2006-01-02")
	dailyShareDay := user.DailyShare.Format("2006-01-02")

	mobile := user.Mobile
	if mobile == "" && user.PhoneInfo != "" {
		var resp weixin.PhoneNumberResp
		resp.ErrCode = -1
		err := json.Unmarshal([]byte(user.PhoneInfo), &resp)
		if err == nil && resp.ErrCode == 0 {
			mobile = resp.PhoneInfo.PhoneNumber
		}
	}

	resp := req.UserInfoResp{
		UserId:         user.ID,
		Username:       user.Username,
		AvatarUrl:      avatarUrl,
		Mobile:         utils.FormatMobileStar(mobile),
		InvitationCode: user.InvitationCode,
		IsDailySign:    dailySignDay == toDay,
		IsIchiMore:     ichiMoreDay == toDay,
		IsDailyShare:   dailyShareDay == toDay,
		LeaveCoin:      user.Coin - user.FrozenCoin,
	}

	result := make(map[string]interface{})
	result["token"] = token
	result["user"] = resp

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"message": "登录成功",
			"msg":     "登录成功",
			"result":  result,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"result":  nil,
			"message": msg,
			"msg":     msg,
		})
	}
}

func LoginSms(c *gin.Context) {
	var code int
	var msg string

	var loginReq req.LoginSmsReq
	var user model.User

	er := c.ShouldBindJSON(&loginReq)
	if er != nil {
		code = errmsg.FAIL
		msg = "数据获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if !utils.IsMobile(loginReq.Mobile) {
		errmsg.Abort(c, errmsg.FAIL, "手机号码不正确")
		return
	}

	if len(loginReq.SmsCode) != 4 {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	redisKey := enums.RedisKeyEnum.SmsLogin + loginReq.Mobile
	testCount := myredis.Get(redisKey + ":testcount")
	if testCount == "" {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请10分钟后重试")
		return
	}
	iTestCount, _ := strconv.Atoi(testCount)
	if iTestCount > 10 {
		errmsg.Abort(c, errmsg.FAIL, "验证码尝试次数过多，请10分钟后重试")
		return
	}
	iTestCount = iTestCount + 1
	if err := myredis.Set(redisKey+":testcount", strconv.Itoa(iTestCount), time.Minute*10); err != nil {
		errmsg.Abort(c, errmsg.FAIL, "设置短信验证码尝试参数失败")
		return
	}

	v := myredis.Get(enums.RedisKeyEnum.SmsLogin + loginReq.Mobile)
	if len(v) == 0 {
		errmsg.Abort(c, errmsg.FAIL, "验证码已失效，请重试")
		return
	}
	if v != loginReq.SmsCode {
		errmsg.Abort(c, errmsg.FAIL, "短信验证码不正确")
		return
	}

	if v == loginReq.SmsCode {
		//if true {
		if err := user.GetByMobile(loginReq.Mobile); err != nil {
			errmsg.Abort(c, errmsg.FAIL, "查询手机号码出错")
			return
		}
	}

	if user.ID == 0 {

		exists, err := user.ExistsMobile(loginReq.Mobile)
		if err != nil || exists {
			errmsg.Abort(c, errmsg.FAIL, "查询数据出错")
			return
		}
		//
		//errmsg.Abort(c, errmsg.FAIL, "用户不存在,请先注册")
		//return
		//开始注册用户

		//user.Platform = enums.PlatFormEnum.WeiXin
		user.Plat = loginReq.Plat
		if loginReq.InvitedCode != "" {
			user.InvitedCode = loginReq.InvitedCode
		}
		user.Mobile = loginReq.Mobile
		user.PhoneInfo = "{}"
		user.UserSystem = "{}"
		if loginReq.UserSystem != "" {
			userSystemStr := strings.Replace(loginReq.UserSystem, `"lat":"",`, `"lat":0,`, -1)
			userSystemStr = strings.Replace(userSystemStr, `"lat": "",`, `"lng":0,`, -1)
			userSystemStr = strings.Replace(userSystemStr, `"lng":"",`, `"lng":0,`, -1)
			userSystemStr = strings.Replace(userSystemStr, `"lng": "",`, `"lng":0,`, -1)
			logger.Info("替换后userSystem:", userSystemStr)
			var userSystem model.UserEnv
			if err := utils.GetStructFromJson(&userSystem, userSystemStr); err != nil {
				logger.Error(err)
				user.UserSystem = loginReq.UserSystem
			} else {
				userSystem.Ip = utils.GetClientIp(c.Request.Header)
				jsonStr := utils.GetJsonFromStruct(userSystem)
				logger.Info("复制IP userSystem:", jsonStr)
				if jsonStr != "" {
					user.UserSystem = jsonStr
				}
			}
		}
		err = user.Save()
		if err != nil {
			logger.Error(err)
			user.UserSystem = "{}"
			err = user.Save()
		}
		if err != nil || user.ID == 0 {
			errmsg.Abort(c, errmsg.FAIL, "生成记录失败")
			return
		}
		err = service.UserService.NewUserAddCoin(user.ID)
		if err != nil {
			logger.Error("赠送幸运值失败 userId:", user.ID)
		}
	}

	if user.ID == 0 {
		logger.Error("生成用户信息失败")
		errmsg.Abort(c, errmsg.FAIL, "生成用户信息失败")
		return
	}

	//token, code, msg = setToken(user)

	token, err := middleware.NewJWT().SetToken(user.ID, user.Username, user.Mobile)
	if err != nil {
		errmsg.Abort(c, errmsg.FAIL, "生成Token失败")
		return
	}

	//loginResp := req.LoginResp{
	//	UserId:         user.ID,
	//	Username:       user.Username,
	//	Mobile:         utils.FormatMobileStar(user.Mobile),
	//	InvitationCode: user.InvitationCode,
	//}

	avatarUrl := ""
	if user.Avatar != "" {
		avatarUrl = config.DiffusionDomain + service.ImgData.GetSmallImagePath(user.Avatar)
	}

	toDay := time.Now().Format("2006-01-02")
	ichiMoreDay := user.IchiMore.Format("2006-01-02")
	dailySignDay := user.DailySign.Format("2006-01-02")
	dailyShareDay := user.DailyShare.Format("2006-01-02")

	mobile := user.Mobile
	if mobile == "" && user.PhoneInfo != "" {
		var resp weixin.PhoneNumberResp
		resp.ErrCode = -1
		err := json.Unmarshal([]byte(user.PhoneInfo), &resp)
		if err == nil && resp.ErrCode == 0 {
			mobile = resp.PhoneInfo.PhoneNumber
		}
	}

	resp := req.UserInfoResp{
		UserId:         user.ID,
		Username:       user.Username,
		AvatarUrl:      avatarUrl,
		Mobile:         utils.FormatMobileStar(mobile),
		InvitationCode: user.InvitationCode,
		IsDailySign:    dailySignDay == toDay,
		IsIchiMore:     ichiMoreDay == toDay,
		IsDailyShare:   dailyShareDay == toDay,
		LeaveCoin:      user.Coin - user.FrozenCoin,
	}

	result := make(map[string]interface{})
	result["token"] = token
	result["user"] = resp

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"message": "登录成功",
			"msg":     "登录成功",
			"result":  result,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"result":  nil,
			"message": msg,
			"msg":     msg,
		})
	}
}

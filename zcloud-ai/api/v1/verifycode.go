package v1

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"zcloud-ai/utils/errmsg"
	"zcloud-ai/utils/verifycode"
)

func GetVerifyCode(c *gin.Context) {
	var code int
	var msg string

	codeId, base64, er := verifycode.GetNewCode()

	if er != nil {
		code = errmsg.FAIL
		msg = "验证码获取失败"
		errmsg.Abort(c, code, msg)
		return
	}

	if code == errmsg.SUCCESS {
		c.JSON(http.StatusOK, gin.H{
			"code":    code,
			"msg":     "",
			"code_id": codeId,
			"base64":  base64,
		})
	} else {
		c.JSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  msg,
		})
	}

}

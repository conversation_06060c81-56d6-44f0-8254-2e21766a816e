package routes

import (
	"net/http"
	"zcloud-ai/api/alipay"
	"zcloud-ai/api/apple"
	"zcloud-ai/api/auth"
	v1 "zcloud-ai/api/v1"
	"zcloud-ai/api/wechatpay"
	"zcloud-ai/api/weixin"
	"zcloud-ai/enums"
	"zcloud-ai/middleware"
	"zcloud-ai/service"
	"zcloud-ai/utils/config"
	"zcloud-ai/utils/logger"
	"zcloud-ai/utils/myredis"

	"github.com/gin-gonic/gin"
)

func InitRouter() {
	r := gin.New()
	//r.Use(middleware.Log())
	r.Use(middleware.Cors())
	//r.Use(middleware.TimeoutMiddleware(time.Second * 15))

	r.StaticFile("api/testupload", "./chunkfile/testupload.html")
	router := r.Group("api/v1")
	{
		router.GET("hello/:action", func(c *gin.Context) {

			action := c.Param("action")
			if action == "difftest" {

				value := `{"output_relative_path":"20221218/dc66a7babbf35432e16077259a8cc803.png","output_md5":"dc66a7babbf35432e16077259a8cc803","batch_num":1,"out_id":"6"}`

				err := service.DiffusionOut.SaveToImage(value)
				c.JSON(http.StatusOK, gin.H{
					"err": err,
				})
			} else if action == "diffout" {
				value, _ := myredis.BRPop(enums.RedisKeyEnum.DiffusionQueueOut)
				logger.Info("DiffusionQueueOut:", value)
				c.JSON(http.StatusOK, gin.H{
					"DiffusionQueueOut": value,
				})
			} else if action == "accetoken" {
				value := myredis.Get(enums.RedisKeyEnum.WeixinAccessToken)
				logger.Info("AccessToken:", value)
				c.JSON(http.StatusOK, gin.H{
					"WeixinAccessToken": value,
				})
			} else if action == "showaccetoken" {
				c.JSON(http.StatusOK, gin.H{
					"WeixinAccessToken": service.AccessTokenUpdate.GetAccessToken(),
					"WeixinAccess":      service.AccessTokenUpdate,
				})
			} else if action == "DiffusionHashOut" {
				service.DiffusionOutV2.HandleDiffusionHashOut()
				c.JSON(http.StatusOK, gin.H{
					"msg": "DiffusionHashOut complete",
				})
				return
			}
			//time.Sleep(time.Second * 100000000)
			ll := myredis.LLen(enums.RedisKeyEnum.DiffusionQueueOut)

			c.JSON(http.StatusOK, gin.H{
				"msg":   "ok",
				"value": ll,
				"host":  c.Request.Host,
				"path":  c.Request.URL.Path,
			})
		})

		router.POST("getverifycode", v1.GetVerifyCode)
		router.POST("getsmscode", v1.SendSms)

		router.POST("artstyle/list", v1.ArtStyleApi.GetList)
		router.POST("coin/anime_price", v1.CoinApi.GetAnimePriceCoin)

		router.POST("recharge/price_list", v1.RechargeApi.GetPriceList)
		router.POST("conf/get", v1.ConfApi.Get)

		router.POST("anime/get_for_share", v1.AnimeApi.GetForShare)

		router.POST("handraw/get_share_list", v1.HandrawApi.GetShareListFall)
		router.POST("handraw/get_detail", v1.HandrawApi.GetDetail)

		router.GET("anime/small/:id_idx", v1.AnimeApi.GetSmallImage)
		router.GET("anime/normal/:id_idx", v1.AnimeApi.GetNormalImage)
		router.GET("anime/init_img/:n", v1.AnimeApi.GetInitImage)

		router.POST("user/reg", v1.Reg)
		router.POST("user/login", v1.Login)
		router.POST("user/reset_password", v1.UserApi.ChangePasswordByMobile)

		router.POST("user/loginsms", v1.LoginSms)
		router.POST("user/logout", v1.Reg)

		router.POST("weixin/login", weixin.Login)
		router.POST("weixin/notify", weixin.Notify)

		//router.POST("anime/upload_init_image", v1.UploadApi.UploadInitImages)
		//router.POST("anime/upload_init_image", v1.InitImageApi.Upload)
		//router.POST("alipay/payback", alipay.PayBack)
		router.GET("alipay/callback", alipay.Callback)
		router.POST("alipay/notify", alipay.Notify)

		router.POST("wechatpay/notify", wechatpay.Notify)
		router.POST("apple/verify", apple.VerifyReceiptApi.VerifyReceipt)
		router.POST("apple/notify", apple.Notify)

	}

	routerAuth := r.Group("api/v1")
	routerAuth.Use(middleware.JwtToken())
	{
		routerAuth.POST("user/get_info", v1.UserApi.GetUserInfo)
		routerAuth.POST("user/get_coin", v1.CoinApi.Get)

		routerAuth.POST("user/upload_head", v1.UserApi.UploadHeadImg)
		routerAuth.POST("user/change_username", v1.UserApi.ChangeUsername)
		routerAuth.POST("user/change_password", v1.UserApi.ChangePassword)
		routerAuth.POST("user/change_mobile", v1.UserApi.ChangeMobile)

		//routerAuth.POST("anime/upload_init_image", v1.UploadApi.UploadInitImages2)
		routerAuth.POST("anime/upload_init_image", v1.InitImageApi.Upload)
		routerAuth.POST("anime/add", v1.AnimeApi.Add)
		routerAuth.POST("anime/del", v1.AnimeApi.Del)
		routerAuth.POST("anime/get", v1.AnimeApi.Get)
		routerAuth.POST("anime/get_list", v1.AnimeApi.GetList)
		routerAuth.POST("anime/get_images", v1.AnimeApi.GetImages)
		routerAuth.POST("anime/get_small_images", v1.AnimeApi.GetSmallImages)

		routerAuth.GET("anime/small_image/", v1.AnimeApi.GetSmallImage) //这个接口是不是没用的

		routerAuth.POST("anime/get_images_state", v1.AnimeApi.GetImageState)

		routerAuth.POST("outimg/get_state", v1.OutImgApi.GetState)
		routerAuth.POST("outimg/get_guess_list", v1.OutImgApi.GetGuessList)

		routerAuth.POST("handraw/launch_guess", v1.HandrawApi.LaunchGuess)
		routerAuth.POST("handraw/guess", v1.HandrawApi.Guess)

		routerAuth.POST("handraw/add", v1.HandrawApi.Add)
		routerAuth.POST("handraw/get_state", v1.HandrawApi.GetState)
		routerAuth.POST("handraw/get_state_md5", v1.HandrawApi.GetStateByMd5)

		routerAuth.POST("handraw/get_outimg_list", v1.HandrawApi.GetOutImgList)
		routerAuth.POST("handraw/get_publish_list", v1.HandrawApi.GetPublishImgList)

		routerAuth.POST("handraw/publish", v1.HandrawApi.Publish)

		routerAuth.POST("diffimg/maxlevel_list", v1.DiffImgApi.GetMaxLevelByAnime)
		routerAuth.POST("diffimg/upscale_list", v1.DiffImgApi.GetUpScaleList)
		routerAuth.POST("diffimg/upscale", v1.DiffImgApi.UpScale)
		routerAuth.POST("diffimg/del", v1.DiffImgApi.Del)

		routerAuth.POST("coin/add", v1.CoinApi.Add)
		routerAuth.POST("coin/addquick", v1.CoinApi.AddQuick)
		routerAuth.POST("coin/balance", v1.CoinBalanceApi.GetList)
		routerAuth.POST("coin/adreward", v1.CoinApi.AdReward)
		routerAuth.POST("coin/ichimore", v1.CoinApi.IchiMore)
		routerAuth.POST("coin/dailysign", v1.CoinApi.DailySign)
		routerAuth.POST("coin/dailyshare", v1.CoinApi.DailyShare)

		routerAuth.POST("share/invite", v1.ShareApi.Invite)
		routerAuth.POST("share/visit", v1.ShareApi.Visit)

		routerAuth.POST("recharge/add", v1.RechargeApi.Recharge)
		routerAuth.POST("recharge/balance", v1.RechargeApi.GetList)
		routerAuth.POST("recharge/query", v1.RechargeApi.QueryByOutTradeNo)

		routerAuth.POST("weixin/getmobile", weixin.GetMobile)

		routerAuth.POST("sys/txt2img", v1.SysApi.TxtToImg)
		routerAuth.POST("sys/anime2txt", v1.SysApi.AnimeToTxt)
		routerAuth.POST("sys/queue", v1.QueueApi.Push)
		routerAuth.POST("sys/diffout", v1.SysApi.DiffusionOut)
		routerAuth.POST("sys/data", v1.SysApi.GetSysData)
		routerAuth.POST("sys/redocomplete", v1.SysApi.RedoComplete)
		routerAuth.POST("sys/RefreshWeixinAccessToken", v1.SysApi.RefreshWeixinAccessToken) //强制更新
	}

	noneAuthManage := r.Group("api/auth")
	{
		noneAuthManage.POST("login", auth.Login)
	}

	routerAuthManage := r.Group("api/auth")
	routerAuthManage.Use(middleware.JwtTokenCenter())
	{
		routerAuthManage.GET("currentUser", auth.CurrentUser)
		routerAuthManage.POST("artstyle/detail", auth.ArtStyleApi.GetDetail)
		routerAuthManage.POST("artstyle/list", auth.ArtStyleApi.GetList)
		routerAuthManage.POST("artstyle/add", auth.ArtStyleApi.Add)
		routerAuthManage.POST("artstyle/upload", auth.ArtStyleApi.Upload)
		routerAuthManage.POST("artstyle/active", auth.ArtStyleApi.Active)

		routerAuthManage.POST("user/list", auth.UserList)

		routerAuthManage.POST("coin/balance", auth.GetCoinBalanceListByUser)

		routerAuthManage.POST("recharge/list", auth.RechargeList)

		routerAuthManage.POST("anime/list", auth.AnimeApi.GetAnimeList)
		routerAuthManage.POST("anime/get_anime", auth.AnimeApi.GetAnime)
		routerAuthManage.POST("anime/del", auth.AnimeApi.Del)

		routerAuthManage.POST("diffimg/list", auth.DiffImgApi.GetList)
		routerAuthManage.POST("diffimg/waterfall", auth.DiffImgApi.GetWaterfall)
		routerAuthManage.POST("diffimg/del", auth.DiffImgApi.Del)
		routerAuthManage.POST("diffimg/repush", auth.DiffImgApi.Repush)

		routerAuthManage.POST("handraw/list", auth.HandrawApi.GetList)

		routerAuthManage.POST("outimg/list", auth.OutImgApi.GetList)
		routerAuthManage.POST("outimg/waterfall", auth.OutImgApi.GetWaterfall)
		routerAuthManage.POST("outimg/del", auth.OutImgApi.Del)

	}
	logger.Info("开启端口监听...")
	r.Run(config.HttpPort)
}

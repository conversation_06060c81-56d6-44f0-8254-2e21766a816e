package enums

import "reflect"

type aigeRedisKeyEnum_ struct {
	Scribble2imageSubscribe, Scribble2imageOutput, HandrawPush, HandrawPop,
	AnimeIdList, Txt2ImgIdList,
	SdTxt2ImgIn, SdTxt2ImgOut, SdTxt2ImgInLastEmptyTime,
	SdImg2ImgIn, SdImg2ImgOut, SdImg2ImgInLastEmptyTime,
	UpScalingIn, UpScalingOut, UpScalingState string
}

func (c aigeRedisKeyEnum_) Get(id string) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if typeVo.Field(i).Name == id {
			return vo.Field(i).Interface().(string)
		}
	}
	return ""
}

var AigcRedisKeyEnum = aigeRedisKeyEnum_{
	Scribble2imageSubscribe:  "aigc-worker:list:scribble2image_subscribe",
	Scribble2imageOutput:     "aigc-worker:list:scribble2image_output",
	HandrawPush:              "aigc-worker:list:control-net-in",
	HandrawPop:               "aigc-worker:list:control-net-out:handraw",
	AnimeIdList:              "aigc-worker:list:anime-id",
	Txt2ImgIdList:            "aigc-worker:list:txt2img-id",
	SdTxt2ImgIn:              "aigc-worker:list:sd-txt2img-in",
	SdTxt2ImgOut:             "aigc-worker:list:sd-txt2img-out:shenbixiaoai",
	SdTxt2ImgInLastEmptyTime: "aigc-worker:list:sd-txt2img-in:last-empty-time", //SdTxt2ImgIn 队列最后空的时间

	SdImg2ImgIn:              "aigc-worker:list:sd-img2img-in",
	SdImg2ImgOut:             "aigc-worker:list:sd-img2img-out:shenbixiaoai",
	SdImg2ImgInLastEmptyTime: "aigc-worker:list:sd-img2img-in:last-empty-time", //SdImg2ImgIn 队列最后空的时间

	UpScalingIn:    "aigc-worker:list:up-scaling-in",
	UpScalingOut:   "aigc-worker:list:up-scaling-out:shenbixiaoai",
	UpScalingState: "aigc-worker:list:up-scaling-state",
}

package enums

import "reflect"

type handrawStateEnum_ struct { //0初始 1上传成功 2校验失败 3非法图片 4校验通过 5绘画中"`
	Normal, Uploaded, SecFail, SecRisky, SecPass, Drawing, DrawComplete int
}

var HandrawStateEnum = handrawStateEnum_{
	Normal:       0,
	Uploaded:     1,
	SecFail:      2,
	SecRisky:     3,
	SecPass:      4,
	Drawing:      5,
	DrawComplete: 6,
}

func (c handrawStateEnum_) GetKey(value int) string {
	vo := reflect.ValueOf(c)
	typeVo := vo.Type()
	for i := 0; i < vo.NumField(); i++ {
		if vo.Field(i).Interface().(int) == value {
			return typeVo.Field(i).Name
		}
	}
	return ""
}

package enums

type imageTipEnum_ struct {
	NoImage, Drawing, Deleted string
}

var ImageTipEnum = imageTipEnum_{
	NoImage: "aigc-static/images/tip/none.png",    //图片不存在
	Drawing: "aigc-static/images/tip/drawing.png", //正在绘画
	Deleted: "aigc-static/images/tip/deleted.png", //已删除
}

type imgTipEnum_ struct {
	NoImage, Drawing, Deleted string
}

var ImgTipEnum = imgTipEnum_{
	NoImage: "shenbixiaoai/static/images/none.png",    //图片不存在
	Drawing: "shenbixiaoai/static/images/drawing.png", //正在绘画
	Deleted: "shenbixiaoai/static/images/deleted.png", //已删除
}

package main

import (
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"log"
	"net/url"
	"os"
	"path"
	"strings"
	"time"
	"zcloud-ai/enums"
	"zcloud-ai/model"
	"zcloud-ai/service"
	"zcloud-ai/utils/logger"
	"zcloud-ai/utils/myredis"
)

func main23323() {

	//service.ImgData.Del("a.txt")

	ext1 := path.Ext("https://aigc.zcloudai.cn/aigc-static/artstyle/112.png")
	fmt.Println(ext1)

	base1 := path.Base("https://aigc.zcloudai.cn/aigc-static/artstyle/112.png")

	fmt.Println(base1)

	model.InitDb()
	var tmpimg model.TempImg
	tmpimg.GetByMd5("qwe")
	if tmpimg.ID > 0 {
		fmt.Println(tmpimg.ID)
	}

	key1 := enums.PayGatewayEnum.GetKey("page")
	fmt.Println(key1)

	filepath := "/Users/<USER>/Downloads/h5_2/1/2/3/1.txt"
	if _, err := os.Stat(filepath); os.IsNotExist(err) {
		// mkdir 创建目录，mkdirAll 可创建多层级目录
		if err := os.MkdirAll(filepath, os.ModePerm); err != nil {
			logger.Error(err, filepath)
			return
		}
	}

	pathStr := "20221221/2e/2ec7f084289bbce350d667a8393a92c8.png"
	ext := path.Ext(pathStr)
	name := path.Base(pathStr)
	fmt.Println(ext, name, path.Clean(pathStr), path.Dir(pathStr))
	repalce := strings.Replace(pathStr, ext, "_s"+ext, -1)
	fmt.Println(repalce)

	count := 5
	var slice = make([]string, count)
	slice[4] = "adf"
	slice[1] = "aaaa"
	fmt.Println(slice[4], slice[3], slice[1])
	by, err := json.Marshal(slice)
	if err != nil {
		fmt.Println(err)
	}
	value1 := string(by)
	fmt.Println(value1)

	myredis.InitRedis()

	key := "testhash"
	result, err := myredis.HSet(key, "aa", "aavalue")
	fmt.Println(result, err)
	result1, err1 := myredis.HSet(key, "bb", "bbvalue")
	fmt.Println(result1, err1)
	result2, err2 := myredis.HSet(key, "aa", "aavalue1")
	fmt.Println(result2, err2)

	result3, err3 := myredis.HGet(key, "aa")
	fmt.Println(result3, err3)
	result4, err4 := myredis.HGet(key, "aa1")
	if err4 != nil {
		fmt.Println(result4, err4, len(result4))
	}
	if err4.Error() == "redis: nil" {
		fmt.Println(result4, err4, len(result4))
	}

	result5, err5 := myredis.HKeys(key)
	fmt.Println(result5, err5)

	result6, err6 := myredis.HExist(key, "aa1")
	fmt.Println(result6, err6)

	result7, err7 := myredis.HExist(key, "aa")
	fmt.Println(result7, err7)

	urlStr := "https://openapi.alipay.com/gateway.do?app_id=2021001191690325&biz_content=%7B%22out_trade_no%22%3A%22r2022121615331500000029%22%2C%22product_code%22%3A%22QUICK_WAP_WAY%22%2C%22subject%22%3A%22%E6%94%AF%E4%BB%98%E6%B5%8B%E8%AF%95%22%2C%22total_amount%22%3A%220.01%22%7D&charset=utf-8&format=JSON&method=alipay.trade.wap.pay&sign=QjuSgEBe7mVgJQejpDZgYuTzGv5DPJvJIiLkoF2Ql5aPI%2BYi99Ip1FXs5A7T18IVA%2FXk9s9FjyAM5jdIlrMZLplOx6KGkKH81lnHA88BX7dTHbyNjGQnIZTpw2O8SGRc4DP%2Fls%2BPJ4V52zeEvoao25nVqrwAUrSFpE%2FVyujED57AVfgOG1e8SWa480Dxf%2BROKNmApTAL1WRTRe637kf1kTUCTK2r%2FJI8sinR%2BCFY6n8X%2Bf90RzEN%2BMgnIi5C1pj3JSQkeve5oabrdwBEg%2BDAKMxAk6f%2Fn1uGtJwN9LOfIwnM%2BHgE6FY2kvJCy4aiYrQg%2FjAVlso2Q%2FdzGqjs2Azpbg%3D%3D&sign_type=RSA2&timestamp=2022-12-16+15%3A33%3A47&version=1.0"
	u, err := url.Parse(urlStr)
	if err == nil {
		fmt.Println(u) // output: https://root:<EMAIL>:0000/login?name=xiaoming&name=xiaoqing&age=24&age1=23#fffffff
	}
	fmt.Println(u.Query())

	dPrice, _ := decimal.NewFromString("0.01")
	fmt.Println(dPrice.String())

	model.InitDb()

	for i := 0; i < 55; i++ {
		value1, _ := myredis.BRPop(enums.RedisKeyEnum.DiffusionQueue)
		fmt.Println(value1, len(value1))
	}

	_, value, _ := service.Diffusion.GetSong(6)

	for i := 0; i < 50; i++ {
		size, err := myredis.LPush(enums.RedisKeyEnum.DiffusionQueue, value)
		fmt.Println(size)
		if err != nil {
			log.Println(err)
		}
	}
	fmt.Println(myredis.LLen(enums.RedisKeyEnum.DiffusionQueue))

	i := int64(7200)
	j := time.Duration(i) * time.Second
	fmt.Println(j)
	//myredis.Set(enums.RedisKeyEnum.WeixinAccessToken, access.AccessToken, time.Duration(access.ExpiresIn)*time.Nanosecond)

	txt2imgId := service.Diffusion.PopRedisQueue()
	log.Println(txt2imgId)

	i1, err1 := service.Diffusion.PushRedisQueue(167)
	log.Println(i1, err1)
	time.Sleep(5 * time.Second)
	txt2imgId = service.Diffusion.PopRedisQueue()
	log.Println(txt2imgId)

	/*
		i, err := service.AnimeToTxt2img.PushRedisQueue(12332)
		log.Println(i, err)

		animeId := service.AnimeToTxt2img.PopRedisQueue()
		log.Println(animeId)

		value, err := myredis.BRPop(enums.RedisKeyEnum.AnimeToTxt)
		log.Println(value, err)

		value, err = myredis.BRPop(enums.RedisKeyEnum.AnimeToTxt)
		log.Println(value, err)*/
}

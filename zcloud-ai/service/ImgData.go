package service

import (
	"encoding/json"
	"image"
	"os"
	"path"
	"strings"
	"zcloud-ai/utils/config"
	"zcloud-ai/utils/logger"
)

type imgData_ struct {
}

func (imgData *imgData_) DelInitImages(initImages string) error {

	if len(initImages) == 0 {
		return nil
	}
	var aryImages []string
	if err := json.Unmarshal([]byte(initImages), &aryImages); err != nil {
		logger.Error(err)
		return err
	}
	for i := 0; i < len(aryImages); i++ {
		if len(aryImages[i]) > 0 {
			if err := imgData.DelImage(aryImages[i]); err != nil {
				return err
			}
		}
	}
	return nil
}

func (imgData *imgData_) DelImage(relativePath string) error {

	if len(relativePath) == 0 {
		return nil
	}
	absolutePath := config.DiffusionFilePath + relativePath
	if err := os.Remove(absolutePath); err != nil && !os.IsNotExist(err) {
		logger.Error(err, absolutePath)
		return err
	}

	smallPath := imgData.GetSmallImagePath(absolutePath)
	if err := os.Remove(smallPath); err != nil && !os.IsNotExist(err) {
		logger.Error(err, absolutePath)
		return err
	}
	return nil
}

func (imgData *imgData_) DelOutputImages(outputImages string) error {

	if len(outputImages) == 0 {
		return nil
	}
	var aryImages []string
	if err := json.Unmarshal([]byte(outputImages), &aryImages); err != nil {
		logger.Error(err)
		return err
	}
	for i := 0; i < len(aryImages); i++ {
		if len(aryImages[i]) > 0 {
			if err := imgData.DelOutputImage(aryImages[i]); err != nil {
				return err
			}
		}
	}
	return nil
}

func (imgData *imgData_) DelOutputImage(relativePath string) error {

	if len(relativePath) == 0 {
		return nil
	}
	absolutePath := config.DiffusionFilePath + relativePath
	if err := os.Remove(absolutePath); err != nil && !os.IsNotExist(err) {
		logger.Error(err, absolutePath)
		return err
	}

	smallPath := imgData.GetSmallImagePath(absolutePath)
	if err := os.Remove(smallPath); err != nil && !os.IsNotExist(err) {
		logger.Error(err, smallPath)
		return err
	}

	smallJpgPath := imgData.GetSmallJpgImagePath(absolutePath)
	if err := os.Remove(smallJpgPath); err != nil && !os.IsNotExist(err) {
		logger.Error(err, smallJpgPath)
		return err
	}

	normalJpgPath := imgData.GetNormalJpgImagePath(absolutePath)
	if err := os.Remove(normalJpgPath); err != nil && !os.IsNotExist(err) {
		logger.Error(err, normalJpgPath)
		return err
	}

	return nil
}

func (imgData *imgData_) DelUpScaleImage(relativePath string) error {
	if len(relativePath) == 0 {
		return nil
	}
	absolutePath := config.DiffusionFilePath + relativePath
	if err := os.Remove(absolutePath); err != nil && !os.IsNotExist(err) {
		logger.Error(err, absolutePath)
		return err
	}
	return nil
}

func (imgData *imgData_) GetSmallImagePath(pathStr string) string {
	//pathStr := "20221221/2e/2ec7f084289bbce350d667a8393a92c8.png"
	ext := path.Ext(pathStr)
	smallPathStr := strings.Replace(pathStr, ext, "_s"+ext, -1)
	return smallPathStr
}
func (imgData *imgData_) GetSmallJpgImagePath(pathStr string) string {
	//pathStr := "20221221/2e/2ec7f084289bbce350d667a8393a92c8.png"
	ext := path.Ext(pathStr)
	smallPathStr := strings.Replace(pathStr, ext, "_s.jpg", -1)
	return smallPathStr
}
func (imgData *imgData_) GetNormalJpgImagePath(pathStr string) string {
	//pathStr := "20221221/2e/2ec7f084289bbce350d667a8393a92c8.png"
	ext := path.Ext(pathStr)
	smallPathStr := strings.Replace(pathStr, ext, ".jpg", -1)
	return smallPathStr
}

func (imgData *imgData_) GetAbsoluteImagePath(relativePath string) string {
	//pathStr := "20221221/2e/2ec7f084289bbce350d667a8393a92c8.png"
	absolutePath := config.DiffusionFilePath + relativePath
	return absolutePath
}

func (imgData *imgData_) GetImageSize(path string) (int, int, error) {
	file, err := os.Open(path)
	if err != nil {
		logger.Error("Error opening file:", err)
		return 0, 0, err
	}
	defer file.Close()

	img, _, err := image.Decode(file)
	if err != nil {
		logger.Error("Error decoding image:", err)
		return 0, 0, err
	}

	bounds := img.Bounds()
	width := bounds.Max.X - bounds.Min.X
	height := bounds.Max.Y - bounds.Min.Y
	return width, height, nil
}

var ImgData imgData_

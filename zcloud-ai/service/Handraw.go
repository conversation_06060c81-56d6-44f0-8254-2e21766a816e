package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"unicode/utf8"
	"zcloud-ai/enums"
	"zcloud-ai/model"
	"zcloud-ai/utils/logger"
	"zcloud-ai/utils/myredis"
)

type handraw_ struct {
}

//type inputData struct {
//	InputImagePath  string  `json:"input_image_path"`
//	CustomId        string  `json:"custom_id"`
//	CustomData      string  `json:"custom_data"`
//	TaskMd5         string  `json:"task_md5"`
//	Prompt          string  `json:"prompt"`
//	APrompt         string  `json:"a_prompt"`
//	NPrompt         string  `json:"n_prompt"`
//	NumSamples      int     `json:"num_samples"`
//	ImageResolution int     `json:"image_resolution"`
//	DdimSteps       int     `json:"ddim_steps"`
//	Scale           float32 `json:"scale"`
//	Seed            int     `json:"seed"`
//	Eta             float32 `json:"eta"`
//}

type Unit struct {
	InputImagePath string `json:"input_image_path"`
	Module         string `json:"module"`
	Weight         int    `json:"weight"`
}

type inputData struct {
	CustomApp      string  `json:"custom_app"`
	CustomId       string  `json:"custom_id"`
	CustomData     string  `json:"custom_data"`
	CustomMd5      string  `json:"custom_md5"`
	CustomPath     string  `json:"custom_path"`
	ModelName      string  `json:"model_name"`
	ModelHash      string  `json:"model_hash"`
	CkptName       string  `json:"ckpt_name"`
	Prompt         string  `json:"prompt"`
	NegativePrompt string  `json:"negative_prompt"`
	SamplerName    string  `json:"sampler_name"`
	Width          int     `json:"width"`
	Height         int     `json:"height"`
	Steps          int     `json:"steps"`
	CfgScale       float32 `json:"cfg_scale"`
	Seed           int     `json:"seed"`
	Eta            float32 `json:"eta"`
	Units          []Unit  `json:"units"`
}

type outputData_ struct {
	CustomApp   string `json:"custom_app"`
	SisPath     string `json:"sis_path"`
	SisMd5      string `json:"sis_md5"`
	CustomId    string `json:"custom_id"`
	CustomData  string `json:"custom_data"`
	ExecuteTime int    `json:"execute_time"`
}

type handrawCustomData_ struct {
	HandId uint   `json:"hand_id"`
	Md5    string `json:"md5"`
}

func (d *handraw_) Run() {

	defer func() {
		if e := recover(); e != nil {
			logger.Error("Handraw奔溃:", e)
		}
	}()
	logger.Info("Handraw.Run 开始循环获取")
	for {
		value, err := d.PopRedisQueue()

		if err != nil {
			logger.Error(value, err)
			continue
		}
		if value == "" {
			continue
		}
		logger.Info("Handraw json:", value)
		if err := d.HandleDraw(value); err != nil {
			logger.Error(err)
		}
	}
}

func (d *handraw_) HandleDraw(value string) error {
	outputData := outputData_{}
	customData := handrawCustomData_{}
	if value == "" {
		logger.Error("数据为空")
		return errors.New("数据为空")
	}

	if err := json.Unmarshal([]byte(value), &outputData); err != nil {
		logger.Error(err)
		return err
	}

	if err := json.Unmarshal([]byte(outputData.CustomData), &customData); err != nil {
		logger.Error(err)
		return err
	}

	if customData.Md5 == "" {
		id, err := strconv.ParseUint(outputData.CustomId, 10, 64)
		if err != nil {
			logger.Error(err)
			return err
		}

		var handraw model.Handraw
		if err := handraw.GetById(uint(id)); err != nil {
			logger.Error(err)
			return err
		}

		if err := handraw.SetOutputImg(outputData.SisPath); err != nil {
			logger.Error(err)
			return err
		}

	} else {
		var outImg model.OutImg
		if err := outImg.GetByMd5(customData.Md5); err != nil {
			logger.Error(err)
			return err
		} else {
			if outImg.Path != "" {
				err := errors.New("path字段不为空")
				logger.Error(err, customData.Md5)
				return err
			}
			if err := outImg.SetPath(outputData.SisPath); err != nil {
				logger.Error(err)
				return err
			}
		}
	}

	return nil
}
func (d *handraw_) GetPushJson(id uint) string {
	var handraw model.Handraw
	if err := handraw.GetById(id); err != nil {
		logger.Error(err)
		return ""
	}

	transPrompt := ""
	if len(handraw.Prompt) > 0 {
		transPrompt = TencentCloud.TextTranslate(handraw.Prompt)
		if utf8.RuneCountInString(transPrompt) == 0 || utf8.RuneCountInString(transPrompt) < utf8.RuneCountInString(handraw.Prompt) {
			logger.Error("翻译失效 ", handraw.ID, transPrompt, handraw.Prompt)
			transPrompt = handraw.Prompt
		}
	}

	units := []Unit{
		{
			InputImagePath: handraw.InputImgPath,
			Module:         "scribble",
			Weight:         1,
		},
	}
	customData := fmt.Sprintf(`{"id":%d}`, handraw.ID)
	customMd5 := handraw.OutputImgMd5
	if handraw.BatchCount > 0 {
		customMd5 = "{{CustomMd5}}"
		customData = fmt.Sprintf(`{"hand_id":%d,"md5":"{{CustomMd5}}"}`, handraw.ID)
	}

	data := inputData{
		CustomApp:      "handraw",
		CustomId:       strconv.Itoa(int(handraw.ID)),
		CustomData:     customData,
		CustomMd5:      customMd5,
		ModelName:      "huggingface/dreamlike-photoreal-2.0.ckpt",
		ModelHash:      "fc52756a74",
		CkptName:       "huggingface/dreamlike-photoreal-2.0.ckpt [fc52756a74]",
		Prompt:         transPrompt,
		NegativePrompt: "longbody, lowres, bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality",
		SamplerName:    "Euler a",
		Width:          512,
		Height:         512,
		Steps:          20,
		CfgScale:       7.0,
		Seed:           -1,
		Eta:            0.0,
		Units:          units,
	}
	//20240412/handraw/01510bffcf0412dfd40ed442d95be28e.png
	//		song["custom_path"] = fmt.Sprintf("%s/{==={CustomMd5_Pre2}===}/{==={CustomMd5}===}.png", o.CreatedAt.Format("20060102"))
	//data.CustomPath = fmt.Sprintf("%s/{==={CustomMd5_Pre2}===}/{==={CustomMd5}===}.png", o.CreatedAt.Format("20060102"))

	jsonByte, err := json.Marshal(data)
	if err != nil {
		logger.Error(err)
		return ""
	}
	return string(jsonByte)
}

func (d *handraw_) GetPushTemplate(id uint) string {
	var handraw model.Handraw
	if err := handraw.GetById(id); err != nil {
		logger.Error(err)
		return ""
	}

	units := []Unit{
		{
			InputImagePath: handraw.InputImgPath,
			Module:         "scribble",
			Weight:         1,
		},
	}
	customData := fmt.Sprintf(`{"id":%d,"md5":"{==={CustomMd5}===}","from":"guess"}`, handraw.ID)
	data := inputData{
		CustomApp:      "handraw",
		CustomId:       strconv.Itoa(int(handraw.ID)),
		CustomData:     customData,
		CustomMd5:      "{==={CustomMd5}===}",
		ModelName:      "huggingface/dreamlike-photoreal-2.0.ckpt",
		ModelHash:      "fc52756a74",
		CkptName:       "huggingface/dreamlike-photoreal-2.0.ckpt [fc52756a74]",
		Prompt:         "{==={Prompt}===}",
		NegativePrompt: "longbody, lowres, bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality",
		SamplerName:    "Euler a",
		Width:          512,
		Height:         512,
		Steps:          20,
		CfgScale:       7.0,
		Seed:           -1,
		Eta:            0.0,
		Units:          units,
	}

	jsonByte, err := json.Marshal(data)
	if err != nil {
		logger.Error(err)
		return ""
	}
	return string(jsonByte)
}

func (d *handraw_) PopRedisQueue() (string, error) {
	//value, err := myredis.BRPop(enums.AigcRedisKeyEnum.Scribble2imageOutput)
	value, err := myredis.BRPop(enums.AigcRedisKeyEnum.HandrawPop)
	return value, err
}

func (o *handraw_) PushRedisQueue(value string) (int64, error) {
	//size, err := myredis.LPush(enums.AigcRedisKeyEnum.Scribble2imageSubscribe, value)
	//size, err := myredis.Publish(enums.AigcRedisKeyEnum.Scribble2imageSubscribe, value)
	//size, err := myredis.LPush(enums.AigcRedisKeyEnum.HandrawPush, value)

	if strings.Contains(value, "{{CustomMd5}}") {
		logger.Error("存在未替换的字符串", value)
		return 0, errors.New("存在未替换的字符串")
	}
	if strings.Contains(value, "{==={") || strings.Contains(value, "}===}") {
		logger.Error("存在未替换的字符串", value)
		return 0, errors.New("存在未替换的字符串")
	}

	size, err := myredis.RPush(enums.AigcRedisKeyEnum.HandrawPush, value)
	if err != nil {
		logger.Error(err)
		return size, err
	}
	return size, err
}

var HandrawService handraw_
